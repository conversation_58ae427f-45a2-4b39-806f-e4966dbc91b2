import defaultTheme from 'tailwindcss/defaultTheme';
import plugin from 'tailwindcss/plugin';
import typographyPlugin from '@tailwindcss/typography';

export default {
  content: ['./src/**/*.{astro,html,js,jsx,json,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      maxWidth: {
        'global': '1100px',
      },
      colors: {
        primary: 'var(--aw-color-primary)',
        brand: 'var(--aw-color-brand)',
        secondary: 'var(--aw-color-secondary)',
        accent: 'var(--aw-color-accent)',
        default: 'var(--aw-color-text-default)',
        muted: 'var(--aw-color-text-muted)',
        heading: 'var(--aw-color-text-heading)',
        'bg-page': 'var(--aw-color-bg-page)',
        'bg-section': 'var(--aw-color-bg-section)',
        'bg-card': 'var(--aw-color-bg-card)',
        'bg-form': 'var(--aw-color-bg-form)',
        'bg-input': 'var(--aw-color-bg-input)',
        'bg-muted': 'var(--aw-color-bg-muted)',
        'kategori-hover': '#E8E1D8',
      },
      fontFamily: {
        sans: ['var(--aw-font-sans, ui-sans-serif)', ...defaultTheme.fontFamily.sans],
        serif: ['var(--aw-font-serif, ui-serif)', ...defaultTheme.fontFamily.serif],
        heading: ['var(--aw-font-heading, ui-sans-serif)', ...defaultTheme.fontFamily.sans],
      },

      animation: {
        fade: 'fadeInUp 1s both',
      },

      keyframes: {
        fadeInUp: {
          '0%': { opacity: 0, transform: 'translateY(2rem)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            h2: {
              borderTopWidth: '4px',
              borderColor: theme('colors.primary'),
              paddingTop: '0.5rem',
              marginTop: '2em',
              marginBottom: '1em',
              fontSize: theme('fontSize.xl'),
            },
          },
        },
      }),
    },
  },
  plugins: [
    typographyPlugin,
    plugin(({ addVariant }) => {
      addVariant('intersect', '&:not([no-intersect])');
    }),
  ],
  darkMode: 'class',
};
