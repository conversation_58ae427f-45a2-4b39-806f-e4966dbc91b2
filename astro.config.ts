import path from 'path';
import { fileURLToPath } from 'url';

import { defineConfig } from 'astro/config';

import sitemap from '@astrojs/sitemap';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import partytown from '@astrojs/partytown';
import preact from '@astrojs/preact';
import icon from 'astro-icon';
import compress from 'astro-compress';
import type { AstroIntegration } from 'astro';

import astrowind from './vendor/integration';

import { responsiveTablesRehypePlugin, lazyImagesRehypePlugin } from './src/utils/frontmatter';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const hasExternalScripts = false;
const whenExternalScripts = (items: (() => AstroIntegration) | (() => AstroIntegration)[] = []) =>
  hasExternalScripts ? (Array.isArray(items) ? items.map((item) => item()) : [items()]) : [];

export default defineConfig({
  output: 'static',
  trailingSlash: 'always',

  integrations: [
    tailwind({
      applyBaseStyles: false,
    }),
    sitemap({
      filter: (page) => {
        // Exclude blog pagination pages (page 2, 3, etc.) from sitemap
        const blogPaginationRegex = /\/blog\/\d+\/$/;
        
        // Exclude tag pages from sitemap (both main tag pages and pagination)
        const tagPagesRegex = /\/tag\/[^/]+\/?(\d+\/?)?$/;
        
        // Exclude directory pagination pages from sitemap
        const directoryPaginationRegex = /\/direktori\/[^/]+\/page\/\d+\/$/;
        
        // Exclude location pagination pages from sitemap
        const locationPaginationRegex = /\/direktori\/lokasi\/[^/]+\/page\/\d+\/$/;
        
        return !blogPaginationRegex.test(page) &&
               !tagPagesRegex.test(page) &&
               !directoryPaginationRegex.test(page) &&
               !locationPaginationRegex.test(page);
      },
    }),
    mdx(),
    icon({
      include: {
        tabler: ['*'],
        'flat-color-icons': [
          'template',
          'gallery',
          'approval',
          'document',
          'advertising',
          'currency-exchange',
          'voice-presentation',
          'business-contact',
          'database',
        ],
      },
    }),
    preact(),

    ...whenExternalScripts(() =>
      partytown({
        config: { forward: ['dataLayer.push'] },
      })
    ),

    compress({
      CSS: true,
      HTML: {
        'html-minifier-terser': {
          removeAttributeQuotes: false,
        },
      },
      Image: false,
      JavaScript: true,
      SVG: false,
      Logger: 1,
    }),

    astrowind({
      config: './src/config.yaml',
    }),
  ],

  image: {
    domains: ['img.penasihathosting.com', 'cdn.penasihathosting.com'],
  },

  markdown: {
    remarkPlugins: [],
    rehypePlugins: [responsiveTablesRehypePlugin, lazyImagesRehypePlugin],
  },

  vite: {
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './src'),
      },
    },
    server: {
      fs: {
        allow: ['..'],
      },
    },
  },
});
