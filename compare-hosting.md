# Rencana Fitur: Perbandingan Hosting (Hosting Comparison)

## 1. <PERSON><PERSON><PERSON>mungkinkan pengguna memilih beberapa provider (misal 2–4) dari listing kategori dan membuka tampilan perbandingan yang menampilkan metrik/faktor penting secara berdampingan untuk membantu keputusan.

## 2. <PERSON><PERSON><PERSON>
- User yang sudah menyempitkan pilihan ke beberapa provider.
- User yang ingin validasi cepat (harga awal, fitur kunci, lokasi DC, promo, tier/badges, support).

## 3. <PERSON><PERSON><PERSON> (MVP)
| Aspek | MVP | Future Iteration |
|-------|-----|------------------|
| Jumlah provider | 2–4 | Dinamis (scrollable) |
| Sumber data | Sudah ada di collection `hosting-providers` | Integrasi API eksternal performance / uptime |
| Penyimpanan pilihan | localStorage | Sync account (jika ada auth) |
| UI compare | Halaman / overlay dedicated | Side-by-side sticky mini panel |
| Sharing | URL dengan query params slug | Shortlink + export image / PDF |
| Metrics | Harga awal, promo, fitur list, datacenter flags, support channel, badges, tier, control panel | Benchmark speed, uptime, rating user |

## 4. Flow Pengguna
1. User berada di halaman kategori (provider list). 
2. Tiap card provider menampilkan checkbox / tombol “Compare”.
3. User klik (status: selected). Panel mini (floating / sticky di bawah) muncul setelah ada >=1 pilihan.
4. Panel menampilkan: daftar slug + tombol “Bandingkan Sekarang” (disabled jika <2). Juga tombol reset.
5. User klik “Bandingkan Sekarang” -> Navigasi ke `/compare/?providers=slug1,slug2,slug3`.
6. Halaman compare render tabel perbandingan.
7. User bisa hapus salah satu provider dari halaman compare (update URL & localStorage).

## 5. Komponen Baru
| Komponen | Deskripsi |
|----------|-----------|
| `CompareToggle` (atom) | Checkbox / button stateful per provider card. |
| `CompareBar` (molecule) | Sticky bottom bar (mobile & desktop) menampilkan pilihan & CTA Compare. |
| `CompareContext` / store | Menyimpan state terpilih (maks 4). |
| `CompareTable` | Tabel perbandingan side-by-side. |
| `CompareFeatureList` | Normalisasi fitur (opsional). |
| `CompareBadge` | Render tier/badge ringkas. |

## 6. Data & Normalisasi
Ambil dari collection:
- `name`, `slug`, `logo`, `pricing.startingPrice`, `pricing.promoDescription`, `pricing.promoCode`
- `features` (maks 6–8 pertama) – bisa difilter nanti.
- `datacenters` (render flag + count)
- `company.founded`, `company.headquarters[0]`
- `supportChannels` (ikon mapping)
- `controlPanels`

Tambahkan util baru: `src/utils/compare.ts`:
```ts
export function pickComparableFields(provider) { /* return object ringkas */ }
export function formatSupportChannels(channels: string[]): {key: string; label: string; icon: string}[] {}
```

## 7. Penyimpanan State
Prioritas: ephemeral + localStorage.
```ts
// kunci
LOCAL_STORAGE_KEY = 'ph_compare_providers_v1';
// struktur
{
  items: ["domainesia","idcloudhost"],
  updatedAt: 1733699000000
}
```
Saat load halaman kategori:
1. Inisialisasi store dari localStorage.
2. Sinkronkan highlight compare toggle.

## 8. State Management Options
- Ringan: gunakan "islands" / partial hydration Astro + Preact signal / nano store (misal `nanostores`).
- MVP: inline script modul + dataset + custom events (menghindari framework penuh).

Rekomendasi: Preact island karena interaksi >1 komponen.

## 9. API URL Compare
Format: `/compare/?providers=slug1,slug2,slug3`
- Validasi: hapus slug tidak ditemukan / duplikat.
- Jika <2 redirect kembali ke referer (kategori) dengan notice.
- Jika >4 trim ke 4 pertama.

## 10. Halaman Compare Baru
Buat `src/pages/compare/index.astro`.
Langkah rendering:
1. Parse query `providers`.
2. Fetch with `getCollection('hosting-providers')` filter by slug.
3. Susun array terurut berdasarkan urutan query.
4. Kirim ke komponen `CompareTable.astro`.

## 11. Layout CompareTable (Desktop)
```
| Metric / Field        | Provider A | Provider B | Provider C | Provider D |
| Logo + Name + Badge   |            |            |            |            |
| Harga Awal            |            |            |            |            |
| Promo                 |            |            |            |            |
| Control Panel         |            |            |            |            |
| Fitur Utama (bullet)  | list       | list       | list       | list       |
| Data Center           | flags      | flags      | flags      | flags      |
| Lokasi HQ             |            |            |            |            |
| Tahun Berdiri         |            |            |            |            |
| Support Channel       | ikon grid  | ikon grid  | ikon grid  | ikon grid  |
| Tombol Kunjungi       | CTA        | CTA        | CTA        | CTA        |
| Hapus dari Compare    | X          | X          | X          | X          |
```
Mobile pattern: horizontal scroll container (CSS snap) tiap kolom provider + kolom pertama (metrics) sticky kiri (optional tahap 2).

## 12. Interaksi
| Aksi | Respon |
|------|--------|
| Klik Compare (provider belum dipilih) | Tambah ke state; tampilkan bar; animasi highlight. |
| Klik Compare (sudah 4) | Tampilkan toast: "Maksimal 4 provider". |
| Klik Compare (sudah dipilih) | Remove dari state. |
| Klik CTA Compare | Navigate ke halaman perbandingan. |
| Hapus di CompareTable | Update URL, update localStorage, re-render. |
| Reload halaman kategori | State restored. |

## 13. Komponen UI Detail
### CompareToggle
- Bentuk: checkbox kecil + label "Bandingkan" / ikon scale.
- Variasi aktif: filled + check.
- Aksesibilitas: `aria-pressed` / `role="button"` + label.

### CompareBar
- Sticky bottom (desktop & mobile).
- Menampilkan badge provider (logo kecil + X).
- CTA: "Bandingkan (3)".
- Tombol Reset.

### CompareTable
- Grid responsive.
- Baris zebra optional.
- Icon mapping (support & control panel).
- Fitur list: bullet trimmed length + tooltip full.

## 14. Aksesibilitas
- Fokus ring jelas pada toggle dan tombol hapus.
- `aria-label` untuk ikon.
- Tabel gunakan `<table>` semantik (desktop) untuk screen reader.
- Mobile: jika gunakan div grid, tambahkan `role="table"` dsb.

## 15. Performance & Optimasi
- Lazy load halaman compare (separate route). 
- Hindari memuat gambar logo ukuran besar (pakai ukuran kecil / width attr). 
- Batasi fitur list (misal 6) untuk menjaga tinggi sel.

## 16. Tracking (Opsional)
Event yang bisa dicatat:
- `compare_add_provider` (slug, total_selected)
- `compare_remove_provider` (slug, total_selected)
- `compare_open_page` (count)
- `compare_click_affiliate` (slug, position_in_compare)

## 17. Edge Cases
| Kasus | Penanganan |
|-------|------------|
| User buka /compare tanpa query | Redirect ke /direktori-hosting/ + flash message. |
| Query slug tidak valid | Dihilangkan diam-diam; jika sisa <2 redirect. |
| Provider isActive = false | Disembunyikan & di-drop dari compare. |
| User hapus sampai sisa 1 | Tampilkan notice: "Tambahkan minimal 1 lagi." + disable beberapa aksi. |

## 18. Styling / Tailwind Guidelines
- Gunakan utility konsisten (shadow-sm, rounded-md, border-gray-200). 
- Warna highlight per-badge (sudah ada di ProviderList) bisa reuse.
- Gunakan `grid-cols-[180px_repeat(n,minmax(180px,1fr))]` (CSS variable untuk n?).

## 19. Tahapan Implementasi
| Tahap | Deskripsi | Output |
|-------|-----------|--------|
| 1 | Setup util + context/store + toggle sederhana | Toggle bekerja, state di console/localStorage |
| 2 | CompareBar + limit 4 + CTA navigate | UX dasar selesai |
| 3 | Halaman /compare + fetch & render tabel minimal (nama & harga) | Skeleton compare |
| 4 | Lengkapi semua baris: fitur, DC, support, promo, badges | Tabel kaya |
| 5 | Mobile horizontal scroll improvement | UX mobile |
| 6 | Remove item + sync URL + toast | Interaksi lengkap |
| 7 | Aksesibilitas & polishing (hover/focus, loading states) | Final MVP |
| 8 (opsional) | Shareable link copy, analytics events | Enhancement |

## 20. Risiko & Mitigasi
| Risiko | Mitigasi |
|--------|----------|
| State mismatch (URL vs localStorage) | Saat load /compare prioritas URL; sinkronkan ke localStorage. |
| Performance table lebar ( >4 ) | Batasi maks 4; scroll pattern untuk future. |
| Fitur list panjang membuat sel tinggi | Hard limit + tooltip. |
| Preact island overhead | Hanya load pada halaman kategori (conditional). |

## 21. To-Do Checklist (Actionable)
- [ ] Buat util `compareStore` (Preact signals / nanostores).
- [ ] Implement `CompareToggle.astro` (island) + integrasi di `ProviderList`.
- [ ] Buat `CompareBar.astro` (island) ditempatkan di bawah `<ProviderList />` atau global.
- [ ] Persist & hydrate dari localStorage.
- [ ] Route `/compare/index.astro` + parsing query.
- [ ] Komponen `CompareTable.astro`.
- [ ] Normalisasi mapping ikon support + control panel.
- [ ] Tambah toast util sederhana (optional).
- [ ] QA desktop + mobile.
- [ ] A11y audit cepat.

## 22. Ekstensi Masa Depan
- Tambah kategori metrik performa (benchmark internal).
- Integrasi rating user / review snippet.
- Export CSV / PDF / gambar.
- Mode highlight per kolom (best value per baris).
- Fitur "Suggest better alternative".

---
Draft oleh: (isi nama / tanggal)
Status: Draft Perencanaan
