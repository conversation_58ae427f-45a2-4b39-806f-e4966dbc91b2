{"css.customData": ["./vscode.tailwind.json"], "eslint.validate": ["javascript", "javascriptreact", "astro", "typescript", "typescriptreact"], "files.associations": {"*.mdx": "markdown"}, "prettier.documentSelectors": ["**/*.astro"], "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "yaml.schemas": {"./.vscode/astrowind/config-schema.json": "/src/config.yaml"}, "eslint.useFlatConfig": true, "zencoder.enableRepoIndexing": true}