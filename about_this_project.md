# About This Project: PenasihatHosting

Comprehensive technical and editorial documentation for the PenasihatHosting codebase. Use this as your single source of truth for stack, structure, content systems, provider database, navigation, CI, and workflows.

## Overview

PenasihatHosting is an Indonesian web hosting review, comparison, and education site built with Astro 5.11.1 and Tailwind CSS, based on the AstroWind template. It provides:
- Independent reviews and comparisons of hosting providers (Indonesia-focused)
- A structured directory of providers (by categories and locations)
- Educational content (blog, wiki, dictionary)
- Practical tools (generators, formatters, calculators)

Primary language: Indonesian (id). Currency: IDR.

## Quick Start

- Install dependencies: `npm install`
- Dev server: `npm run dev` → http://localhost:4321
- Production build: `npm run build` → outputs to `./dist/`
- Preview build: `npm run preview`

Quality & maintenance:
- All checks: `npm run check` (Astro + ESLint + Prettier)
- Individual: `npm run check:astro`, `npm run check:eslint`, `npm run check:prettier`
- Fixes: `npm run fix`, `npm run fix:eslint`, `npm run fix:prettier`

Node engines: >=18.20.8, ^20.3.0, or >=22.0.0

## Key Files & Config

- astro.config.ts: Astro integrations and site-level config
- src/config.yaml: Site metadata, SEO, and app settings consumed by the custom integration
- src/navigation.ts: Header and footer structures
- tailwind.config.js: Tailwind CSS
- package.json: dependencies and scripts
- tsconfig.json: TypeScript configuration

Important astro.config.ts highlights:
- output: static, trailingSlash: always
- Integrations: tailwind, sitemap (with custom filters), mdx, astro-icon, preact, optional partytown, astro-compress, custom astrowind vendor integration
- Image domains allowlist: img.penasihathosting.com, cdn.penasihathosting.com
- Markdown rehype plugins: responsive tables, lazy images (from src/utils/frontmatter)
- Vite alias: ~ → ./src

## Repository Structure

- src/assets/ — images, favicons, styles
- src/components/ — Astro/TSX components, grouped by domain (blog, widgets, ui, common, home, alat, direktori)
- src/layouts/ — site layouts (Layout.astro, MarkdownLayout.astro, PageLayout.astro, WikiHostingLayout.astro)
- src/pages/ — route files (static pages, tools, directory routes, dynamic routes)
- src/content/ — content collections
- src/utils/ — utilities for blog, guides, compare, permalinks, images, search, directories, locations, etc.
- public/ — static files (robots.txt, decap CMS files, headers)
- vendor/ — custom AstroWind integration (in progress)

## Content Collections (src/content/config.ts)

- post (MD/MDX in src/data/post): publish/update dates, category/tags, SEO metadata
- guide (data): multi-chapter guide definitions (see cpanel.yaml, web-hosting.yaml)
- kamus (MD/MDX in src/content/kamus): dictionary entries with SEO and dates
- wiki (MD/MDX in src/content/wiki): lightweight wiki pages
- wikihosting (MD/MDX in src/content/wikihosting): comprehensive articles with advanced schema
  - category, phase, priority, difficulty, readingTime, targetKeywords, relatedTerms, tags, author, schema types, optional FAQ
- hosting-providers (data JSON): standardized provider database (see below)
- hosting-categories (MD/MDX): title, description, icon, SEO fields, featured flag, providerCount

## Provider Database Schema (hosting-providers)

Each provider JSON includes:
- Basic: name, slug, displayName?, logo, website, description, longDescription?
- Company: name, founded?, headquarters[], address, supportChannels[] (live-chat, ticket, phone, email, documentation, community, whatsapp)
- Categorization: categories[], controlPanels? (cpanel, directadmin, plesk), badges[] ({type, label})
- Features & Infra: features[] ({name, icon?, description?}), datacenters[] ({location, country, flag?})
- Pricing: startingPrice, currency (default IDR), plans[] ({name, price, period, features[], isPopular?, promoCode?, promoDescription?}), promoCode?, promoDescription?
- Media: gallery[], featuredImage?
- Business: tier (basic | verified | sponsored | recommended), affiliateLink?
- Meta: isActive, isFeatured, createdAt?, updatedAt?, modifiedAt?

Typical counts: 40+ providers with Indonesia/Singapore/US datacenters. Used across directory pages and comparison components.

## Directory System (Direktori Hosting)

- Category routes: /direktori/[category]/ (+ pagination at /direktori/[category]/page/[page]/)
- Location routes: /direktori/lokasi/[location]/ (+ pagination)
- All providers: /direktori-hosting/
- Components: src/components/direktori/common/* and /hosting/* (cards, gallery modal, sticky sidebar, promo copy, search box, sponsored areas, etc.)
- Utilities: src/utils/locationUtils.ts for slugging, filtering, and static path generation

Provider detail pages feature:
- ImageModal (click-to-zoom)
- Simplified pricing sidebar with promo code copy
- Sponsored badges and styling variants by tier

## Reviews & Recommendations

- Home review tiles populated via src/components/home/<USER>/data/* with types in types.ts
- Long-form reviews in src/data/post (review-*.mdx)
- Recommendations in src/content/recommendations/* (managed WordPress, VPS, cloud panels, shared hosting, etc.)

## Blog & Permalinks

- Blog content: src/data/post/*.mdx
- Permalinks via src/utils/permalinks.ts (configured with patterns like /%slug%)
- Post processing utilities in src/utils/blog.ts (normalization, related posts)

## Tools (Alat)

Available as pages under src/pages:
- password-generator.astro, json-formatter.astro, base64-converter.astro, hash-generator.astro, url-encoder.astro, kalkulator-uptime.astro
- Shared layout/components in src/components/alat/shared/*

## Search Index API

- Route: src/pages/search-index.json.ts (GET)
- Uses utils/search-index to generate providers/categories/pages JSON
- Adds _timestamp for dev no-cache; returns structured error with empty arrays on failure

## Navigation & Footer (src/navigation.ts)

- Header menus: Perbandingan, Review, Direktori Hosting (Kategori Populer, Lokasi Populer, Quick Actions), Tools & Edukasi, Promo
- Footer includes policy and methodology pages, plus tools used for research (K6, Uptimia)
- Footnote includes dynamic copyright and policy links

## SEO, Images, and Performance

- SEO: @astrolib/seo, sitemap with filtered routes (no blog pagination, tag pages, or directory pagination)
- Images: Astro image service, allowed domains: img.penasihathosting.com, cdn.penasihathosting.com; responsive with lazy loading
- Compression: astro-compress (CSS/HTML/JS); SVG/Image compression disabled (handled upstream/CDN)

## CI & Code Quality

- GitHub Actions (.github/workflows/actions.yaml):
  - build job (Node 18/20/22): checkout → setup-node with npm cache → npm ci → npm run build
  - check job (Node 22): checkout → npm ci → npm run check
- Linting: eslint (typescript-eslint, astro plugin), prettier (with astro plugin)

## CMS (optional)

- Decap CMS config at public/decapcms/config.yml (git-gateway, main)
- Media folder: src/assets/images; public_folder: /_astro
- NOTE: The CMS collection targets src/content/post, while project posts live in src/data/post — adjust if enabling CMS

## Development Notes & Conventions

- Use ~ alias for imports from src
- Keep trailing slashes in routes; use getPermalink helper from utils/permalinks
- Indonesian copy and metadata (id locale) throughout; price in IDR
- Prefer Astro islands (Preact) for interactivity; avoid unnecessary client JS
- Store provider media under src/assets/images/providers; keep filenames stable for caching

## Recent Updates (high level)

- WikiHosting system (July 2025): advanced schema (category/phase/priority/difficulty, target keywords, FAQ)
- Location-based directory (Dec 2024): location filters/pages, standardized 2-column grids, breadcrumbs, dynamic paths
- Provider pages: media modal, sticky pricing sidebar, promo copy UX, tiered badges; simplified event handling
- Data schema: added recommended tier, improved promo fields, normalized locations

## License

MIT License (see LICENSE.md)

---
# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Project Overview

This is **PenasihatHosting** - an Indonesian web hosting review and comparison website built with Astro 5.11.1 and Tailwind CSS. The project is based on the AstroWind template and focuses on providing independent reviews and comparisons of web hosting providers in Indonesia.

The site serves as a comprehensive resource for Indonesian users looking for web hosting solutions, featuring detailed reviews, comparisons, educational content, and practical tools.

## Development Commands

### Essential Commands
- `npm install` - Install dependencies
- `npm run dev` - Start development server (http://localhost:4321)
- `npm run build` - Build for production (output to `./dist/`)
- `npm run preview` - Preview production build

### Code Quality
- `npm run check` - Run all checks (Astro, ESLint, Prettier)
- `npm run check:astro` - Run Astro check
- `npm run check:eslint` - Run ESLint check
- `npm run check:prettier` - Run Prettier check
- `npm run fix` - Fix all fixable issues (ESLint + Prettier)
- `npm run fix:eslint` - Fix ESLint issues
- `npm run fix:prettier` - Fix Prettier formatting

## Key Configuration Files

- `astro.config.ts` - Main Astro configuration with integrations
- `src/config.yaml` - Site configuration, SEO metadata, and app settings
- `src/navigation.ts` - Site navigation structure (header/footer)
- `tailwind.config.js` - Tailwind CSS configuration
- `package.json` - Dependencies and scripts

## Architecture Overview

### Content Management
- **Blog Posts**: Located in `src/data/post/` as MDX files with frontmatter
- **Hosting Reviews**: Individual MDX files with structured frontmatter for SEO
- **Static Pages**: Astro components in `src/pages/`
- **Content Collections**: Configured in `src/content/config.ts`
- **Recommendations**: Structured content in `src/content/recommendations/` organized by hosting type
- **WikiHosting Content**: Comprehensive hosting education articles in `src/content/wikihosting/`
- **Kamus Content**: Hosting dictionary terms in `src/content/kamus/`
os
### Key Directories
- `src/components/` - Reusable Astro components organized by feature
- `src/layouts/` - Page layouts (Layout.astro, MarkdownLayout.astro, PageLayout.astro)
- `src/utils/` - Utility functions for blog, permalinks, images, location processing, etc.
- `src/assets/` - Images, fonts, and styles processed by Astro
- `public/` - Static assets served directly (favicons, robots.txt, etc.)
- `src/content/` - Content collections for recommendations, wikihosting, kamus, and structured data

### Blog System
- **Permalink Generation**: Uses `src/utils/permalinks.ts` with configurable patterns
- **Post Processing**: `src/utils/blog.ts` handles post normalization and related posts
- **URL Structure**: Configured in `src/config.yaml` with permalink patterns like `/%slug%`
- **MDX Support**: Rich content with embedded components and interactive elements

### Component Structure
- `src/components/blog/` - Blog-specific components (PostCard, Pagination, etc.)
- `src/components/widgets/` - Reusable widgets (Hero, Features, etc.)
- `src/components/ui/` - Base UI components (Button, Form, etc.)
- `src/components/common/` - Common components (Analytics, Metadata, etc.)
- `src/components/home/<USER>
- `src/components/alat/` - Tool components for utility pages
- `src/components/direktori/` - Directory-specific components for provider listings and location pages

### Hosting Review System
- **Review Data**: Structured TypeScript objects in `src/components/home/<USER>/data/`
- **Categories**: 
  - `hosting-terbaik/` - Premium hosting providers
  - `hosting-murah/` - Budget hosting providers
- **Type Definitions**: `src/components/home/<USER>/types.ts` defines `HostingReview` interface
- **Review Components**: Reusable components for displaying hosting reviews
- **Recommendation Content**: Markdown files in `src/content/recommendations/` with frontmatter

### Content Categories

#### Hosting Types Covered
- **Shared Hosting Indonesia**: Budget-friendly options for beginners
- **Cloud Panels**: Server management solutions (CloudPanel, etc.)
- **VPS/Cloud Hosting**: Scalable hosting solutions
- **Managed Hosting**: Fully managed hosting services

#### Educational Content
- **Hosting Guides**: Comprehensive tutorials and explanations
- **Hosting Dictionary**: `kamushosting.md` - 200+ hosting terms explained in Indonesian
- **WikiHosting Content**: In-depth educational articles with structured schema
- **Kamus Collection**: Individual term definitions with comprehensive metadata
- **Comparison Articles**: Detailed hosting provider comparisons
- **Technical Tutorials**: Setup guides and best practices

#### Tools and Utilities
- **Developer Tools**: Built as Astro pages in `src/pages/`
- **Password Generator**: Security tools for users (`password-generator.astro`)
- **JSON Formatter**: Development utilities (`json-formatter.astro`)
- **Base64 Converter**: Encoding/decoding tool (`base64-converter.astro`)
- **Hash Generator**: MD5, SHA1, SHA256 hash generation (`hash-generator.astro`)
- **URL Encoder**: URL encoding/decoding utility (`url-encoder.astro`)
- **Uptime Calculator**: Calculate uptime/downtime statistics (`kalkulator-uptime.astro`)
- **Other Utilities**: Various web tools for developers and users

## Important Patterns

### SEO and Metadata
- Uses `@astrolib/seo` for SEO optimization
- Metadata configured in `src/config.yaml`
- Custom meta tags and OpenGraph images supported
- Structured data for hosting reviews and articles
- Indonesian language optimization (`id` locale)

### Image Optimization
- Uses Astro's built-in image optimization
- Custom domain configured: `img.penasihathosting.com`
- Responsive images with lazy loading via rehype plugins
- Optimized for Indonesian hosting provider logos and screenshots

### Content Features
- **MDX Support**: Rich content with components
- **Responsive Tables**: Hosting comparison tables
- **Lazy Loading**: Performance optimization
- **Automatic Sitemap**: SEO-friendly site structure
- **RSS Feed**: Automatic feed generation for blog posts
- **Multilingual Ready**: Configured for Indonesian content

### Review System Features
- **Structured Reviews**: Consistent format across all hosting providers
- **Pricing Information**: Detailed pricing in Indonesian Rupiah (IDR)
- **Feature Comparisons**: Standardized feature lists
- **Coupon Integration**: Discount codes and affiliate links
- **Performance Metrics**: Server location and performance data

## Development Notes

### Localization
- Primary language: Indonesian (`id`)
- Currency: Indonesian Rupiah (IDR)
- Server focus: Indonesian hosting providers
- Content optimized for Indonesian market

### Technical Configuration
- Static site generation with trailing slashes enabled
- Custom vendor integration at `vendor/integration`
- Vite alias configured: `~` points to `./src`
- Image domains allowlisted for optimization
- Astro 5.11.1 with latest features and optimizations
- Location-based directory system with dynamic routing

### Content Strategy
- Focus on Indonesian hosting market
- Independent reviews and comparisons
- Educational content for beginners
- Technical guides for developers
- Practical tools and utilities

## Content Structure

### Frontmatter Standards
Blog posts and reviews use structured frontmatter including:
- `title` - Page title
- `description` - Meta description
- `publishedAt` - Publication date
- `keywords` - SEO keywords
- `author` - Content author
- `excerpt` - Short summary
- Custom fields for hosting reviews (pricing, features, etc.)

**WikiHosting Content Standards:**
- `category` - Content categorization (Core Concepts, Security Terms, etc.)
- `phase` - Development phase (Phase 1-5)
- `priority` - Content priority (High, Medium, Low)
- `difficulty` - Reader level (Pemula, Menengah, Advanced)
- `targetKeywords` - SEO target keywords array
- `relatedTerms` - Related hosting terms
- `readingTime` - Estimated reading time in minutes

**Hosting Provider Data Structure:**
- `name` - Provider name and display information
- `company` - Company details (founded, headquarters, support channels)
- `categories` - Service categories (shared-hosting, cloud-hosting, vps, etc.)
- `badges` - Provider badges (recommended, sponsored, verified)  
- `features` - Feature list with icons and descriptions
- `datacenters` - Data center locations with country flags
- `pricing` - Detailed pricing plans with features and promo codes
- `gallery` - Media gallery and featured images
- `tier` - Provider tier (basic, verified, sponsor, recommended)
- `affiliateLink` - Affiliate tracking URLs

### Review Data Structure
Hosting reviews follow the `HostingReview` interface:
- `providerName` - Hosting provider name
- `website` - Provider website
- `reviewUrl` - Internal review page URL
- `pricing` - Pricing information in IDR
- `couponCode` - Discount codes
- `highlights` - Key selling points
- `boxContent` - Detailed review content

### URL Structure
- Blog posts: `/%slug%`
- Hosting reviews: `/review-[provider-name]`
- Category pages: `/hosting-murah`, `/hosting-terbaik`
- Tools: `/alat/[tool-name]`
- Static pages: Direct path mapping

## Key Pages and Sections

### Homepage Sections
- `HomeHero` - Main hero section
- `IntroductionSection` - Site introduction
- `TopHostingSection` - Featured hosting providers
- `HostingRecommendationSection` - Categorized recommendations
- `FAQSection` - Frequently asked questions

### Specialized Pages
- `/hosting-murah` - Budget hosting recommendations
- `/hosting-terbaik` - Premium hosting recommendations
- `/kamushosting` - Hosting dictionary (200+ terms)
- Various tool pages in `/alat/` directory

### Content Collections
- `recommendations` - Structured hosting recommendations
- `wikihosting` - Comprehensive educational articles with advanced schema
- `kamus` - Hosting dictionary terms with metadata
- `wiki` - Individual wiki entries
- `hosting-providers` - Structured provider data
- `hosting-categories` - Provider category definitions
- Blog posts in `src/data/post/` with MDX format
- Static pages as Astro components

**Provider Database Structure:**
- **46+ Active Providers**: Comprehensive database of Indonesian hosting providers
- **Structured Data**: JSON-based provider information with standardized schema
- **Location Coverage**: Providers mapped to data center locations across Indonesia and Singapore
- **Category System**: Organized by hosting types (shared, cloud, VPS, WordPress, dedicated)
- **Tier System**: Provider classification (basic, verified, sponsor, recommended)
- **Features Matrix**: Standardized feature comparison across providers

## UI/UX Design System

### Design Philosophy

PenasihatHosting employs a clean, professional design that prioritizes trust, readability, and user experience. The design system is built around the concept of providing clear, unbiased hosting reviews with an emphasis on Indonesian market needs.

**Core Design Principles:**
- **Trust & Credibility**: Professional appearance with clear disclosure statements
- **Clarity**: Clean typography and well-structured content hierarchy
- **Accessibility**: Mobile-first responsive design with proper contrast ratios
- **Performance**: Optimized images and efficient CSS for fast loading
- **Localization**: Indonesian-focused content with appropriate cultural considerations

### Color Palette

The website uses a sophisticated color system built on CSS custom properties for consistent theming and comprehensive dark mode support.

**Light Mode Colors:**
- **Primary**: `oklch(14.5% 0.01 270)` - Very dark navy/black for primary buttons and accents
- **Secondary**: `oklch(55.6% 0 0)` - Neutral gray for secondary elements
- **Brand**: `rgb(1 97 239)` - Bright blue for brand elements
- **Accent**: `oklch(50.5% 0.213 27.518)` - Warm orange/red for links and highlights
- **Accent Hover**: `oklch(50.5% 0.213 27.518 / 0.8)` - Semi-transparent accent for hover states

**Text Colors (Light Mode):**
- **Heading**: `rgb(0 0 0)` - Pure black for maximum contrast on headings
- **Default**: `oklch(0.252 0 0)` - Very dark gray for body text
- **Muted**: `rgb(16 16 16 / 66%)` - Semi-transparent for secondary text

**Background Colors (Light Mode):**
- **Page**: `#f9f9f9` - Light gray page background
- **Section**: `rgb(255 255 255)` - Pure white for content sections
- **Card**: `rgb(255 255 255)` - White background for cards
- **Form**: `rgb(255 255 255)` - White for form elements
- **Input**: `rgb(255 255 255)` - White for input fields
- **Muted**: `#F2EEE9` - Warm beige for muted backgrounds

**Dark Mode Colors:**
- **Primary**: `rgb(1 97 239)` - Bright blue for primary elements
- **Secondary**: `rgb(1 84 207)` - Darker blue for secondary elements
- **Accent**: `oklch(50.5% 0.213 27.518)` - Same warm accent color
- **Accent Hover**: `oklch(50.5% 0.213 27.518 / 0.8)` - Semi-transparent accent

**Text Colors (Dark Mode):**
- **Heading**: `rgb(247, 248, 248)` - Near-white for headings
- **Default**: `rgb(229 236 246)` - Light blue-gray for body text
- **Muted**: `rgb(229 236 246 / 66%)` - Semi-transparent light text

**Background Colors (Dark Mode):**
- **Page**: `rgb(3 6 32)` - Deep navy background
- **Section**: `rgb(15 23 42)` - Slightly lighter navy for sections
- **Card**: `rgb(30 41 59)` - Medium slate for cards
- **Form**: `rgb(30 41 59)` - Same as cards for consistency
- **Input**: `rgb(30 41 59)` - Consistent input styling
- **Muted**: `rgb(31 41 55)` - Muted slate background

**Special Effects:**
- **Selection (Light)**: `lavender` - Light purple text selection
- **Selection (Dark)**: `black` background with `snow` text

### Typography System

**Font Family:**
- **Primary**: 'Barlow' from Google Fonts
- **Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Fallbacks**: System fonts (ui-sans-serif) for performance

**Typography Scale:**
- **Headings**: Responsive sizing (text-3xl md:text-4xl, text-2xl md:text-3xl)
- **Body Text**: Base size with responsive adjustments
- **Small Text**: text-sm for metadata and secondary information
- **Large Text**: text-lg for emphasis and introductions

**Typography Features:**
- **Line Height**: Optimized for readability (leading-tighter for headings)
- **Letter Spacing**: Subtle tracking adjustments (tracking-tighter)
- **Font Weight**: Strategic use of bold weights for hierarchy
- **Responsive**: Automatic scaling across device sizes

### Layout System

**Container System:**
- **Global Max Width**: 1100px (`max-w-global`) - Optimized for readability
- **Content Width**: Character-based width (max-w-[65ch]) for optimal reading
- **Responsive Padding**: `px-4 md:px-6` for consistent horizontal spacing
- **Section Padding**: `py-12 md:py-16 lg:py-20` for vertical rhythm

**Grid Patterns:**
- **Three-Column Layout**: `lg:grid lg:grid-cols-3 lg:gap-12` for content with sidebar
- **Two-Column Layout**: `md:grid md:grid-cols-2` for balanced content presentation
- **Card Grids**: `grid-cols-1 md:grid-cols-3 gap-6` for hosting provider cards
- **Responsive Gaps**: `gap-4`, `gap-6`, `gap-8`, `gap-12` for consistent spacing

**Spacing System:**
- **Base Unit**: 4px Tailwind spacing scale (1 = 4px)
- **Component Padding**: `p-4` (16px), `p-6` (24px), `p-8` (32px)
- **Section Spacing**: `py-8` (32px) for sections, `py-12` (48px) for major sections
- **Vertical Rhythm**: `mb-4` (16px), `mb-6` (24px), `mb-8` (32px), `mb-12` (48px)
- **Responsive Scaling**: Larger spacing on desktop (`md:py-16`, `lg:py-20`)

**Scroll and Positioning:**
- **Scroll Offset**: `scroll-mt-[72px]` for sticky header compensation
- **Z-Index Layers**: Header (`z-40`), overlays (`z-50`), backgrounds (`-z-[1]`)
- **Sticky Elements**: Header with `sticky top-0` positioning

### Component Design Patterns

**Button System:**

*Base Button Class:*
```css
.btn {
  @apply inline-flex items-center justify-center rounded-full border-gray-400 border bg-transparent font-medium text-center text-base leading-snug transition py-3.5 px-6 md:px-8 ease-in duration-200 focus:ring-blue-500 focus:ring-offset-blue-200 focus:ring-2 focus:ring-offset-2 hover:bg-gray-100 hover:border-gray-600 cursor-pointer;
}
```

*Primary Button:*
```css
.btn-primary {
  background-color: var(--aw-color-primary);
  color: white;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease-in-out;
}
.btn-primary:hover {
  background-color: var(--aw-color-accent);
}
```

**Button Variants:**
- **Primary**: Dark navy background (`--aw-color-primary`) with white text, hover changes to accent color
- **Secondary**: Gray background (`--aw-color-secondary`) with white text
- **Tertiary**: Text-only style with hover effects
- **Link**: Simple text with color change on hover (`hover:text-primary`)

**Card Design Patterns:**
- **Border Radius**: `rounded-lg` (8px) for cards, `rounded-xl` (12px) for featured content
- **Shadows**: `shadow-md` for standard depth, `shadow-lg` for emphasis, `shadow-xl` for hover states
- **Borders**: `border border-gray-200 dark:border-gray-700` for definition
- **Hover Effects**: `hover:shadow-xl hover:-translate-y-1` for lift effect
- **Background**: Uses semantic background colors (`bg-bg-card`)

**Sponsored Content Styling:**
- **Border Animation**: Gradient border with glow effect
- **Button Gradient**: `linear-gradient(135deg, #C10007, #A50006)` for sponsored CTAs
- **Glow Effects**: Animated glow with `@keyframes sponsored-glow`
- **Text Gradient**: Gradient text effect for sponsored badges

**Form Elements:**
- **Input Fields**: `py-3 px-4` padding with `rounded-lg` borders
- **Background**: Uses `--aw-color-bg-input` for consistent theming
- **Focus States**: Ring-based focus indicators (`focus:ring-2 focus:ring-blue-500`)
- **Validation**: Color-coded feedback with semantic colors

### Navigation Design

**Header Structure:**
- **Sticky Navigation**: `sticky top-0 z-40` for persistent access
- **Shadow**: `shadow-md` for visual separation
- **Background**: White with dark mode support
- **Logo**: Prominent branding on the left
- **Menu**: Right-aligned navigation with dropdowns

**Dropdown Menus:**
- **Mega Menu**: 680px width for service categories
- **Animation**: Smooth expand/collapse with rotation icons
- **Mobile**: Accordion-style for touch interfaces
- **Backdrop**: Blur effect for visual hierarchy

**Mobile Navigation:**
- **Hamburger Menu**: Three-line animated icon
- **Full-Screen**: Overlay navigation for mobile
- **Touch Targets**: Adequate spacing for finger navigation

### Content Presentation

**Hosting Review Cards:**
- **Structured Layout**: Two-column grid with image and content
- **Visual Hierarchy**: Clear headings, bullet points, and CTAs
- **Border Treatment**: 4px primary border for emphasis
- **Responsive Images**: Optimized loading with proper aspect ratios

**Blog Post Cards:**
- **Featured Variant**: Horizontal layout for important content
- **Standard Variant**: Vertical card layout
- **Hover Effects**: Scale and shadow transitions
- **Category Badges**: Color-coded labels for content types

**Content Typography:**
- **Prose Styling**: Tailwind Typography plugin with custom H2 styling
  ```css
  h2 {
    border-top-width: 4px;
    border-color: theme('colors.primary');
    padding-top: 0.5rem;
    margin-top: 2em;
    margin-bottom: 1em;
  }
  ```
- **Link Styling**: Accent color (`--aw-color-accent`) with bold weight (700)
- **List Styling**: Consistent bullet points and numbering with proper spacing
- **Code Blocks**: Syntax highlighting for technical content
- **Semantic Colors**: Automatic color application to HTML elements (h1-h6, p, span, div)

### Interactive Elements & Animations

**Hover States:**
- **Buttons**: Background color transitions (200ms ease-in-out)
- **Cards**: Combined scale and shadow effects (`hover:scale-105`, `hover:shadow-xl`)
- **Links**: Color transitions to accent color with underline reveal
- **Images**: Subtle zoom effects (`hover:scale-105`) with 500ms duration
- **Post Cards**: Lift effect (`hover:-translate-y-1`) with enhanced shadows

**Custom Animations:**
- **Fade In Up**: `fadeInUp` keyframe animation (1s duration)
  ```css
  @keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(2rem); }
    100% { opacity: 1; transform: translateY(0); }
  }
  ```
- **Gradient Shift**: Animated gradient borders for sponsored content
- **Sponsored Glow**: Pulsing glow effect for premium content
- **Border Rotate**: 360-degree rotating gradient borders

**Transition System:**
- **Standard Duration**: 200ms for buttons and quick interactions
- **Medium Duration**: 300ms for dropdowns and menus
- **Long Duration**: 500ms for image transforms and complex animations
- **Easing**: `ease-in-out` for natural feeling transitions

**Dropdown Animations:**
- **Mobile**: `max-height` transitions (300ms ease-out) with opacity fade
- **Desktop**: Transform-based positioning with backdrop blur
- **Icon Rotation**: Chevron icons rotate 180° on expand

**Focus Management:**
- **Keyboard Navigation**: Proper tab order with visible focus rings
- **Focus Rings**: Blue ring system (`focus:ring-2 focus:ring-blue-500`)
- **Screen Readers**: Comprehensive ARIA labels and semantic HTML
- **Color Contrast**: WCAG AA compliance across all color combinations

### Responsive Design Strategy

**Breakpoint System:**
- **Mobile First**: Base styles for mobile devices
- **Small**: `sm:` (640px+) for larger phones
- **Medium**: `md:` (768px+) for tablets
- **Large**: `lg:` (1024px+) for desktop
- **Extra Large**: `xl:` (1280px+) for large screens

**Mobile Optimizations:**
- **Touch Targets**: Minimum 44px for interactive elements
- **Readable Text**: Appropriate font sizes without zooming
- **Simplified Navigation**: Collapsible menus and streamlined content
- **Performance**: Optimized images and minimal JavaScript

**Desktop Enhancements:**
- **Larger Typography**: Increased font sizes for better readability
- **Multi-Column Layouts**: Efficient use of screen real estate
- **Hover Effects**: Rich interactions for mouse users
- **Advanced Navigation**: Mega menus and complex dropdowns

### Visual Design Patterns

**Card Variants:**
- **Standard Cards**: `rounded-xl shadow-md border` with hover lift effects
- **Featured Cards**: Horizontal layout (`md:flex-row`) for important content
- **Guide Cards**: Gradient overlays with chapter count badges
- **Review Cards**: 4px primary border with structured content layout
- **Sponsored Cards**: Animated gradient borders with glow effects

**Badge System:**
- **Category Badges**: `bg-primary text-white text-xs font-bold px-2 py-1 rounded`
- **Sponsored Badges**: Red gradient text with custom styling
- **Guide Badges**: White text on gradient overlay for chapter counts
- **Status Indicators**: Color-coded badges for different content types

**Image Treatments:**
- **Aspect Ratios**: Consistent 16:9 ratio for blog and review images
- **Hover Effects**: Scale transform (`hover:scale-105`) with overflow hidden
- **Gradient Overlays**: `bg-gradient-to-t from-black/70` for text readability
- **Lazy Loading**: Progressive loading with proper alt text

**Icon System:**
- **Tabler Icons**: Consistent icon library throughout the site
- **Icon Weights**: Light (1.2) and bold (2.4) stroke weights
- **Icon Sizes**: Standardized sizing (`w-4 h-4`, `w-5 h-5`, `w-6 h-6`)
- **Icon Colors**: Semantic coloring based on context

### Performance Considerations

**Image Optimization:**
- **Lazy Loading**: Progressive image loading with `loading="lazy"`
- **Responsive Images**: Multiple widths (`[400, 900]`) for different devices
- **Modern Formats**: WebP format support for smaller file sizes
- **CDN Integration**: `img.penasihathosting.com` for optimized delivery
- **Aspect Ratio**: Consistent 16:9 ratio prevents layout shift

**CSS Optimization:**
- **Tailwind Purging**: Automatic unused CSS removal for smaller bundles
- **Critical CSS**: Above-the-fold styles inlined for faster rendering
- **Component Styles**: Scoped styling with CSS custom properties
- **CSS Compression**: Minification enabled in production builds

**JavaScript Optimization:**
- **Minimal JavaScript**: Progressive enhancement approach
- **Intersection Observer**: For scroll-triggered animations
- **Event Delegation**: Efficient event handling for dropdowns
- **Performance Monitoring**: Umami analytics for user experience tracking

This design system ensures a consistent, professional, and user-friendly experience across all pages while maintaining excellent performance and accessibility standards for Indonesian hosting review content.

## Recent Development Updates

### WikiHosting Content System (Latest)
**Implementation Date**: July 2025

**New Features Added:**
- **Comprehensive Education Platform**: New WikiHosting content collection for in-depth hosting education
- **Structured Content Schema**: Advanced metadata system with categories, phases, and difficulty levels
- **SEO Optimization**: Target keywords, related terms, and content classification
- **Content Roadmap**: 150+ planned articles for 2025-2026 covering all hosting topics

**Content Categories:**
- **Core Concepts**: Fundamental hosting knowledge
- **Security Terms**: Website security and compliance
- **Performance Terms**: Speed and optimization topics
- **Technical Terms**: Advanced technical concepts
- **Business Terms**: Commercial hosting aspects
- **Advanced Concepts**: Expert-level topics

### Location-Based Directory System
**Implementation Date**: December 2024

**New Features Added:**
- **Location-Based Filtering**: Users can now filter hosting providers by data center location
- **URL Structure**: `/direktori/lokasi/` for all locations, `/direktori/lokasi/jakarta/` for specific locations
- **Clickable Data Centers**: Provider detail pages now have clickable data center locations
- **Consistent Grid Layout**: Standardized 2-column grid across all directory pages

**Technical Implementation:**
- **LocationUtils**: New utility functions for location processing and slug generation (`src/utils/locationUtils.ts`)
- **LocationCard Component**: Reusable card component for location display with variants (default, featured, compact)
- **ProviderGrid Component**: Shared component for consistent provider listings with empty state handling
- **ProviderCard Component**: Individual provider display cards with tier-based styling
- **Dynamic Pages**: Static generation for all location-based pages with SEO optimization

### Enhanced Provider Detail Pages
**Media Gallery Improvements:**
- **Click-to-Enlarge**: Images now open in modal instead of hover animations
- **Modal System**: Full-screen image viewing with ESC/close button/click-outside functionality
- **Clean Design**: Removed complex hover effects for better UX

**Sidebar Redesign:**
- **Simplified Pricing Card**: Clean white background with minimal styling
- **Promo Code Integration**: Copy-to-clipboard functionality with visual feedback
- **Sticky Behavior**: Dynamic padding (2rem) when scrolling for better visibility
- **NYT-Style Headers**: Consistent border-top design across all sections

**Provider Submission System:**
- **Multi-Tier Pricing**: Basic (Free), Verified (Rp 300,000/year), Sponsor (Rp 600,000/year)
- **Comprehensive Form**: Tally.so integration for provider submissions
- **Feature Differentiation**: Badge systems, priority placement, analytics reporting
- **Review Process**: 1-2 day review cycle with email notifications

### Component Architecture Improvements
**Modular JavaScript Components:**
- **ImageModal.astro**: Self-contained modal functionality with ESC/click-outside closing
- **StickyScroll.astro**: Sidebar scroll behavior management with dynamic padding
- **PromoCodeCopy.astro**: Clipboard operations with fallback support and visual feedback
- **SearchBox.astro**: Directory search functionality with filtering
- **CategoryCard.astro**: Reusable category display component

**Performance Optimizations:**
- **Simplified Event Handling**: Removed complex MutationObserver patterns
- **Direct Event Listeners**: Function-based approach instead of class-based
- **No Infinite Loops**: Fixed loading issues caused by DOM observation conflicts
- **Proper Null Checks**: Enhanced error handling and stability

### Design System Consistency
**NYT-Style Implementation:**
- **Border-Top Headers**: `border-t-8 border-primary pt-4` for main content
- **Section Headers**: `border-t-2 border-primary pt-2` for subsections
- **Consistent Typography**: Unified heading styles across all pages
- **Grid Standardization**: 2-column layout for all directory listings

**Navigation Enhancements:**
- **Location Menu**: Added "Berdasarkan Lokasi" to directory dropdown
- **Breadcrumb Updates**: Proper navigation hierarchy for location pages
- **Import Path Fixes**: Resolved all layout import issues across pages

### Data Structure Enhancements
**Provider Schema Updates:**
- **Tier Support**: Added 'recommended' tier to existing enum values
- **Location Processing**: Standardized data center location handling
- **Promo Integration**: Enhanced promo code and description fields

**Content Collections:**
- **Location Utilities**: Helper functions for location-based operations (`src/utils/locationUtils.ts`)
- **Provider Filtering**: Enhanced filtering by location and category
- **Static Path Generation**: Automated page generation for all locations
- **WikiHosting Schema**: Comprehensive content management with phases, priorities, and SEO metadata
- **Kamus Collection**: Individual hosting term definitions with structured metadata

### Navigation System Updates
**Enhanced Directory Navigation:**
- **Location-Based Browsing**: "Berdasarkan Lokasi" menu option added
- **Category Browsing**: Comprehensive category navigation
- **Provider Submission**: Integrated submission form for new providers
- **Mobile-Optimized**: Responsive navigation for mobile users

### Content Strategy Implementation
**Educational Focus:**
- **200+ Planned Articles**: Comprehensive roadmap for hosting education (2025-2026)
- **Indonesian Context**: All content optimized for Indonesian market with local business examples
- **Difficulty Levels**: Content categorized by user expertise (Pemula, Menengah, Advanced)
- **Visual Content**: Screenshot-heavy tutorials and infographics planned
- **Tool Integration**: Interactive calculators and utilities (uptime, JSON formatter, hash generator, etc.)

**Provider Management System:**
- **Comprehensive Database**: 46+ Indonesian hosting providers with detailed profiles
- **Tier-Based Organization**: Structured provider classification system
- **Location Mapping**: Data center location tracking and filtering
- **Review Integration**: Provider reviews linked to directory listings
- **Submission Workflow**: Streamlined provider onboarding with pricing tiers

This documentation should be updated as the project evolves to maintain accuracy and usefulness for development work.
