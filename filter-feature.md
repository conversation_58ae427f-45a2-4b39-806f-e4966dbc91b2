# Rencana Implementasi Fitur Filter Kategori Provider Hosting

## 1. <PERSON><PERSON><PERSON>
Menyediakan filter dinamis (client-side) di halaman kategori (`/direktori/[category].astro`) tepat setelah tab navigasi ("Semua Provider" | "Panduan Pengguna"). Fase awal fokus pada filter promo ("Sedang Promo"), lalu diperluas ke Support Channel dan Data Center.

## 2. Ruang Lingkup Fase 1 (MVP)
- <PERSON><PERSON> komponen `FilterBar` terpisah.
- Filter aktif hanya: Harga → opsi tunggal "Sedang Promo".
- Filtering berjalan client-side dengan manipulasi DOM (progressive enhancement).
- Pagination disembunyikan ketika ada filter aktif.
- Update badge jumlah provider yang tampil secara dinamis.

## 3. Definisi & Aturan Data
| Filter | Field Sumber | Kriteria Penentuan | Catatan |
|--------|--------------|--------------------|---------|
| Sedang Promo | `provider.data.pricing` | `pricing.promoCode` atau `pricing.promoDescription` truthy | XCloud contoh TRUE, Autowebhost FALSE |
| Support Channel (fase 2) | `provider.data.company.supportChannels[]` | Interseksi dengan pilihan user | Multi-select (checkbox) |
| Data Center (fase 2) | `provider.data.datacenters[].country` atau `.location` | Interseksi dengan pilihan user | Multi-select (checkbox / searchable) |

Promo definition sementara TIDAK melihat plan-level `plans[].promoCode`, hanya root-level.

## 4. Arsitektur Komponen
### 4.1 Komponen Baru
`src/components/direktori/FilterBar.astro`
- Mengikuti prinsip Astro: "Zero / Minimum JS by default".
- Implementasi fase 1: pure Astro + `<script type="module">` kecil (tanpa framework) → tidak perlu `client:*` directive karena bukan komponen framework (best practice: kirim hanya JS yang benar-benar dibutuhkan).
- Jika nanti kompleks (multi-select banyak, pencarian), pertimbangkan migrasi ke island ringan (misal Preact) dengan `client:idle` (sesuai docs: gunakan `client:idle` untuk UI non-kritis) agar initial load efisien.
- Props (fase 1 minimal): `totalProviders: number` (opsional), bisa gunakan `data-total` pada wrapper jika ingin benar-benar meminimalkan props.
- Akan:
  - Render container filter + ringkasan hasil.
  - Render dropdown Harga (checkbox Sedang Promo).
  - Sisipkan script state & applyFilters dibungkus IIFE untuk menghindari global leakage.

### 4.2 Integrasi di Halaman
- Import & tempatkan `<FilterBar />` di `[category].astro` setelah section Tab Navigation dan sebelum `<section id="providers-section">`.

### 4.3 Tanpa Refactor Besar
- Tidak memecah `ProviderList` menjadi card individual dulu (minim risiko).
- Mengandalkan data-attribute di wrapper card untuk filtering.

### 4.4 Prinsip Hydration (Referensi Context7 Astro Docs)
- Tidak memakai framework hydration jika tidak diperlukan → mengurangi bundle.
- Hindari `client:load` kecuali komponen framework diperlukan segera (docs: `client:load` untuk interaksi langsung). Sekarang cukup vanilla.
- Potensi masa depan: gunakan `client:visible` bila filter dipindah ke bawah fold (tidak sekarang), atau `client:idle` bila logika filter menjadi berat.
- Tidak perlu custom client directive (docs: `addClientDirective`) karena use case sederhana.

## 5. Penambahan Data-Attribute di Provider Cards
Modifikasi `ProviderList.astro` saat mapping provider:
- Tambah pada elemen root card (div dengan `data-provider-card`):
  - `data-has-promo="true|false"`.
  - `data-support="live-chat|ticket|email"` (joined by `|`).
  - `data-dc="Singapore|Indonesia|..."` (joined by `|`).
- (Fase 1 cukup `data-has-promo`).
- Gunakan `toLowerCase()` saat join untuk normalisasi pencarian.

## 6. Algoritma Filtering (Client-Side)
State object:
```js
const filterState = {
  promo: false,
  supports: new Set(), // fase 2
  dcs: new Set(),      // fase 2
};
```
Optimasi / Best Practice Astro & Web Performance:
- Batch DOM writes: loop sekali lalu apply style/class.
- Gunakan class util `.hidden` vs manipulasi inline style repetitif.
- Hindari layout thrash: tidak membaca offset di loop.
- Minimal JSON serialization (tidak embed seluruh array berulang).

Langkah apply (disesuaikan):
1. Select statis sekali: `const cards = [...document.querySelectorAll('[data-provider-card]')]`.
2. Loop: tentukan boolean `visible`.
3. Toggle class `hidden` (Tailwind) atau `aria-hidden` (aksesibilitas). Tambah `inert` opsional saat hidden (future improvement) agar fokus tidak masuk.
4. Hitung visible → update UI.
5. Toggle pagination wrapper (add class `hidden`).

Pseudo ringkas:
```js
function applyFilters() {
  let visible = 0;
  for (const card of cards) {
    let show = true;
    if (filterState.promo && card.dataset.hasPromo !== 'true') show = false;
    card.classList.toggle('hidden', !show);
    if (show) visible++;
  }
  updateVisibleCount(visible);
  paginationEl?.classList.toggle('hidden', filterActive());
}
```

## 7. URL Parameter (Progressive Enhancement)
- Fase 1: `?promo=1` → pre-select.
- Parsing: gunakan `new URLSearchParams(location.search)`; on state change update dengan `history.replaceState` (no reload) supaya ringan.
- Pastikan tidak menambah parameter kosong.

## 8. UX & Aksesibilitas
- Dropdown: gunakan `<details class="filter-dropdown">` + `<summary>` (native keyboard & a11y default). Tambah `aria-label` bila teks ringkas.
- Checkbox label dapat di-click (wrap <input> di dalam <label> atau gunakan `for`).
- Live region sederhana (optional) `<div aria-live="polite" class="sr-only" id="filter-status">` untuk mengumumkan jumlah hasil baru.
- Gunakan `fieldset` + `legend` (future fase 2 multi-group) untuk semantics.
- Pastikan tab order: summary → checkbox → reset.

## 9. Styling (Tailwind)
- Wrapper: `flex flex-wrap gap-3 items-start mb-6`.
- `<details>`: border, rounded, ring focus: `focus:outline-none focus:ring-2 focus:ring-primary/40`.
- Transition open: gunakan `[open]` selector (Tailwind `data-[state=open]` alternatif manual CSS kecil inline).
- Hindari animasi height auto berat (skip dulu).

## 10. Langkah Implementasi Fase 1 (Promo Only)
1. Tambah data-attribute `data-has-promo` di `ProviderList.astro` (per provider).
2. Tambah komponen `FilterBar.astro` (markup + checkbox + script minimal) → gunakan IIFE.
3. Sisipkan komponen ke `[category].astro` setelah nav tabs.
4. Seleksi pagination container (`section` atau nav pagination) beri id `pagination-wrapper` untuk toggle.
5. Update counter terpisah: elemen `<span id="filter-count" data-total="X">`.
6. Parse URL param `promo=1` sebelum initial `applyFilters()`.
7. Tambah tombol reset jika state aktif.
8. Manual QA (list di Section 12).
9. Komentar dokumentasi di file komponen untuk ekstensi.

## 11. Fase 2 (Support & Data Center)
Tambahan:
- Extend data-attributes (support & dc) normalisasi lowercase.
- Generate opsi secara server: lewat prop list unik (lebih hemat daripada parse DOM dua kali). Atau fallback: derive client-side sekali jika prop tidak dikirim.
- Multi-select: state Set, UI checkbox.
- Update URL parameter multi-value (comma separated). Encode dengan `encodeURIComponent` jika ada spasi.
- Aksesibilitas: group per kategori dengan `fieldset`.
- Potensi deferred hydration: jika logic >5KB, pertimbangkan pindah ke island framework mini + `client:idle` (sesuai docs: tunda hydration sampai idle untuk UI non-kritis).

## 12. Testing Checklist
| Area | Kasus | Status |
|------|-------|--------|
| Promo filter | Aktifkan & hanya tampil kartu dengan promo |  |
| Promo filter | Nonaktifkan & semua kembali |  |
| URL param | `?promo=1` preload aktif |  |
| Pagination | Tersembunyi saat filter aktif |  |
| Counter | Nilai benar setelah filter toggle |  |
| No result | Tampilkan pesan ketika 0 |  |
| Reset | Menghapus state & restore |  |
| A11y | Navigasi keyboard & fokus tetap logis |  |
| Perf | applyFilters < 5ms untuk 100 card (profil devtools) |  |

## 13. Performa
- O(N) loop ringan; hindari reflow.
- Tidak menambah framework hydration → sesuai prinsip Astro zero-JS baseline.
- Script < 2KB target.
- Tree-shake: gunakan vanilla bukan utility besar.

## 14. Backlog / Future Improvements
- Server-side filter param untuk SEO (khusus promo): pre-filter saat `getStaticPaths` + query param canonical handling.
- Island konversi (Preact) bila kompleksitas interaksi naik (drag, fuzzy search) dengan `client:idle`.
- Virtualization bila N > 500 (kemungkinan kecil).
- Custom directive (tidak prioritas) misal `client:first-interaction` jika ingin menunda load sampai ada interaksi user (dapat dibuat via `addClientDirective`).
- Data center search input + highlight.
- Persist state di URL + sessionStorage.

## 15. Risiko & Mitigasi
| Risiko | Mitigasi |
|--------|----------|
| JS bloat seiring penambahan fitur | Audit bundle size; migrasi ke island terpisah + lazy | 
| Reflow karena manipulasi style inline | Gunakan class toggle `.hidden` | 
| Duplikasi parsing DOM | Cache array cards & dataset parse sekali | 
| Aksesibilitas menurun saat banyak filter | Gunakan fieldset/legend dan aria-live | 
| URL param edge (karakter khusus) | `encodeURIComponent` saat serialize | 
| SEO canonical duplikat (server filtering nanti) | Tambah canonical tanpa query param | 

## 16. Estimasi Waktu
| Task | Estimasi |
|------|----------|
| Fase 1 implement | 1.5 - 2 jam |
| Review & test | 30 mnt |
| Fase 2 (support + dc) | 2 - 3 jam |

## 17. Referensi Best Practice (Context7 Astro Docs)
- Hydration strategies: gunakan `client:load` hanya bila perlu interaktivitas segera; saat ini tidak diperlukan (komponen Astro + script). (Ref: Client Directives docs)
- Pertimbangkan `client:idle` untuk fitur non-kritis (filter lanjutan) agar waktu interaksi awal cepat.
- Minimalkan JS: prinsip Astro "Ship Less JavaScript" → inline script modular kecil.
- Hindari akses `window/document` di bagian server script; semua DOM code di `<script>` client (Ref: Troubleshooting docs tentang browser API).
- Custom client directive tidak diperlukan saat ini (overhead konfigurasi) (Ref: addClientDirective API) → keep sederhana.

## 18. Persetujuan
Silakan review versi revisi (best practice Astro diterapkan). Jika disetujui → lanjut implementasi Fase 1.

-- END PLAN --
