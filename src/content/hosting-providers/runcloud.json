{"name": "RunCloud", "slug": "runcloud", "displayName": "RunCloud", "logo": "https://cdn.penasihathosting.com/hosting/RC-Logo-compact.webp", "website": "https://runcloud.io/", "description": "RunCloud adalah panel kontrol berbasis web untuk manajemen server cloud PHP, WordPress, Laravel dan aplikasi lainnya, dengan workflow Git, monitoring, dan backup otomatis.", "longDescription": "RunCloud adalah solusi manajemen server modern yang dirancang untuk memudahkan developer dan tim <PERSON>ps dalam mengelola VPS atau server dedicated dari penyedia cloud seperti DigitalOcean, Linode, AWS, dan Google Cloud. Dengan antarmuka berbasis web, pengguna dapat melakukan instalasi stack web server (Nginx, Apache, PHP, MySQL), mengelola domain, SSL, dan deployment aplikasi tanpa harus mengetik baris perintah.\n\nFitur unggulannya mencakup staging WordPress, backup otomatis, sistem caching WordPress (RunCache), pemantauan server real-time, serta integrasi Git dengan webhook untuk deployment otomatis. RunCloud juga mendukung manajemen tim dan kolaborasi, menjadikannya pilihan yang ideal untuk agensi kecil, developer profesional, dan pengguna teknis yang ingin efisiensi tanpa kehilangan kendali penuh atas infrastruktur server.", "company": {"name": "RunCloud", "founded": 2017, "headquarters": ["Malaysia"], "address": "Kuala Lumpur, Malaysia", "supportChannels": ["email", "ticket"]}, "categories": ["cloud-management-platform"], "badges": [], "features": [{"name": "Free Trial", "icon": "tabler:discount-check"}, {"name": "One‑click WordPress Install", "icon": "tabler:rocket"}, {"name": "Git Deployment & Webhook", "icon": "tabler:git-branch"}, {"name": "Server Monitoring", "icon": "tabler:monitor"}, {"name": "Automated Backups", "icon": "tabler:database-backup"}, {"name": "Staging Environment", "icon": "tabler:flask"}, {"name": "Team Management", "icon": "tabler:users-group"}, {"name": "Multiple PHP Versions", "icon": "tabler:code"}, {"name": "SSL Automation", "icon": "tabler:shield-check"}, {"name": "Run<PERSON>ache (WP Caching)", "icon": "tabler:speedometer"}, {"name": "Email/Telegram/<PERSON>lack <PERSON>s", "icon": "tabler:bell"}], "datacenters": [], "pricing": {"startingPrice": 153000, "currency": "IDR", "plans": [{"name": "Essentials (Basic)", "price": 153000, "period": "month", "features": ["1 server managed", "Unlimited web apps", "One-click SSL", "Git deployment"], "isPopular": true}, {"name": "Professional", "price": 323000, "period": "month", "features": ["50 servers", "Cloning & staging", "Custom NGINX config"], "isPopular": false}, {"name": "Business", "price": 785000, "period": "month", "features": ["100 servers", "Team collaboration", "ModSecurity WAF", "Atomic deployment"], "isPopular": false}, {"name": "Enterprise", "price": 6783000, "period": "month", "features": ["500 servers", "API Pro", "Workspace Pro seats"], "isPopular": false}], "promoCode": "", "promoDescription": ""}, "gallery": ["https://cdn.penasihathosting.com/hosting/review-runcloud.webp"], "featuredImage": "", "tier": "basic", "modifiedAt": "2025-08-05T00:00:00Z", "isActive": true, "isFeatured": false}