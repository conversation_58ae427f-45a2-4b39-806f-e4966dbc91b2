{"name": "CloudPanel.io", "slug": "cloudpanel", "displayName": "CloudPanel", "logo": "https://cdn.penasihathosting.com/hosting/cloudpanel-logo.webp", "website": "https://www.cloudpanel.io/", "description": "CloudPanel adalah control panel hosting gratis berbasis web untuk server Debian dan Ubuntu dengan teknologi stack modern dan performa tinggi.", "longDescription": "CloudPanel adalah control panel hosting gratis yang dibangun dengan teknologi stack modern dan lightweight untuk memberikan performa maksimal dalam manajemen server. Platform ini dirancang khusus untuk server Debian dan Ubuntu, menyediakan antarmuka web yang intuitif untuk mengelola website, database, SSL certificate, dan berbagai layanan server lainnya tanpa perlu menguasai command line. CloudPanel mendukung instalasi otomatis untuk WordPress, aplikasi PHP, Node.js, Python, dan static HTML dengan Installation Wizard yang mudah digunakan.\n\nDengan pengalaman lebih dari satu dekade dalam managed hosting, CloudPanel menawarkan fitur enterprise-grade seperti multi-user management, automated backup ke cloud provider, firewall management, dan monitoring real-time untuk CPU, memory, dan disk usage. Platform ini mendukung PHP versi terbaru (7.1 hingga 8.4), MySQL/MariaDB, Redis, dan dilengkapi dengan integrasi Cloudflare untuk keamanan dan performa tambahan. CloudPanel cocok untuk developer, web designer, dan administrator yang membutuhkan control panel powerful namun gratis tanpa biaya lisensi.", "company": {"name": "CloudPanel", "founded": 2019, "headquarters": ["Germany"], "address": "Germany", "supportChannels": ["documentation", "community"]}, "categories": ["cloud-management-platform"], "badges": [{"type": "verified", "label": "Open Source"}], "features": [{"name": "100% <PERSON><PERSON><PERSON>", "icon": "tabler:gift"}, {"name": "Installation Wizard (WordPress, PHP, Node.js)", "icon": "tabler:rocket"}, {"name": "SSL Let's Encrypt Otomatis", "icon": "tabler:shield-check"}, {"name": "File Manager dengan Drag & Drop", "icon": "tabler:folder"}, {"name": "Multi-User & Role Management", "icon": "tabler:users-group"}, {"name": "Real-time Monitoring Dashboard", "icon": "tabler:activity"}, {"name": "Automated Cloud Backup", "icon": "tabler:cloud-upload"}, {"name": "Cloudflare Integration", "icon": "tabler:cloud"}, {"name": "PHP 7.1 - 8.4 Support", "icon": "tabler:code"}, {"name": "MySQL/MariaDB & phpMyAdmin", "icon": "tabler:database"}, {"name": "SSH/FTP Management", "icon": "tabler:terminal"}, {"name": "Firewall & Security Features", "icon": "tabler:shield"}, {"name": "Cron Jobs Scheduler", "icon": "tabler:clock"}, {"name": "Vhost Editor (NGINX)", "icon": "tabler:settings"}, {"name": "CLI Automation Tools", "icon": "tabler:terminal-2"}], "datacenters": [], "pricing": {"startingPrice": 0, "currency": "IDR", "plans": [{"name": "CloudPanel Free", "price": 0, "period": "month", "features": ["Unlimited Sites & Domains", "Unlimited Databases", "All Features Included", "Community Support", "Open Source", "Self-hosted Installation", "No License Fees", "Regular Updates"], "isPopular": true}], "promoCode": "", "promoDescription": "Completely free forever"}, "systemRequirements": {"os": ["Debian 11", "Debian 12", "Ubuntu 20.04 LTS", "Ubuntu 22.04 LTS", "Ubuntu 24.04 LTS"], "minRam": "1 GB RAM", "minStorage": "10 GB", "architecture": ["x86_64", "ARM64"]}, "supportedTechnologies": {"webServers": ["NGINX"], "phpVersions": ["7.1", "7.2", "7.3", "7.4", "8.0", "8.1", "8.2", "8.3", "8.4"], "databases": ["MySQL 5.5-8.0", "MariaDB 10.3-11.4"], "applicationFrameworks": ["WordPress", "<PERSON><PERSON>", "Symfony", "Node.js 16-20 LTS", "Python 3"], "caching": ["Redis", "Memca<PERSON>"], "ssl": ["Let's Encrypt", "Custom SSL"]}, "gallery": ["https://cdn.penasihathosting.com/hosting/review-cloudpanel.webp"], "featuredImage": "", "tier": "verified", "affiliateLink": "https://penasihathosting.com/go/cloudpanel", "modifiedAt": "2025-08-05T00:00:00Z", "isActive": true, "isFeatured": true}