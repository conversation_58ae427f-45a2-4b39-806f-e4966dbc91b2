import { z, defineCollection } from 'astro:content';
import { glob } from 'astro/loaders';

const metadataDefinition = () =>
  z
    .object({
      title: z.string().optional(),
      ignoreTitleTemplate: z.boolean().optional(),

      canonical: z.string().url().optional(),

      robots: z
        .object({
          index: z.boolean().optional(),
          follow: z.boolean().optional(),
        })
        .optional(),

      description: z.string().optional(),

      openGraph: z
        .object({
          url: z.string().optional(),
          siteName: z.string().optional(),
          images: z
            .array(
              z.object({
                url: z.string(),
                width: z.number().optional(),
                height: z.number().optional(),
              })
            )
            .optional(),
          locale: z.string().optional(),
          type: z.string().optional(),
        })
        .optional(),

      twitter: z
        .object({
          handle: z.string().optional(),
          site: z.string().optional(),
          cardType: z.string().optional(),
        })
        .optional(),

      noticeType: z.enum(['fyi', 'update']).nullable().optional(),
      noticeDate: z.union([z.date(), z.string()]).optional(),

      // Featured post property
      featured: z.boolean().optional(),
    })
    .optional();

const postCollection = defineCollection({
  loader: glob({ pattern: ['*.md', '*.mdx'], base: 'src/data/post' }),
  schema: z.object({
    publishDate: z.date().optional(),
    updateDate: z.date().optional(),
    draft: z.boolean().optional(),

    title: z.string(),
    excerpt: z.string().optional(),
    image: z.string().optional(),

    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    author: z.string().optional(),

    metadata: metadataDefinition(),
  }),
});

// Define the guide collection schema
const guideCollection = defineCollection({
  type: 'data',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    slug: z.string(),
    chapters: z.array(
      z.object({
        title: z.string(),
        slug: z.string(),
        excerpt: z.string().optional(),
      })
    ),
  }),
});

// Define the kamus collection schema
const kamusCollection = defineCollection({
  loader: glob({ pattern: ['*.md', '*.mdx'], base: 'src/content/kamus' }),
  schema: z.object({
    title: z.string(),
    description: z.string(),
    excerpt: z.string().optional(),
    keywords: z.string(),
    author: z.string().optional(),
    publishedAt: z.union([z.date(), z.string()]),
    updateDate: z.union([z.date(), z.string()]).optional(),
    tags: z.array(z.string()).optional(),
    
    metadata: metadataDefinition(),
  }),
});

// Define the wiki collection schema for individual WikiHosting pages
const wikiCollection = defineCollection({
  loader: glob({ pattern: ['*.md', '*.mdx'], base: 'src/content/wiki' }),
  schema: z.object({
    title: z.string(),
    publishDate: z.union([z.date(), z.string()]).optional(),
    updateDate: z.union([z.date(), z.string()]).optional(),
    draft: z.boolean().optional(),
    
    excerpt: z.string().optional(),
    image: z.string().optional(),
    
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    author: z.string().optional(),
    difficulty: z.enum(['Pemula', 'Menengah', 'Advanced']).optional(),
    readingTime: z.number().optional(),
    
    // Wiki-specific fields
    targetKeywords: z.array(z.string()).optional(),
    relatedTerms: z.array(z.string()).optional(),
    
    metadata: metadataDefinition(),
  }),
});

// Define the wikihosting collection schema for comprehensive WikiHosting articles
const wikihostingCollection = defineCollection({
  loader: glob({ pattern: ['*.md', '*.mdx'], base: 'src/content/wikihosting' }),
  schema: z.object({
    title: z.string(),
    publishDate: z.union([z.date(), z.string()]).optional(),
    updateDate: z.union([z.date(), z.string()]).optional(),
    draft: z.boolean().optional(),
    
    description: z.string(),
    excerpt: z.string().optional(),
    image: z.string().optional(),
    
    // WikiHosting specific fields
    category: z.enum(['Core Concepts', 'Security Terms', 'Performance Terms', 'Technical Terms', 'Business Terms', 'Advanced Concepts']).optional(),
    phase: z.enum(['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4', 'Phase 5']).optional(),
    priority: z.enum(['High', 'Medium', 'Low']).optional(),
    difficulty: z.enum(['Pemula', 'Menengah', 'Advanced']).optional(),
    readingTime: z.number().optional(),
    wordCount: z.number().optional(),
    
    // SEO and content fields
    targetKeywords: z.array(z.string()),
    relatedTerms: z.array(z.string()).optional(),
    tags: z.array(z.string()).optional(),
    author: z.string().optional(),
    
    // Content type classification
    contentType: z.enum(['Article', 'Comparison', 'Tutorial', 'Guide']).optional(),
    schemaType: z.array(z.enum(['Article', 'FAQ', 'HowTo', 'Comparison'])).optional(),
    
    // FAQ support
    faq: z.array(z.object({
      title: z.string(),
      description: z.string(),
      icon: z.string().optional(),
    })).optional(),
    
    metadata: metadataDefinition(),
  }),
});

// Define the hosting providers collection schema
const hostingProvidersCollection = defineCollection({
  type: 'data',
  schema: z.object({
    // Basic Info
    name: z.string(),
    slug: z.string(),
    displayName: z.string().optional(),
    logo: z.string(),
    website: z.string().url(),
    description: z.string(),
    longDescription: z.string().optional(),

    // Company Details
    company: z.object({
      name: z.string(),
      founded: z.number().optional(),
      headquarters: z.array(z.string()),
      address: z.string(),
      supportChannels: z.array(z.enum(['live-chat', 'ticket', 'phone', 'email', 'documentation', 'community', 'whatsapp'])),
    }),

    // Categorization
    categories: z.array(z.string()),
    controlPanels: z.array(z.enum(['cpanel', 'directadmin', 'plesk'])).optional(),
    badges: z.array(z.object({
      type: z.enum(['recommended', 'sponsored', 'promoted', 'verified']),
      label: z.string(),
    })),

    // Features & Infrastructure
    features: z.array(z.object({
      name: z.string(),
      icon: z.string().optional(),
      description: z.string().optional(),
    })),
    datacenters: z.array(z.object({
      location: z.string(),
      country: z.string(),
      flag: z.string().optional(),
    })),

    // Pricing
    pricing: z.object({
      startingPrice: z.number(),
      currency: z.string().default('IDR'),
      plans: z.array(z.object({
        name: z.string(),
        price: z.number(),
        period: z.string(), // Changed from enum to string for flexibility
        features: z.array(z.string()),
        isPopular: z.boolean().optional(),
        promoCode: z.string().optional(),
        promoDescription: z.string().optional(),
      })),
      promoCode: z.string().optional(),
      promoDescription: z.string().optional(),
    }),

    // Media
    gallery: z.array(z.string()),
    featuredImage: z.string().optional(),

    // Business
    tier: z.enum(['basic', 'verified', 'sponsored', 'recommended']),
    affiliateLink: z.string().url().optional(),

    // Meta
    isActive: z.boolean().default(true),
    isFeatured: z.boolean().default(false),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
    modifiedAt: z.union([z.date(), z.string()]).optional(),
  }),
});

// Define the hosting categories collection schema
const hostingCategoriesCollection = defineCollection({
  loader: glob({ pattern: ['*.md', '*.mdx'], base: 'src/content/hosting-categories' }),
  schema: z.object({
    title: z.string(),
    description: z.string(),
    icon: z.string(),
    providerCount: z.number().optional().default(0), // Now optional and defaults to 0
    featured: z.boolean().default(false),
    listTitle: z.string().optional(), // Dynamic title for provider list
    seoTitle: z.string().optional(),
    seoDescription: z.string().optional(),

    metadata: metadataDefinition(),
  }),
});

export const collections = {
  post: postCollection,
  guide: guideCollection,
  kamus: kamusCollection,
  wiki: wikiCollection,
  wikihosting: wikihostingCollection,
  'hosting-providers': hostingProvidersCollection,
  'hosting-categories': hostingCategoriesCollection,
};
