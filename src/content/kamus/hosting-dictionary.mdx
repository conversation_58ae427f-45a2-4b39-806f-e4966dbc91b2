---
title: "Kamus Hosting A-Z Indonesia: 200+ Istilah Web Hosting Dijelaskan Lengkap"
publishedAt: "2025-07-17"
description: "Kamus hosting terlengkap dalam bahasa Indonesia. 200+ istilah web hosting dijelaskan dengan bahasa sederhana dan analogi mudah dipahami untuk pemula."
excerpt: "Bingung dengan istilah-istilah hosting? Kamus hosting A-Z ini menjelaskan 200+ istilah web hosting dengan bahasa sederhana dan contoh yang mudah dipahami. Dari Apache hingga Zone File, semua dijelaskan lengkap!"
keywords: "kamus hosting, istilah hosting, hosting dictionary indonesia, istilah web hosting, glosarium hosting, hosting glossary indonesia, arti istilah hosting, pengertian hosting, hosting terminology indonesia"
author: "Willya Randika"
tags: ["panduan", "hosting", "kamus", "pemula", "referensi"]
metadata:
  title: "Kamus Hosting A-Z Indonesia: 200+ Istilah Web Hosting Dijelaskan Lengkap"
  description: "Kamus hosting terlengkap dalam bahasa Indonesia. 200+ istilah web hosting dijelaskan dengan bahasa sederhana dan analogi mudah dipahami untuk pemula."
  robots:
    index: true
    follow: true
  openGraph:
    siteName: "PenasihatHosting"
    images:
      - url: "https://img.penasihathosting.com/2025/juli/kamus-hosting-indonesia.webp"
        width: 1200
        height: 628
    type: "article"
---

<div class="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-8">
  <div class="flex items-start gap-3">
    <div class="flex-shrink-0">
      <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
      </svg>
    </div>
    <div>
      <p class="text-blue-800 dark:text-blue-200 font-medium mt-0">💡 Tips Penggunaan</p>
      <p class="text-blue-700 dark:text-blue-300 text-sm mt-1">Gunakan fitur pencarian di atas untuk menemukan istilah dengan cepat, atau klik huruf di navigasi untuk melompat ke bagian tertentu. Semua istilah dijelaskan dengan analogi sederhana yang mudah dipahami.</p>
    </div>
  </div>
</div>

## <span id="section-a">A</span>

### **Add-on Domain** <a href="/wikihosting/add-on-domain/" class="text-blue-600 hover:text-red-600 no-underline text-sm">⤷</a>
Domain tambahan yang bisa Anda hosting dalam satu akun hosting. Ibarat Anda punya rumah besar, lalu membangun rumah kecil di halaman yang sama dengan alamat berbeda.


### **Apache**
Software web server paling populer di dunia. Ibarat penjaga toko yang melayani pengunjung website Anda. Apache bertugas mengirimkan file website ke browser pengunjung.


### **API (Application Programming Interface)**
Cara untuk aplikasi berkomunikasi satu sama lain. Seperti pelayan restoran yang menjadi perantara antara Anda (pelanggan) dengan dapur (server).


### **Auto-Installer**
Fitur untuk install aplikasi (WordPress, Joomla, dll) dengan sekali klik. Seperti memasang aplikasi di HP - tinggal klik install, selesai!


### **Autoresponder**
Email balasan otomatis. Seperti pesan "sedang cuti" yang otomatis terkirim saat ada yang email Anda.


### **A Record**
Bagian dari DNS yang menghubungkan domain ke IP address. Ibarat nomor rumah yang menunjukkan lokasi pasti rumah Anda.


### **Addon**
Fitur tambahan yang bisa dipasang. Seperti aksesori HP - case, tempered glass, dll.

### **Alias**
Nama lain untuk domain atau email. Seperti nama panggilan - "Budi" bisa dipanggil "Bud" atau "Mas Budi".

### **Allocated Resources**
Sumber daya yang dialokasikan khusus untuk Anda. Seperti jatah makanan di prasmanan - sudah ditentukan porsinya.

### **Anonymous FTP**
FTP yang bisa diakses tanpa username/password. Seperti WiFi gratis tanpa password di kafe.


## <span id="section-b">B</span>

### **Bandwidth**
Jumlah data yang bisa ditransfer dalam periode tertentu. Ibarat kuota internet bulanan Anda - ada batasnya per bulan.


### **Backup**
Salinan data website untuk jaga-jaga. Seperti fotokopi KTP - kalau yang asli hilang, masih ada cadangan.


### **Blog**
Website yang berisi artikel/tulisan, biasanya diupdate berkala. Seperti diary online yang bisa dibaca orang lain.

### **Browser**
Aplikasi untuk browsing internet (Chrome, Firefox, Safari). Ibarat mobil yang Anda pakai untuk "jalan-jalan" di internet.

### **Bounce Rate**
Persentase pengunjung yang langsung pergi tanpa melihat halaman lain. Seperti pengunjung toko yang masuk terus langsung keluar tanpa lihat-lihat.

### **Bot**
Program otomatis yang menjalankan tugas tertentu. Seperti robot yang bekerja sendiri tanpa perlu disuruh terus-menerus.

### **Brute Force Attack**
Serangan dengan mencoba semua kombinasi password. Seperti maling yang coba semua kunci sampai dapat yang pas.


### **Blacklist**
Daftar IP/email yang diblokir. Seperti daftar orang yang dilarang masuk ke toko Anda.

### **Bandwidth Limit**
Batas maksimal bandwidth yang bisa digunakan. Seperti batas kecepatan di jalan tol.

### **Binary**
Sistem angka komputer (0 dan 1). Bahasa dasar komputer - seperti ABC untuk manusia.


## <span id="section-c">C</span>

### **cPanel**
Control panel hosting paling populer. Ibarat dashboard mobil - semua kontrol ada di satu tempat yang mudah diakses.


### **Cache**
Penyimpanan sementara untuk mempercepat akses. Seperti kulkas di minimarket - barang yang sering dibeli ditaruh depan supaya cepat diambil.


### **CDN (Content Delivery Network)**
Jaringan server di berbagai lokasi untuk mempercepat loading. Ibarat cabang toko di berbagai kota - pembeli bisa beli di cabang terdekat.


### **SSL Certificate**
Sertifikat keamanan website (https). Seperti gembok di pintu - menunjukkan website Anda aman.


### **Cloud Hosting**
Hosting yang menggunakan banyak server. Ibarat menyimpan barang di beberapa gudang - kalau satu rusak, masih ada cadangan.


### **CPU (Central Processing Unit)**
Otak dari server. Semakin kuat CPU, semakin cepat website Anda diproses.

### **Cron Job**
Tugas terjadwal otomatis. Seperti alarm yang bunyi setiap jam tertentu tanpa perlu diset ulang.

### **CMS (Content Management System)**
Sistem untuk mengelola konten website (WordPress, Joomla). Seperti Microsoft Word untuk website - mudah edit tanpa coding.

### **Cookie**
File kecil yang menyimpan info pengunjung. Seperti cap tangan di tempat hiburan - untuk ingat Anda sudah pernah masuk.

### **CSS (Cascading Style Sheets)**
Kode untuk mengatur tampilan website. Ibarat baju dan makeup - membuat website cantik dan menarik.


## <span id="section-d">D</span>

### **Domain**
Alamat website Anda (contoh: google.com). Seperti alamat rumah di internet - orang bisa kunjungi dengan mengetik alamat ini.


### **DNS (Domain Name System)**
Sistem yang menerjemahkan domain ke IP. Ibarat buku telepon internet - cari nama, dapat nomornya.


### **Database**
Tempat penyimpanan data terstruktur. Seperti lemari arsip - semua data tersusun rapi dan mudah dicari.

### **Dedicated Server**
Server yang khusus untuk Anda sendiri. Ibarat rumah pribadi - semua fasilitas hanya untuk Anda.

### **Disk Space**
Ruang penyimpanan di server. Seperti kapasitas harddisk HP Anda - untuk simpan foto, video, dll.

### **Downtime**
Waktu ketika website tidak bisa diakses. Seperti toko tutup - pengunjung tidak bisa masuk.

### **DDoS (Distributed Denial of Service)**
Serangan dengan membanjiri traffic. Ibarat ratusan orang pura-pura mau beli, tapi cuma bikin macet toko.


### **Data Center**
Gedung tempat server disimpan. Seperti gudang besar berisi ribuan komputer server.

### **Domain Registrar**
Perusahaan tempat daftar domain. Seperti kantor catatan sipil untuk nama domain.

### **Dedicated IP**
IP address khusus untuk website Anda. Seperti nomor telepon pribadi - tidak sharing dengan orang lain.


## <span id="section-e">E</span>

### **Email Hosting**
Layanan hosting khusus untuk email. Bisa pakai email @namadomainanda.com, terlihat lebih profesional.


### **Error 404**
Pesan error halaman tidak ditemukan. Seperti alamat rumah yang tidak ada - kurir bingung mau antar ke mana.

### **E-commerce**
Website untuk jual beli online. Toko online di internet - bisa jual apa saja 24/7.

### **Encryption**
Proses mengamankan data. Seperti kunci brankas - hanya yang punya kunci bisa buka.

### **Email Forwarder**
Meneruskan email ke alamat lain. Seperti pos yang diteruskan ke alamat baru Anda.

### **Exit Rate**
Persentase pengunjung keluar dari halaman tertentu. Pintu keluar mana yang paling sering dipakai pengunjung.

### **Expiration Date**
Tanggal kadaluarsa domain/hosting. Seperti masa berlaku SIM - harus diperpanjang sebelum expired.

### **Email Filter**
Menyaring email masuk. Seperti satpam yang seleksi tamu - yang mencurigakan tidak boleh masuk.

### **Error Log**
Catatan error yang terjadi. Seperti buku catatan dokter - mencatat semua "penyakit" website.

### **Extension**
Ekstensi domain (.com, .id, .net). Seperti kode plat nomor kendaraan - B untuk Jakarta, D untuk Bandung.


## <span id="section-f">F</span>

### **FTP (File Transfer Protocol)**
Cara upload file ke server. Seperti kurir untuk kirim file dari komputer ke server hosting.

### **Firewall**
Sistem keamanan jaringan. Ibarat pagar rumah - menghalangi yang tidak diinginkan masuk.


### **File Manager**
Tool untuk mengelola file di hosting. Seperti Windows Explorer tapi untuk file di server.

### **Framework**
Kerangka kerja pemrograman. Seperti blueprint rumah - sudah ada polanya, tinggal sesuaikan kebutuhan.

### **Frontend**
Bagian website yang dilihat pengunjung. Seperti etalase toko - yang pertama dilihat customer.

### **Full Backup**
Backup lengkap semua data. Seperti pindah rumah - semua barang dibawa, tidak ada yang tertinggal.

### **File Permission**
Pengaturan akses file. Seperti kunci kamar - tentukan siapa boleh masuk, lihat, atau ubah.

### **Favicon**
Icon kecil di tab browser. Seperti logo kecil di kartu nama - identitas mini website Anda.

### **Form**
Formulir di website. Seperti formulir pendaftaran - pengunjung isi data, kirim ke Anda.

### **Footer**
Bagian bawah website. Seperti catatan kaki buku - info tambahan dan link penting.



## <span id="section-g">G</span>

### **GB (Gigabyte)**
Satuan ukuran data (1 GB = 1.000 MB). Seperti liter untuk ukuran air - untuk ukur kapasitas penyimpanan.

### **GUI (Graphical User Interface)**
Tampilan visual yang user-friendly. Lebih enak lihat gambar dan tombol daripada ketik kode kan?

### **Gzip**
Kompresi file untuk mempercepat loading. Seperti vacuum bag untuk baju - sama isinya tapi lebih kecil ukurannya.

### **Google Analytics**
Tool analisis pengunjung website. Seperti CCTV toko - tahu berapa pengunjung, dari mana, lihat apa saja.

### **Gateway**
Pintu masuk ke jaringan. Seperti gerbang tol - semua harus lewat sini untuk masuk.

### **Git**
Sistem version control. Seperti save game - bisa balik ke versi sebelumnya kalau ada masalah.

### **Global DNS**
DNS yang berlaku di seluruh dunia. Seperti nomor telepon internasional - bisa dihubungi dari mana saja.

### **Green Hosting**
Hosting ramah lingkungan. Menggunakan energi terbarukan - baik untuk bumi kita.

### **Grid Hosting**
Hosting dengan teknologi grid computing. Seperti gotong royong - beban kerja dibagi ke banyak server.

### **Guest User**
Pengguna tamu tanpa login. Seperti tamu di rumah - boleh lihat-lihat tapi terbatas aksesnya.


## <span id="section-h">H</span>

### **Hosting**
Tempat menyimpan file website di internet. Ibarat menyewa ruko untuk buka toko online Anda.

### **HTML (HyperText Markup Language)**
Bahasa dasar pembuatan website. Seperti batu bata untuk membangun rumah - fondasi website.

### **HTTP/HTTPS**
Protokol transfer data website. HTTP seperti surat biasa, HTTPS seperti surat tercatat - lebih aman.

### **Htaccess**
File konfigurasi Apache. Seperti aturan rumah - tentukan siapa boleh masuk, ke mana, bagaimana.

### **Header**
Bagian atas website. Seperti kepala surat - berisi logo, menu navigasi, info penting.

### **Hotlink Protection**
Mencegah website lain pakai gambar Anda. Seperti watermark foto - orang lain tidak bisa curi dan pakai.

### **Host**
Server tempat website disimpan. Rumah digital untuk website Anda.

### **Hostname**
Nama server hosting. Seperti nama gedung apartemen - untuk identifikasi server mana.

### **HTTP Status Code**
Kode status komunikasi browser-server. Seperti kode pos - 200 = sukses, 404 = tidak ketemu, dll.

### **Hybrid Server**
Gabungan dedicated dan cloud server. Seperti rumah dengan ruang pribadi dan ruang bersama.


## <span id="section-i">I</span>

### **IP Address**
Alamat unik setiap device di internet. Seperti nomor KTP - setiap orang punya nomor unik.

### **Inode**
Jumlah file dan folder di hosting. Seperti jumlah barang di gudang - ada batas maksimalnya.

### **IMAP (Internet Message Access Protocol)**
Protokol untuk akses email. Email tetap di server, bisa diakses dari mana saja - seperti cloud storage.

### **Inbound**
Data yang masuk ke server. Seperti barang masuk ke gudang.

### **Index**
Halaman utama website (index.html). Seperti halaman depan buku - yang pertama dibuka.

### **Installation**
Proses instalasi aplikasi/software. Seperti pasang aplikasi di HP - ikuti langkahnya sampai selesai.

### **Interface**
Tampilan untuk interaksi user. Seperti dashboard mobil - tempat Anda kontrol semuanya.

### **Internet**
Jaringan komputer global. Jalan raya informasi yang menghubungkan seluruh dunia.

### **IP Blocking**
Blokir akses dari IP tertentu. Seperti daftar orang yang dilarang masuk toko.

### **ISP (Internet Service Provider)**
Penyedia layanan internet. Seperti Telkom, Indihome - yang kasih Anda akses internet.


## <span id="section-j">J</span>

### **JavaScript**
Bahasa pemrograman untuk interaktivitas website. Membuat website lebih hidup - popup, animasi, dll.

### **Joomla**
CMS populer selain WordPress. Platform lain untuk bikin website tanpa coding.

### **JSON**
Format pertukaran data. Seperti format dokumen standar - mudah dibaca manusia dan mesin.

### **jQuery**
Library JavaScript populer. Seperti toolkit - mempermudah pemrograman JavaScript.

### **Junk Mail**
Email sampah/spam. Seperti brosur tidak penting yang sering masuk ke rumah.

### **Java**
Bahasa pemrograman (beda dengan JavaScript). Untuk aplikasi yang lebih kompleks.

### **Job Queue**
Antrian tugas server. Seperti antrian di bank - dilayani satu per satu sesuai urutan.


## <span id="section-k">K</span>

### **Kernel**
Inti sistem operasi. Seperti otak manusia - mengatur semua fungsi vital.

### **Keyword**
Kata kunci untuk SEO. Kata yang diketik orang di Google untuk menemukan website Anda.

### **Knowledge Base**
Kumpulan artikel bantuan. Seperti buku manual - jawaban untuk pertanyaan umum.

### **KVM (Kernel-based Virtual Machine)**
Teknologi virtualisasi. Membagi satu server jadi beberapa server virtual.

### **Keep-Alive**
Koneksi tetap terbuka. Seperti telepon yang tidak ditutup - siap untuk komunikasi lanjutan.

### **Kilobyte (KB)**
Satuan data (1.024 byte). Satuan kecil - 1 foto WhatsApp biasanya ratusan KB.

### **Key**
Kunci enkripsi/autentikasi. Seperti kunci rumah digital - untuk akses yang aman.

### **Kill Process**
Menghentikan proses yang berjalan. Seperti force close aplikasi yang hang di HP.


## <span id="section-l">L</span>

### **Linux**
Sistem operasi server populer. Seperti Android untuk server - gratis dan powerful.

### **Load Balancer**
Pembagi beban traffic. Seperti banyak kasir di supermarket - biar tidak antri di satu kasir saja.

### **Log File**
File catatan aktivitas. Seperti buku harian server - mencatat semua yang terjadi.

### **Laravel**
Framework PHP populer. Seperti template canggih untuk bikin aplikasi web.

### **Landing Page**
Halaman khusus untuk campaign. Seperti booth pameran - fokus jual satu produk.

### **Latency**
Delay waktu respons. Seperti jeda saat telepon internasional - makin jauh makin delay.

### **Link**
Tautan ke halaman lain. Seperti jembatan antar halaman website.

### **Live Chat**
Chat langsung di website. Seperti customer service online - siap bantu real-time.

### **Local Server**
Server di komputer sendiri. Untuk testing di "rumah" sebelum upload ke hosting.

### **Login**
Masuk ke sistem dengan username/password. Seperti buka kunci pintu dengan kunci yang tepat.


## <span id="section-m">M</span>

### **MySQL**
Sistem database populer. Seperti Excel tapi lebih canggih - untuk simpan data website.

### **Migration**
Pindah hosting/server. Seperti pindah rumah - semua barang (data) dipindahkan.

### **Malware**
Software jahat. Virus komputer - bisa rusak website Anda.

### **Meta Tags**
Tag HTML untuk SEO. Seperti label di barang - memberi tahu Google isi website Anda.

### **MB (Megabyte)**
Satuan data (1 MB = 1.024 KB). Ukuran file sedang - lagu MP3 biasanya 3-5 MB.

### **Mail Server**
Server khusus email. Kantor pos digital - mengatur pengiriman email.

### **Maintenance Mode**
Mode perbaikan website. Seperti toko tutup sementara untuk renovasi.

### **Mirror**
Salinan identik website/server. Seperti cermin - copy persis yang asli.

### **Module**
Bagian program yang bisa ditambah. Seperti LEGO - bisa pasang sesuai kebutuhan.

### **Monitoring**
Pemantauan server/website. Seperti CCTV - awasi terus kondisi website.


## <span id="section-n">N</span>

### **Nameserver**
Server yang mengatur DNS. Seperti operator telepon - menghubungkan nama domain ke server hosting.

### **Node**
Titik dalam jaringan. Seperti halte bus - tempat perhentian dalam perjalanan data.

### **Network**
Jaringan komputer. Seperti jalan yang menghubungkan rumah-rumah (komputer).

### **Nginx**
Web server alternatif Apache. Saingan Apache - lebih ringan dan cepat.

### **Notification**
Pemberitahuan sistem. Seperti notifikasi HP - kasih tahu ada yang penting.

### **NPM (Node Package Manager)**
Manager paket untuk Node.js. Seperti toko aplikasi untuk developer.

### **Null**
Nilai kosong/tidak ada. Seperti kotak kosong - ada tempatnya tapi tidak ada isinya.

### **NAT (Network Address Translation)**
Penerjemah alamat jaringan. Seperti resepsionis hotel - meneruskan tamu ke kamar yang tepat.

### **NAS (Network Attached Storage)**
Storage yang terhubung jaringan. Harddisk external yang bisa diakses banyak orang.

### **Nslookup**
Tool cek informasi DNS. Seperti cek nomor telepon di buku telepon.


## <span id="section-o">O</span>

### **Operating System (OS)**
Sistem operasi (Linux, Windows). Seperti fondasi rumah - menjalankan semua program.

### **Optimization**
Proses optimasi/percepatan. Seperti tune-up mobil - biar performa maksimal.

### **Outbound**
Data keluar dari server. Seperti barang keluar dari gudang.

### **Open Source**
Software gratis dan terbuka. Seperti resep masakan gratis - boleh dipakai dan dimodifikasi.

### **Overusage**
Penggunaan melebihi batas. Seperti tagihan listrik membengkak - pakai melebihi normal.

### **Online**
Terhubung ke internet. Seperti toko buka - siap melayani.

### **Offline**
Tidak terhubung internet. Seperti toko tutup - tidak bisa diakses.

### **OAuth**
Sistem login dengan akun lain. Login pakai Facebook/Google - tidak perlu bikin akun baru.

### **Object Cache**
Cache untuk data object. Simpan sementara data yang sering dipakai.

### **Override**
Menimpa pengaturan. Seperti aturan khusus yang mengalahkan aturan umum.


## <span id="section-p">P</span>

### **PHP**
Bahasa pemrograman web populer. Otak di balik WordPress dan website dinamis lainnya.

### **Plugin**
Tambahan fitur untuk CMS. Seperti aplikasi tambahan di HP - nambahin fungsi baru.

### **Parked Domain**
Domain yang "diparkir" tanpa website. Seperti tanah kosong yang sudah dibeli tapi belum dibangun.

### **Password**
Kata sandi untuk keamanan. Kunci digital - jangan sampai orang lain tahu!

### **POP3**
Protokol email lama. Download email ke komputer - email hilang dari server.

### **Port**
Nomor pintu komunikasi. Seperti nomor loket - port 80 untuk web, port 21 untuk FTP.

### **Proxy**
Perantara koneksi internet. Seperti calo - minta tolong dia yang akses, bukan langsung.

### **Python**
Bahasa pemrograman serbaguna. Bahasa favorit untuk AI dan data science.

### **Ping**
Test koneksi ke server. Seperti ketuk pintu - cek ada orang atau tidak.

### **Permission**
Hak akses file/folder. Seperti ijin masuk - siapa boleh lihat, edit, hapus.


## <span id="section-q">Q</span>

### **Query**
Permintaan data dari database. Seperti tanya ke pustakawan - "minta buku tentang masak".

### **Queue**
Antrian proses. Seperti antri di kasir - dilayani sesuai urutan.

### **Quota**
Batas penggunaan resource. Seperti jatah bensin - ada batas per bulan.

### **Quick Install**
Instalasi cepat aplikasi. One-click install - praktis tanpa ribet.

### **QoS (Quality of Service)**
Kualitas layanan jaringan. Prioritas jalur cepat - seperti jalur busway.


## <span id="section-r">R</span>

### **RAM (Random Access Memory)**
Memori untuk proses sementara. Seperti meja kerja - makin luas, makin banyak yang bisa dikerjakan sekaligus.

### **Root**
Level akses tertinggi. Seperti pemilik rumah - bisa akses semua ruangan.

### **Redirect**
Pengalihan ke halaman lain. Seperti plang "pindah alamat" - otomatis diarahkan ke alamat baru.

### **Restore**
Mengembalikan dari backup. Seperti memulihkan foto dari recycle bin.

### **Ruby**
Bahasa pemrograman. Populer untuk web app dengan framework Rails.

### **Registrar**
Tempat daftar domain. Seperti kantor catatan sipil untuk domain.

### **Response Time**
Waktu respon server. Berapa lama pelayan (server) melayani pesanan Anda.

### **Robots.txt**
File instruksi untuk search engine. Seperti rambu "dilarang masuk" untuk Google bot.

### **RSS**
Format untuk sindikasi konten. Seperti langganan koran - konten baru otomatis terkirim.

### **Round Robin DNS**
Distribusi beban ke beberapa server. Seperti kasir bergantian - biar tidak capek satu orang saja.


## <span id="section-s">S</span>

### **Server**
Komputer yang melayani website. Seperti pelayan restoran - melayani permintaan pengunjung.

### **Shared Hosting**
Hosting yang dipakai bersama. Seperti kos-kosan - satu rumah untuk beberapa penghuni.

### **SSL (Secure Sockets Layer)**
Enkripsi untuk keamanan. Seperti amplop segel - data aman dari intipan.

### **Subdomain**
Domain tambahan di bawah domain utama. Seperti nomor kamar di hotel - hotel.com/kamar101.

### **SSD (Solid State Drive)**
Storage super cepat. Seperti flash disk raksasa - lebih cepat dari harddisk biasa.

### **SMTP (Simple Mail Transfer Protocol)**
Protokol kirim email. Seperti kantor pos untuk email - atur pengiriman.

### **Spam**
Email/konten sampah. Seperti brosur tidak penting yang ganggu.

### **SQL**
Bahasa untuk database. Cara "ngobrol" dengan database.

### **Staging**
Website testing sebelum live. Seperti gladi resik sebelum pentas.

### **Static Website**
Website tanpa database. Seperti brosur digital - isinya tetap.


## <span id="section-t">T</span>

### **Traffic**
Jumlah pengunjung website. Seperti jumlah pengunjung toko fisik.

### **Theme**
Tema/template website. Seperti baju untuk website - ganti tema, ganti tampilan.

### **Transfer**
Pindah data/domain. Seperti pindah barang dari satu tempat ke tempat lain.

### **TLD (Top Level Domain)**
Ekstensi domain (.com, .net, .id). Seperti kode area telepon.

### **Two-Factor Authentication (2FA)**
Keamanan dua lapis. Seperti pintu dengan dua kunci - password + kode SMS.

### **Ticket**
Tiket bantuan support. Seperti nomor antrian customer service.

### **Terms of Service (ToS)**
Syarat dan ketentuan layanan. Aturan main yang harus disetujui.

### **Template**
Desain siap pakai. Seperti cetakan kue - tinggal pakai.

### **Thread**
Alur proses di server. Seperti jalur di jalan tol - bisa multiple.

### **Timeout**
Batas waktu respons. Seperti batas waktu jawab ujian - lewat waktu, gagal.


## <span id="section-u">U</span>

### **Upload**
Kirim file ke server. Seperti kirim paket ke gudang online Anda.

### **Uptime**
Waktu server hidup/aktif. Persentase waktu toko buka - 99.9% uptime = jarang tutup.

### **URL (Uniform Resource Locator)**
Alamat lengkap halaman web. Alamat lengkap dengan jalan dan nomor rumah.

### **Username**
Nama pengguna untuk login. Seperti nama di KTP digital Anda.

### **Unlimited**
Tanpa batas (tapi ada fair use). Seperti makan sepuasnya - ada batas kewajaran.

### **Update**
Pembaruan software/sistem. Seperti renovasi rumah - perbaiki yang lama, tambah yang baru.

### **Unix**
Sistem operasi server. Kakek moyangnya Linux.

### **Unzip**
Ekstrak file terkompresi. Buka koper yang divakum - kembalikan ke ukuran normal.

### **User Interface (UI)**
Tampilan untuk pengguna. Apa yang dilihat dan diklik user.

### **UTF-8**
Standar encoding karakter. Biar bisa tampilkan huruf dari berbagai bahasa.


## <span id="section-v">V</span>

### **VPS (Virtual Private Server)**
Server virtual pribadi. Seperti apartemen - punya ruang sendiri tapi dalam gedung yang sama.

### **Virus**
Program jahat. Bisa merusak website dan data Anda.

### **Virtual Host**
Host virtual di satu server. Beberapa website dalam satu server fisik.

### **Visitor**
Pengunjung website. Orang yang datang ke "toko online" Anda.

### **Version Control**
Kontrol versi kode. Seperti save game - bisa balik ke versi lama jika perlu.

### **Vulnerability**
Celah keamanan. Seperti jendela yang lupa dikunci - bisa dimasuki maling.

### **Validation**
Proses validasi data. Cek data sudah benar atau belum.

### **Variable**
Tempat simpan data sementara dalam program. Seperti sticky note untuk ingat sesuatu.

### **View**
Tampilan halaman. Apa yang dilihat pengunjung.

### **VPN (Virtual Private Network)**
Jaringan pribadi virtual. Seperti terowongan pribadi di internet - aman dari intipan.


## <span id="section-w">W</span>

### **Web Hosting**
Layanan penyimpanan website. Sewa tempat untuk website Anda di internet.

### **WordPress**
CMS paling populer. Platform bikin website paling mudah - 40% website pakai ini.

### **Website**
Kumpulan halaman di internet. Toko/kantor/rumah digital Anda.

### **Web Server**
Software yang melayani website. Pelayan yang mengirim file website ke pengunjung.

### **Widget**
Komponen kecil di website. Seperti pernak-pernik di sidebar.

### **Whitelist**
Daftar yang diijinkan. Kebalikan blacklist - yang masuk list ini boleh akses.

### **WHOIS**
Info pemilik domain. Seperti cek pemilik plat nomor kendaraan.

### **Webmail**
Email via browser. Baca email lewat browser, tidak perlu aplikasi.

### **WAF (Web Application Firewall)**
Firewall khusus aplikasi web. Bodyguard untuk website Anda.

### **Webhook**
Notifikasi otomatis antar aplikasi. Seperti SMS otomatis kalau ada kejadian tertentu.


## <span id="section-x">X</span>

### **XML (eXtensible Markup Language)**
Format data terstruktur. Seperti form dengan kolom-kolom jelas.

### **XSS (Cross-Site Scripting)**
Jenis serangan keamanan. Suntik kode jahat ke website orang.

### **X-Frame-Options**
Pengaturan keamanan frame. Cegah website ditampilkan dalam frame website lain.

### **XAMPP**
Paket server lokal. Apache + MySQL + PHP untuk testing di komputer sendiri.

### **X.509**
Standar certificate digital. Format standar untuk SSL certificate.


## <span id="section-y">Y</span>

### **YAML**
Format konfigurasi. Lebih mudah dibaca manusia daripada JSON.

### **Yoast**
Plugin SEO WordPress populer. Membantu optimasi website untuk Google.

### **YouTube Embed**
Menyematkan video YouTube. Tampilkan video YouTube di website Anda.

### **Yarn**
Package manager alternatif NPM. Cara lain install paket JavaScript.


## <span id="section-z">Z</span>

### **ZIP**
Format kompresi file. Seperti vakum baju - muat lebih banyak dalam space kecil.

### **Zone File**
File DNS yang berisi mapping domain. Seperti peta yang tunjukkan alamat server hosting Anda.

