---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import Base64ConverterForm from '~/components/alat/base64-converter/Base64ConverterForm.astro';

const metadata = {
  title: 'Base64 Encoder Decoder - Konversi Teks ke Base64 Online | Penasihat Hosting',
  description: 'Encoder dan Decoder Base64 online gratis. Konversi teks ke Base64 dan sebaliknya dengan mudah.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/placeholder/base64-converter.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Apa Itu Base64?",
    content: [
      "Base64 adalah metode encoding biner-ke-teks yang mengubah data biner menjadi format string ASCII. Format ini dirancang untuk membawa data yang, secara tradisional, mungkin tidak stabil di seluruh sistem jaringan, seperti byte non-ASCII dalam email atau XML. Base64 menggunakan set 64 karakter aman untuk web (A-Z, a-z, 0-9, +, /) dan karakter padding (=) untuk memastikan panjang output adalah kelipatan 4.",
      "Penting untuk dicatat bahwa Base64 bukanlah metode enkripsi. Ini tidak memberikan keamanan atau kerahasiaan pada data; ini hanyalah cara untuk merepresentasikan data biner dalam format teks. Siapa pun dapat dengan mudah men-decode string Base64 kembali ke data aslinya."
    ]
  },
  {
    title: "Kegunaan Base64",
    content: [
      "Base64 digunakan dalam berbagai aplikasi, termasuk:",
      "<ul class='list-disc list-inside'><li><strong>Mengirim Data Biner via Teks:</strong> Embedding gambar atau file lain langsung ke dalam kode HTML atau CSS (misalnya, `data:image/png;base64,...`).</li><li><strong>Transfer Data di URL:</strong> Mengirim data biner di bagian URL.</li><li><strong>Standar Komunikasi:</strong> Digunakan dalam standar seperti MIME (Multipurpose Internet Mail Extensions) untuk mengirim attachment email dan dalam format data lain seperti XML dan JSON.</li><li><strong>Obfuscation Ringan:</strong> Meskipun bukan keamanan, terkadang digunakan untuk menyembunyikan data dari pandangan sekilas.</li></ul>"
    ]
  },
  {
    title: "Bagaimana Cara Kerjanya? (Singkat)",
    content: [
      "Proses encoding Base64 melibatkan pengelompokan data input menjadi blok 3 byte (24 bit). Setiap blok 3 byte kemudian dibagi menjadi empat kelompok 6 bit. Setiap kelompok 6 bit diubah menjadi nilai desimal (0-63), yang kemudian dipetakan ke karakter yang sesuai dalam set karakter Base64. Jika blok terakhir data input kurang dari 3 byte, padding (`=`) ditambahkan ke output untuk memastikan panjangnya kelipatan 4."
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="Base64 Converter">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="Base64 Encoder Decoder"
    description="Konversi teks biasa menjadi string Base64 atau sebaliknya dengan cepat dan mudah."
    subtitle="Tool gratis untuk meng-encode dan men-decode teks ke/dari format Base64."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <ToolInterface title="Base64 Converter">
        <Base64ConverterForm />
      </ToolInterface>
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  /* Add styling consistent with other tools if needed */
</style>

<!-- Base64 Converter Logic -->
<script is:inline>
  class Base64Converter {
    constructor() {
      this.encodeModeBtn = document.getElementById('encodeModeBtn');
      this.decodeModeBtn = document.getElementById('decodeModeBtn');
      this.inputTextarea = document.getElementById('inputText');
      this.outputTextarea = document.getElementById('outputText');
      this.inputLabel = document.getElementById('inputLabel');
      this.outputLabel = document.getElementById('outputLabel');
      this.copyButton = document.getElementById('copyOutputBtn');
      this.clearButton = document.getElementById('clearAllButton');
      this.errorMessageElement = document.getElementById('inputErrorMessage');
      this.copyFeedbackMessageElement = document.getElementById('copyFeedbackMessage');

      this.currentMode = 'encode'; // 'encode' or 'decode'

      // Need to handle UTF-8 for btoa/atob
      // From: https://developer.mozilla.org/en-US/docs/Web/API/btoa#unicode_strings
      this.utf8Encode = new TextEncoder();
      this.utf8Decode = new TextDecoder();
    }

    init() {
      // Set up mode switching
      if (this.encodeModeBtn && this.decodeModeBtn) {
        this.encodeModeBtn.addEventListener('click', () => this.switchMode('encode'));
        this.decodeModeBtn.addEventListener('click', () => this.switchMode('decode'));
      }

      // Set up input handling
      if (this.inputTextarea) {
        this.inputTextarea.addEventListener('input', () => this.convert());
      }

      // Set up copy button
      if (this.copyButton) {
        this.copyButton.addEventListener('click', () => this.copyOutput());
      }

      // Set up clear button
      if (this.clearButton) {
        this.clearButton.addEventListener('click', () => this.clearAll());
      }

      // Show encode mode by default
      this.switchMode('encode');
    }

    switchMode(mode) {
      this.currentMode = mode;
      
      if (mode === 'encode') {
        this.encodeModeBtn?.classList.add('active');
        this.decodeModeBtn?.classList.remove('active');
        this.inputLabel.textContent = 'Teks Biasa';
        this.outputLabel.textContent = 'Base64';
      } else {
        this.encodeModeBtn?.classList.remove('active');
        this.decodeModeBtn?.classList.add('active');
        this.inputLabel.textContent = 'Base64';
        this.outputLabel.textContent = 'Teks Biasa';
      }

      // Convert current input
      this.convert();
    }

    convert() {
      const input = this.inputTextarea?.value || '';
      
      if (!input.trim()) {
        this.outputTextarea.value = '';
        this.hideError();
        return;
      }

      try {
        if (this.currentMode === 'encode') {
          this.outputTextarea.value = this.encodeToBase64(input);
        } else {
          this.outputTextarea.value = this.decodeFromBase64(input);
        }
        this.hideError();
      } catch {
        this.showError('Input tidak valid untuk mode ' + this.currentMode);
        this.outputTextarea.value = '';
      }
    }

    encodeToBase64(text) {
      // Handle UTF-8 encoding for Unicode characters
      const bytes = this.utf8Encode.encode(text);
      const binaryString = Array.from(bytes, byte => String.fromCharCode(byte)).join('');
      return btoa(binaryString);
    }

    decodeFromBase64(base64String) {
      try {
        // Remove any whitespace and padding issues
        const cleanBase64 = base64String.replace(/\s/g, '');
        const binaryString = atob(cleanBase64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        return this.utf8Decode.decode(bytes);
      } catch {
        throw new Error('Invalid Base64 string');
      }
    }

    async copyOutput() {
      const output = this.outputTextarea?.value;
      if (!output) return;

      try {
        await navigator.clipboard.writeText(output);
        this.showCopyFeedback('Berhasil disalin!');
      } catch {
        // Fallback for older browsers
        this.outputTextarea?.select();
        document.execCommand('copy');
        this.showCopyFeedback('Berhasil disalin!');
      }
    }

    clearAll() {
      this.inputTextarea.value = '';
      this.outputTextarea.value = '';
      this.hideError();
      this.hideCopyFeedback();
    }

    showError(message) {
      if (this.errorMessageElement) {
        this.errorMessageElement.textContent = message;
        this.errorMessageElement.classList.remove('hidden');
      }
    }

    hideError() {
      this.errorMessageElement?.classList.add('hidden');
    }

    showCopyFeedback(message) {
      if (this.copyFeedbackMessageElement) {
        this.copyFeedbackMessageElement.textContent = message;
        this.copyFeedbackMessageElement.classList.remove('hidden');
        setTimeout(() => this.hideCopyFeedback(), 2000);
      }
    }

    hideCopyFeedback() {
      this.copyFeedbackMessageElement?.classList.add('hidden');
    }
  }

  // Initialize converter on Astro page load
  document.addEventListener('astro:page-load', () => {
    setTimeout(() => {
      try {
        const converter = new Base64Converter();
        converter.init();
      } catch (error) {
        console.error('Error initializing Base64 Converter on astro:page-load:', error);
      }
    }, 100);
  });
</script> 