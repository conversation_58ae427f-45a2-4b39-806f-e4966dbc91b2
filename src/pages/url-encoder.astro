---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import UrlEncoderForm from '~/components/alat/url-encoder/UrlEncoderForm.astro';

const metadata = {
  title: 'URL Encoder Decoder - Konversi URL dengan Mudah | Penasihat Hosting',
  description: 'Encoder dan Decoder URL online gratis. Konversi karakter khusus dalam URL dengan mudah dan cepat.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/url-encoder.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Apa Itu URL Encoding?",
    content: [
      "URL Encoding (juga dikenal sebagai Percent Encoding) adalah mekanisme untuk mengkonversi karakter khusus dalam URL menjadi format yang dapat ditransmisikan dengan aman melalui internet. Dalam URL, hanya karakter alfanumerik dan beberapa karakter khusus yang diperbolehkan tanpa encoding.",
      "Karakter khusus, spasi, dan simbol non-ASCII dikonversi menjadi format \"%XX\" di mana XX adalah nilai heksadesimal dari karakter tersebut. Misalnya, spasi dikonversi menjadi \"%20\"."
    ]
  },
  {
    title: "Kapan Menggunakan URL Encoding?",
    content: [
      "URL Encoding penting digunakan dalam situasi berikut:",
      "<ul class='list-disc list-inside'><li><strong>Parameter Query String:</strong> Ketika mengirim data melalui URL dalam parameter query string.</li><li><strong>Karakter Khusus dalam URL:</strong> Saat URL berisi karakter seperti spasi, tanda kurung, tanda plus, dll.</li><li><strong>Karakter Non-ASCII:</strong> Untuk menangani karakter internasional atau non-ASCII dalam URL.</li><li><strong>Pengembangan Web:</strong> Saat bekerja dengan formulir web, API, atau manipulasi URL.</li></ul>"
    ]
  },
  {
    title: "Karakter URL Encoding Umum",
    content: [
      "Berikut adalah beberapa karakter yang sering di-encode dalam URL:",
      "<ul class='list-disc list-inside'><li><strong>Spasi:</strong> %20</li><li><strong>!</strong> (tanda seru): %21</li><li><strong>#</strong> (tanda pagar): %23</li><li><strong>$</strong> (tanda dolar): %24</li><li><strong>&</strong> (ampersand): %26</li><li><strong>'</strong> (tanda kutip tunggal): %27</li><li><strong>(</strong> (kurung buka): %28</li><li><strong>)</strong> (kurung tutup): %29</li><li><strong>*</strong> (tanda bintang): %2A</li><li><strong>+</strong> (tanda plus): %2B</li><li><strong>,</strong> (koma): %2C</li><li><strong>/</strong> (garis miring): %2F</li><li><strong>:</strong> (titik dua): %3A</li><li><strong>;</strong> (titik koma): %3B</li><li><strong>=</strong> (tanda sama dengan): %3D</li><li><strong>?</strong> (tanda tanya): %3F</li><li><strong>@</strong> (at): %40</li><li><strong>[</strong> (kurung siku buka): %5B</li><li><strong>]</strong> (kurung siku tutup): %5D</li></ul>"
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="URL Encoder">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="URL Encoder Decoder"
    description="Encode dan decode karakter khusus dalam URL dengan mudah. Berguna untuk pengembangan web dan API."
    subtitle="Tool gratis untuk mengkonversi URL dengan mudah dan cepat."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <ToolInterface title="URL Encoder Decoder">
        <UrlEncoderForm />
      </ToolInterface>
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  /* Add styling consistent with other tools if needed */
</style>

<!-- URL Encoder/Decoder Logic -->
<script is:inline>
  class UrlConverter {
    constructor() {
      this.textInput = document.getElementById('inputText');
      this.outputText = document.getElementById('outputText');
      this.encodeModeBtn = document.getElementById('encodeModeBtn');
      this.decodeModeBtn = document.getElementById('decodeModeBtn');
      this.convertBtn = document.getElementById('convertBtn');
      this.clearBtn = document.getElementById('clearBtn');
      this.copyBtn = document.getElementById('copyBtn');
      this.errorMessageElement = document.getElementById('errorMessage');
      this.copyFeedbackElement = document.getElementById('copyFeedback');
      
      this.isEncodeMode = true; // Default mode is encode
    }

    init() {
      if (this.encodeModeBtn) {
        this.encodeModeBtn.addEventListener('click', () => this.setMode(true));
      }
      if (this.decodeModeBtn) {
        this.decodeModeBtn.addEventListener('click', () => this.setMode(false));
      }
      if (this.convertBtn) {
        this.convertBtn.addEventListener('click', () => this.convert());
      }
      if (this.clearBtn) {
        this.clearBtn.addEventListener('click', () => this.clearAll());
      }
      if (this.copyBtn) {
        this.copyBtn.addEventListener('click', () => this.copyOutput());
      }
      if (this.textInput) {
        this.textInput.addEventListener('input', () => this.convert());
      }
    }

    setMode(isEncode) {
      this.isEncodeMode = isEncode;
      
      if (isEncode) {
        this.encodeModeBtn?.classList.add('active');
        this.decodeModeBtn?.classList.remove('active');
      } else {
        this.encodeModeBtn?.classList.remove('active');
        this.decodeModeBtn?.classList.add('active');
      }
      
      // Convert current input
      this.convert();
    }

    convert() {
      const input = this.textInput?.value || '';
      
      if (!input.trim()) {
        this.outputText.value = '';
        this.hideError();
        return;
      }

      try {
        if (this.isEncodeMode) {
          this.outputText.value = encodeURIComponent(input);
        } else {
          this.outputText.value = decodeURIComponent(input);
        }
        this.hideError();
      } catch (error) {
        this.showError('Error converting URL. Please check your input.');
      }
    }

    async copyOutput() {
      const output = this.outputText?.value;
      if (!output) return;

      try {
        await navigator.clipboard.writeText(output);
        this.showCopyFeedback('URL berhasil disalin!');
      } catch (error) {
        // Fallback for older browsers
        this.outputText?.select();
        document.execCommand('copy');
        this.showCopyFeedback('URL berhasil disalin!');
      }
    }

    clearAll() {
      this.textInput.value = '';
      this.outputText.value = '';
      this.hideError();
      this.hideCopyFeedback();
    }

    showError(message) {
      if (this.errorMessageElement) {
        this.errorMessageElement.textContent = message;
        this.errorMessageElement.classList.remove('hidden');
      }
    }

    hideError() {
      this.errorMessageElement?.classList.add('hidden');
    }

    showCopyFeedback(message) {
      if (this.copyFeedbackElement) {
        this.copyFeedbackElement.textContent = message;
        this.copyFeedbackElement.classList.remove('hidden');
        setTimeout(() => this.hideCopyFeedback(), 2000);
      }
    }

    hideCopyFeedback() {
      this.copyFeedbackElement?.classList.add('hidden');
    }
  }

  // Initialize converter on Astro page load
  document.addEventListener('astro:page-load', () => {
    setTimeout(() => {
      try {
        const converter = new UrlConverter();
        converter.init();
      } catch (error) {
        console.error('Error initializing URL Converter on astro:page-load:', error);
      }
    }, 100);
  });
</script> 