---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection, render } from 'astro:content';
import type { CollectionEntry } from 'astro:content';
import ProviderList from '~/components/widgets/ProviderList.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';

export async function getStaticPaths() {
  const categories = await getCollection('hosting-categories');
  const allProviders = await getCollection('hosting-providers');

  const PROVIDERS_PER_PAGE = 20;
  const paths: Array<{
    params: { category: string; page: string };
    props: { category: CollectionEntry<'hosting-categories'> };
  }> = [];

  for (const category of categories) {
    // Filter providers by category (including control panel categories)
    const categoryProviders = allProviders.filter((provider) => {
      if (!provider.data.isActive) return false;
      
      // Check regular categories
      if (provider.data.categories.includes(category.id)) return true;
      
      // Check control panel categories
      if (provider.data.controlPanels) {
        const controlPanelCategories = {
          'cpanel': 'cpanel-hosting',
          'directadmin': 'directadmin-hosting', 
          'plesk': 'plesk-hosting'
        };
        
        return provider.data.controlPanels.some(panel => 
          controlPanelCategories[panel] === category.id
        );
      }
      
      return false;
    });

    const totalPages = Math.ceil(categoryProviders.length / PROVIDERS_PER_PAGE);

    // Generate pagination paths (starting from page 2, since page 1 is handled by [category].astro)
    for (let page = 2; page <= totalPages; page++) {
      paths.push({
        params: {
          category: category.id,
          page: page.toString(),
        },
        props: {
          category,
        },
      });
    }
  }

  return paths;
}

const { category } = Astro.props;
const { category: categorySlug, page: pageParam } = Astro.params;

const currentPage = parseInt(pageParam || '1', 10);

// Render the markdown content
const { Content } = await render(category);

// Get all hosting providers
const allProviders = await getCollection('hosting-providers');

// Filter providers by category (including control panel categories)
const categoryProviders = allProviders.filter((provider) => {
  if (!provider.data.isActive) return false;
  
  // Check regular categories
  if (provider.data.categories.includes(categorySlug)) return true;
  
  // Check control panel categories
  if (provider.data.controlPanels) {
    const controlPanelCategories = {
      'cpanel': 'cpanel-hosting',
      'directadmin': 'directadmin-hosting', 
      'plesk': 'plesk-hosting'
    };
    
    return provider.data.controlPanels.some(panel => 
      controlPanelCategories[panel] === categorySlug
    );
  }
  
  return false;
});

// Sort providers (sponsored first, then promoted, then recommended, then verified, then by modified date latest)
const sortedProviders = categoryProviders.sort((a, b) => {
  // Get badge types
  const aSponsored = a.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const bSponsored = b.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const aPromoted = a.data.badges?.some(badge => badge.type === 'promoted') || false;
  const bPromoted = b.data.badges?.some(badge => badge.type === 'promoted') || false;
  const aRecommended = a.data.badges?.some(badge => badge.type === 'recommended') || false;
  const bRecommended = b.data.badges?.some(badge => badge.type === 'recommended') || false;
  const aVerified = a.data.badges?.some(badge => badge.type === 'verified') || false;
  const bVerified = b.data.badges?.some(badge => badge.type === 'verified') || false;

  // Sponsored always first
  if (aSponsored && !bSponsored) return -1;
  if (!aSponsored && bSponsored) return 1;
  
  // If both or neither are sponsored, check promoted
  if (!aSponsored && !bSponsored) {
    if (aPromoted && !bPromoted) return -1;
    if (!aPromoted && bPromoted) return 1;
  }
  
  // If both or neither are sponsored/promoted, check recommended
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted) {
    if (aRecommended && !bRecommended) return -1;
    if (!aRecommended && bRecommended) return 1;
  }
  
  // If both or neither are sponsored/promoted/recommended, check verified
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted && !aRecommended && !bRecommended) {
    if (aVerified && !bVerified) return -1;
    if (!aVerified && bVerified) return 1;
  }

  // Finally sort by modified date (latest first)
  const aModified = new Date(a.data.modifiedAt || 0);
  const bModified = new Date(b.data.modifiedAt || 0);
  return bModified.getTime() - aModified.getTime();
});

// Pagination logic
const PROVIDERS_PER_PAGE = 20;
const totalPages = Math.ceil(categoryProviders.length / PROVIDERS_PER_PAGE);
const startIndex = (currentPage - 1) * PROVIDERS_PER_PAGE;
const endIndex = startIndex + PROVIDERS_PER_PAGE;
const paginatedProviders = sortedProviders.slice(startIndex, endIndex);

// Generate dynamic title
const dynamicTitle = category.data.listTitle
  ? category.data.listTitle.replace('{count}', categoryProviders.length.toString())
  : `Temukan ${categoryProviders.length} Solusi ${category.data.title} Terbaik untuk Kebutuhan Anda`;

// Add page number to title for SEO
const pageTitle =
  currentPage > 1
    ? `${category.data.title} - Halaman ${currentPage} dari ${totalPages}`
    : `${category.data.title} - ${category.data.providerCount} Provider Terbaik`;

const metadata = {
  title: pageTitle,
  description: category.data.seoDescription || category.data.description,
  canonical: `https://penasihathosting.com/direktori/${categorySlug}/`,
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    images: [
      {
        url: `https://img.penasihathosting.com/og/kategori-${categorySlug}.webp`,
        width: 1200,
        height: 630,
      },
    ],
  },
};

// Redirect to first page if this is page 1 (should be handled by [category].astro)
if (currentPage === 1) {
  return Astro.redirect(`/direktori/${categorySlug}/`);
}

// Redirect to last valid page if page number is too high
if (currentPage > totalPages) {
  return Astro.redirect(`/direktori/${categorySlug}/page/${totalPages}/`);
}
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 border-b border-gray-200 bg-bg-muted">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href={`/direktori/${categorySlug}/`} class="text-muted hover:text-primary">{category.data.title}</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">Halaman {currentPage}</li>
      </ol>
    </div>
  </nav>

  <!-- Category Header -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Disclosure -->
      <Disclosure />  
      <div class="border-t-8 border-primary pt-4 mb-6">
        <div>
          <h1 class="text-3xl font-bold text-heading md:text-4xl">
            {category.data.title}
            {currentPage > 1 && <span class="text-muted"> - Halaman {currentPage}</span>}
          </h1>
        </div>
      </div>

      <div class="max-w-3xl">
        <div id="category-description" class="line-clamp-3 text-muted">
          {category.data.description}
        </div>
        <button
          id="read-more-btn"
          class="mt-2 text-muted hover:text-primary/80 text-base font-medium transition-colors hidden"
        >
          Baca selengkapnya
        </button>
      </div>
    </div>
  </section>

  <!-- Providers List -->
  <ProviderList
    providers={paginatedProviders}
    currentPage={currentPage}
    totalPages={totalPages}
    baseUrl={`/direktori/${categorySlug}/`}
    title={dynamicTitle}
    showPagination={totalPages > 1}
    categorySlug={categorySlug}
    similarCategoriesTitle="Kategori Terkait"
  />

  <!-- Educational Content -->
  <section class="py-16 bg-bg-section">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <div
        class="prose prose-md max-w-none dark:prose-invert prose-headings:font-heading prose-headings:leading-tight prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-img:rounded-md prose-headings:scroll-mt-[80px] prose-p:text-muted prose-h3:border-t-2 prose-h3:border-primary prose-h3:pt-4 leading-normal category-content"
      >
        <Content />
      </div>
    </div>
  </section>
</Layout>

<script>
  function initReadMore() {
    const description = document.getElementById('category-description');
    const readMoreBtn = document.getElementById('read-more-btn');

    if (!description || !readMoreBtn) return;

    // Use requestAnimationFrame to ensure layout is calculated
    requestAnimationFrame(() => {
      // Add a small delay to ensure CSS is fully applied
      setTimeout(() => {
        // Check if text is actually clamped (overflow)
        const isTextClamped = description.scrollHeight > description.clientHeight;

        if (isTextClamped) {
          readMoreBtn.classList.remove('hidden');

          // Remove any existing event listeners to prevent duplicates
          const newBtn = readMoreBtn.cloneNode(true);
          if (readMoreBtn.parentNode) {
            readMoreBtn.parentNode.replaceChild(newBtn, readMoreBtn);
          }

          newBtn.addEventListener('click', (e) => {
            e.preventDefault();

            if (description.classList.contains('line-clamp-3')) {
              description.classList.remove('line-clamp-3');
              newBtn.textContent = 'Baca lebih sedikit';
            } else {
              description.classList.add('line-clamp-3');
              newBtn.textContent = 'Baca selengkapnya';
            }
          });
        }
      }, 100); // Small delay to ensure CSS is applied
    });
  }

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initReadMore);

  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initReadMore);

  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initReadMore);

  // Additional fallback for extra reliability
  window.addEventListener('load', initReadMore);

  // Initialize immediately if DOM is already ready
  if (document.readyState === 'loading') {
    // DOM is still loading, events will handle it
  } else {
    // DOM is already ready, initialize now
    initReadMore();
  }
</script>
