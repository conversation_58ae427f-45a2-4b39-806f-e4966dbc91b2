---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection, render } from 'astro:content';
import ProviderList from '~/components/widgets/ProviderList.astro';
import BackToTop from '~/components/common/BackToTop.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';

export async function getStaticPaths() {
  const categories = await getCollection('hosting-categories');

  return categories.map((category) => ({
    params: { category: category.id },
    props: { category },
  }));
}

const { category } = Astro.props;
const { category: categorySlug } = Astro.params;

// Render the markdown content
const { Content } = await render(category);

// Get all hosting providers
const allProviders = await getCollection('hosting-providers');

// Filter providers by category (including control panel categories)
const categoryProviders = allProviders.filter((provider) => {
  if (!provider.data.isActive) return false;
  
  // Check regular categories
  if (provider.data.categories.includes(categorySlug)) return true;
  
  // Check control panel categories
  if (provider.data.controlPanels) {
    const controlPanelCategories = {
      'cpanel': 'cpanel-hosting',
      'directadmin': 'directadmin-hosting', 
      'plesk': 'plesk-hosting'
    };
    
    return provider.data.controlPanels.some(panel => 
      controlPanelCategories[panel] === categorySlug
    );
  }
  
  return false;
});

// Sort providers (sponsored first, then promoted, then recommended, then verified, then by modified date latest)
const sortedProviders = categoryProviders.sort((a, b) => {
  // Get badge types
  const aSponsored = a.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const bSponsored = b.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const aPromoted = a.data.badges?.some(badge => badge.type === 'promoted') || false;
  const bPromoted = b.data.badges?.some(badge => badge.type === 'promoted') || false;
  const aRecommended = a.data.badges?.some(badge => badge.type === 'recommended') || false;
  const bRecommended = b.data.badges?.some(badge => badge.type === 'recommended') || false;
  const aVerified = a.data.badges?.some(badge => badge.type === 'verified') || false;
  const bVerified = b.data.badges?.some(badge => badge.type === 'verified') || false;

  // Sponsored always first
  if (aSponsored && !bSponsored) return -1;
  if (!aSponsored && bSponsored) return 1;
  
  // If both or neither are sponsored, check promoted
  if (!aSponsored && !bSponsored) {
    if (aPromoted && !bPromoted) return -1;
    if (!aPromoted && bPromoted) return 1;
  }
  
  // If both or neither are sponsored/promoted, check recommended
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted) {
    if (aRecommended && !bRecommended) return -1;
    if (!aRecommended && bRecommended) return 1;
  }
  
  // If both or neither are sponsored/promoted/recommended, check verified
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted && !aRecommended && !bRecommended) {
    if (aVerified && !bVerified) return -1;
    if (!aVerified && bVerified) return 1;
  }

  // Finally sort by modified date (latest first)
  const aModified = new Date(a.data.modifiedAt || 0);
  const bModified = new Date(b.data.modifiedAt || 0);
  return bModified.getTime() - aModified.getTime();
});

// Pagination logic
const PROVIDERS_PER_PAGE = 20;
const currentPage = 1; // For now, we'll implement dynamic pages later
const totalProviders = categoryProviders.length;
const totalPages = Math.ceil(totalProviders / PROVIDERS_PER_PAGE);
const startIndex = (currentPage - 1) * PROVIDERS_PER_PAGE;
const endIndex = startIndex + PROVIDERS_PER_PAGE;
const paginatedProviders = sortedProviders.slice(startIndex, endIndex);

// Generate dynamic title
const dynamicTitle = category.data.listTitle
  ? category.data.listTitle.replace('{count}', totalProviders.toString())
  : `Temukan ${totalProviders} Solusi ${category.data.title} Terbaik untuk Kebutuhan Anda`;

// Generate SEO optimized title and description
const seoTitle = category.data.seoTitle
  ? category.data.seoTitle.replace('{count}', totalProviders.toString())
  : `${category.data.title} - ${totalProviders} Provider Terbaik`;

const seoDescription = category.data.seoDescription
  ? category.data.seoDescription.replace('{count}', totalProviders.toString())
  : category.data.description;

const metadata = {
  title: seoTitle,
  description: seoDescription,
  canonical: `https://penasihathosting.com/direktori/${categorySlug}/`,
  openGraph: {
    type: 'website',
    title: seoTitle,
    description: seoDescription,
    url: `https://penasihathosting.com/direktori/${categorySlug}/`,
    images: [
      {
        url: `https://img.penasihathosting.com/og/kategori-${categorySlug}.webp`,
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 border-b border-gray-200 bg-bg-muted">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">{category.data.title}</li>
      </ol>
    </div>
  </nav>

  <!-- Category Header -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Disclosure -->
      <Disclosure />
      <div class="border-t-8 border-primary pt-4 mb-6">
        <div>
          <h1 class="text-3xl font-bold text-heading md:text-4xl">
            {category.data.title}
          </h1>
        </div>
      </div>

      <div class="max-w-3xl">
        <div id="category-description" class="line-clamp-3 text-muted">
          {category.data.description}
        </div>
        <button
          id="read-more-btn"
          class="mt-2 text-muted hover:text-primary/80 text-base font-medium transition-colors hidden"
        >
          Baca selengkapnya
        </button>
      </div>
    </div>
  </section>

  <!-- Navigation Tabs -->
  <section>
    <div class="mx-auto max-w-global px-4 md:px-6 mb-4">
      <nav class="flex space-x-8 border-b border-gray-200" aria-label="Category navigation">
        <button
          id="providers-tab"
          class="py-3 px-1 border-b-2 border-primary text-primary font-medium text-sm whitespace-nowrap transition-colors -mb-px"
          data-target="providers"
        >
          Semua Provider
          <span class="ml-2 bg-primary text-white text-xs px-2 py-0.5 rounded-full">
            {totalProviders}
          </span>
        </button>
        <button
          id="guide-tab"
          class="py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm whitespace-nowrap transition-colors -mb-px"
          data-target="guide"
        >
          Panduan Pengguna
        </button>
      </nav>
    </div>
  </section>

  <!-- Providers List -->
  <section id="providers-section">
    <ProviderList
      providers={paginatedProviders}
      currentPage={currentPage}
      totalPages={totalPages}
      baseUrl={`/direktori/${categorySlug}/`}
      title={dynamicTitle}
      showPagination={totalPages > 1}
      categorySlug={categorySlug}
      similarCategoriesTitle="Kategori Terkait"
    />
  </section>

  <!-- Educational Content -->
  <section id="guide-section" class="py-16 bg-bg-section">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <div
        class="prose prose-md max-w-none dark:prose-invert prose-headings:font-heading prose-headings:leading-tight prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-img:rounded-md prose-headings:scroll-mt-[80px] prose-p:text-muted prose-h3:border-t-2 prose-h3:border-primary prose-h3:pt-4 leading-normal category-content"
      >
        <Content />
      </div>
    </div>
  </section>

  <!-- Back to Top Button -->
  <BackToTop />
</Layout>

<script>
  // Tab Navigation Functions
  function scrollToSection(target) {
    const section = target === 'providers' 
      ? document.getElementById('providers-section')
      : document.getElementById('guide-section');
    
    if (section) {
      window.scrollTo({
        top: section.offsetTop - 20,
        behavior: 'smooth'
      });
    }
    
    updateActiveTab(target);
  }

  function updateActiveTab(activeTab) {
    const providersTab = document.getElementById('providers-tab');
    const guideTab = document.getElementById('guide-tab');
    
    if (!providersTab || !guideTab) return;

    // Reset all tabs
    const inactiveClass = 'py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm whitespace-nowrap transition-colors -mb-px';
    const activeClass = 'py-3 px-1 border-b-2 border-primary text-primary font-medium text-sm whitespace-nowrap transition-colors -mb-px';

    providersTab.className = inactiveClass;
    guideTab.className = inactiveClass;

    // Set active tab
    if (activeTab === 'providers') {
      providersTab.className = activeClass;
    } else if (activeTab === 'guide') {
      guideTab.className = activeClass;
    }
  }

  // Initialize tab navigation
  function initTabNavigation() {
    const providersTab = document.getElementById('providers-tab');
    const guideTab = document.getElementById('guide-tab');
    
    if (providersTab) {
      providersTab.addEventListener('click', () => scrollToSection('providers'));
    }
    
    if (guideTab) {
      guideTab.addEventListener('click', () => scrollToSection('guide'));
    }
  }

  // Read More functionality
  function initReadMore() {
    const description = document.getElementById('category-description');
    const readMoreBtn = document.getElementById('read-more-btn');

    if (!description || !readMoreBtn) return;

    // Use requestAnimationFrame to ensure layout is calculated
    requestAnimationFrame(() => {
      // Add a small delay to ensure CSS is fully applied
      setTimeout(() => {
        // Check if text is actually clamped (overflow)
        const isTextClamped = description.scrollHeight > description.clientHeight;

        if (isTextClamped) {
          readMoreBtn.classList.remove('hidden');

          // Remove any existing event listeners to prevent duplicates
          const newBtn = readMoreBtn.cloneNode(true);
          if (readMoreBtn.parentNode) {
            readMoreBtn.parentNode.replaceChild(newBtn, readMoreBtn);
          }

          newBtn.addEventListener('click', (e) => {
            e.preventDefault();

            if (description.classList.contains('line-clamp-3')) {
              description.classList.remove('line-clamp-3');
              newBtn.textContent = 'Baca lebih sedikit';
            } else {
              description.classList.add('line-clamp-3');
              newBtn.textContent = 'Baca selengkapnya';
            }
          });
        }
      }, 100); // Small delay to ensure CSS is applied
    });
  }

  // Initialize all functions
  function initAll() {
    initReadMore();
    initTabNavigation();
  }

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initAll);

  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initAll);

  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initAll);

  // Additional fallback for extra reliability
  window.addEventListener('load', initAll);

  // Initialize immediately if DOM is already ready
  if (document.readyState === 'loading') {
    // DOM is still loading, events will handle it
  } else {
    // DOM is already ready, initialize now
    initAll();
  }
</script>
