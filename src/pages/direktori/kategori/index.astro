---
import Layout from '~/layouts/PageLayout.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';
import { getCollection } from 'astro:content';

// Get all hosting categories and providers
const hostingCategories = await getCollection('hosting-categories');
const allProviders = await getCollection('hosting-providers');

// Calculate actual provider count for each category
const categoriesWithCount = hostingCategories.map(category => {
  const categoryId = category.id;
  
  const actualCount = allProviders.filter(provider => {
    if (!provider.data.isActive) return false;
    
    // Handle control panel categories (cpanel-hosting, plesk-hosting, directadmin-hosting)
    if (categoryId.endsWith('-hosting')) {
      const controlPanelName = categoryId.replace('-hosting', '') as 'cpanel' | 'plesk' | 'directadmin';
      const isControlPanelCategory = ['cpanel', 'plesk', 'directadmin'].includes(controlPanelName);
      
      if (isControlPanelCategory) {
        return provider.data.controlPanels && provider.data.controlPanels.includes(controlPanelName);
      }
    }
    
    // Handle regular categories
    return provider.data.categories.includes(categoryId);
  }).length;
  
  return {
    ...category,
    actualProviderCount: actualCount,
    isControlPanel: categoryId.endsWith('-hosting') && ['cpanel', 'plesk', 'directadmin'].includes(categoryId.replace('-hosting', ''))
  };
});

// Separate and sort categories
const controlPanelCategories = categoriesWithCount
  .filter(category => category.isControlPanel)
  .sort((a, b) => {
    if (a.data.featured && !b.data.featured) return -1;
    if (!a.data.featured && b.data.featured) return 1;
    return b.actualProviderCount - a.actualProviderCount;
  });

const regularCategories = categoriesWithCount
  .filter(category => !category.isControlPanel)
  .sort((a, b) => {
    if (a.data.featured && !b.data.featured) return -1;
    if (!a.data.featured && b.data.featured) return 1;
    return b.actualProviderCount - a.actualProviderCount;
  });

const metadata = {
  title: 'Semua Kategori Hosting Indonesia - Direktori Lengkap',
  description: 'Jelajahi semua kategori hosting Indonesia. Temukan provider hosting terbaik berdasarkan jenis layanan yang Anda butuhkan.',
  canonical: 'https://penasihathosting.com/direktori/kategori/',
  openGraph: {
    type: 'website',
    images: [
      {
        url: 'https://img.penasihathosting.com/og/direktori-kategori.webp',
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 bg-bg-muted border-b border-gray-200">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">Semua Kategori</li>
      </ol>
    </div>
  </nav>

  <!-- Header -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Disclosure -->
      <Disclosure />
      <div class="border-t-8 border-primary pt-4 mb-8">
        <h1 class="text-3xl font-bold text-heading md:text-4xl mb-4">
          Semua Kategori Hosting
        </h1>
        <p class="text-lg text-muted max-w-2xl">
          Temukan provider hosting Indonesia terbaik berdasarkan kategori layanan yang Anda butuhkan.
          Dari shared hosting hingga dedicated server, semua ada di sini.
        </p>
      </div>
    </div>
  </section>

  <!-- Categories Grid -->
  <section class="pb-12">
    <div class="mx-auto max-w-global px-4 md:px-6">
      
      <!-- Regular Categories Section -->
      <div class="mb-12">
        <div class="mb-8">
          <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-3">
            Kategori Layanan Hosting
          </h2>
          <p class="text-muted">
            Pilih jenis layanan hosting sesuai dengan kebutuhan website Anda
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {regularCategories.map((category) => (
            <a
              href={`/direktori/${category.id}/`}
              class="group block rounded-md border border-gray-200 bg-white p-6 shadow-md hover:no-underline transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
            >
              <div class="mb-4">
                <h3 class="font-semibold text-heading group-hover:text-primary transition-colors text-lg mb-2">
                  {category.data.title}
                </h3>
                <p class="text-sm text-muted">
                  {category.actualProviderCount} provider tersedia
                </p>
              </div>

              <p class="text-sm text-muted line-clamp-2">
                {category.data.description}
              </p>
            </a>
          ))}
        </div>
      </div>

      <!-- Control Panel Categories Section -->
      <div>
        <div class="mb-8">
          <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-3">
            Hosting Berdasarkan Control Panel
          </h2>
          <p class="text-muted">
            Pilih hosting berdasarkan panel kontrol yang Anda sukai untuk kemudahan pengelolaan
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {controlPanelCategories.map((category) => (
            <a
              href={`/direktori/${category.id}/`}
              class="group block rounded-md bg-white border border-gray-200 p-6 shadow-md hover:no-underline transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
            >
              <div class="mb-4">
                <h3 class="font-semibold text-heading group-hover:text-primary transition-colors text-lg mb-2">
                  {category.data.title}
                </h3>
                <p class="text-sm text-muted">
                  {category.actualProviderCount} provider tersedia
                </p>
              </div>

              <p class="text-sm text-muted line-clamp-2">
                {category.data.description}
              </p>
            </a>
          ))}
        </div>
      </div>
      
    </div>
  </section>
</Layout>
