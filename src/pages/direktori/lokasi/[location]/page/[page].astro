---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection } from 'astro:content';
import {
  getLocationPaths,
  getLocationBySlug,
  getProvidersByLocation,
  formatLocationDisplay,
} from '~/utils/locationUtils';
import ProviderList from '~/components/widgets/ProviderList.astro';

export async function getStaticPaths() {
  const hostingProviders = await getCollection('hosting-providers');
  const activeProviders = hostingProviders.filter((provider) => provider.data.isActive);
  const locationPaths = getLocationPaths(activeProviders);

  const PROVIDERS_PER_PAGE = 20;
  const paths: Array<{
    params: { location: string; page: string };
    props: { locationSlug: string };
  }> = [];

  for (const locationSlug of locationPaths) {
    // Get providers for this location
    const providers = getProvidersByLocation(activeProviders, locationSlug);
    const totalPages = Math.ceil(providers.length / PROVIDERS_PER_PAGE);

    // Generate pagination paths (starting from page 2, since page 1 is handled by [location].astro)
    for (let page = 2; page <= totalPages; page++) {
      paths.push({
        params: {
          location: locationSlug,
          page: page.toString(),
        },
        props: {
          locationSlug,
        },
      });
    }
  }

  return paths;
}

const { location: locationSlug, page: pageParam } = Astro.params;

const currentPage = parseInt(pageParam || '1', 10);

// Get all hosting providers
const hostingProviders = await getCollection('hosting-providers');
const activeProviders = hostingProviders.filter((provider) => provider.data.isActive);

// Get location data and providers
const locationData = getLocationBySlug(activeProviders, locationSlug!);
const providers = getProvidersByLocation(activeProviders, locationSlug!);

if (!locationData) {
  return Astro.redirect('/direktori/lokasi/');
}

// Sort providers by badge priority, then featured status, then by modified date latest
const sortedProviders = providers.sort((a, b) => {
  // Get badge types
  const aSponsored = a.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const bSponsored = b.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const aPromoted = a.data.badges?.some(badge => badge.type === 'promoted') || false;
  const bPromoted = b.data.badges?.some(badge => badge.type === 'promoted') || false;
  const aRecommended = a.data.badges?.some(badge => badge.type === 'recommended') || false;
  const bRecommended = b.data.badges?.some(badge => badge.type === 'recommended') || false;
  const aVerified = a.data.badges?.some(badge => badge.type === 'verified') || false;
  const bVerified = b.data.badges?.some(badge => badge.type === 'verified') || false;

  // Sponsored always first
  if (aSponsored && !bSponsored) return -1;
  if (!aSponsored && bSponsored) return 1;
  
  // If both or neither are sponsored, check promoted
  if (!aSponsored && !bSponsored) {
    if (aPromoted && !bPromoted) return -1;
    if (!aPromoted && bPromoted) return 1;
  }
  
  // If both or neither are sponsored/promoted, check recommended
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted) {
    if (aRecommended && !bRecommended) return -1;
    if (!aRecommended && bRecommended) return 1;
  }
  
  // If both or neither are sponsored/promoted/recommended, check verified
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted && !aRecommended && !bRecommended) {
    if (aVerified && !bVerified) return -1;
    if (!aVerified && bVerified) return 1;
  }

  // If all badge statuses are equal, sort by featured status
  if (a.data.isFeatured && !b.data.isFeatured) return -1;
  if (!a.data.isFeatured && b.data.isFeatured) return 1;

  // Finally, sort by most recently modified
  const aModified = new Date(a.data.modifiedAt || 0).getTime();
  const bModified = new Date(b.data.modifiedAt || 0).getTime();
  return bModified - aModified;
});

// Pagination logic
const PROVIDERS_PER_PAGE = 20;
const totalPages = Math.ceil(providers.length / PROVIDERS_PER_PAGE);
const startIndex = (currentPage - 1) * PROVIDERS_PER_PAGE;
const endIndex = startIndex + PROVIDERS_PER_PAGE;
const paginatedProviders = sortedProviders.slice(startIndex, endIndex);

// Generate dynamic title and subtitle
const dynamicTitle = `Temukan ${providers.length} Provider Hosting ${locationData.name} Terbaik`;
const subtitle = `Data center di ${formatLocationDisplay(locationData)} untuk performa optimal dengan latensi rendah`;

// Add page number to title for SEO
const pageTitle =
  currentPage > 1
    ? `${providers.length}+ Provider Hosting ${locationData.name} - Halaman ${currentPage} dari ${totalPages}`
    : `${providers.length}+ Provider Hosting ${locationData.name} - Data Center ${locationData.country}`;

const metadata = {
  title: pageTitle,
  description: `Temukan ${providers.length}+ provider hosting terbaik dengan data center di ${formatLocationDisplay(locationData)}. Halaman ${currentPage} dari ${totalPages}. Performa optimal dengan latensi rendah untuk target audience lokal.`,
  canonical: `https://penasihathosting.com/direktori/lokasi/${locationSlug}/`,
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    images: [
      {
        url: `https://img.penasihathosting.com/og/lokasi-${locationSlug}.webp`,
        width: 1200,
        height: 630,
      },
    ],
  },
};

// Redirect to first page if this is page 1 (should be handled by [location].astro)
if (currentPage === 1) {
  return Astro.redirect(`/direktori/lokasi/${locationSlug}/`);
}

// Redirect to last valid page if page number is too high
if (currentPage > totalPages) {
  return Astro.redirect(`/direktori/lokasi/${locationSlug}/page/${totalPages}/`);
}
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 bg-bg-muted border-b border-gray-200">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori/lokasi/" class="text-muted hover:text-primary">Lokasi</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href={`/direktori/lokasi/${locationSlug}/`} class="text-muted hover:text-primary">{locationData.name}</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">Halaman {currentPage}</li>
      </ol>
    </div>
  </nav>

  <!-- Header -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <div class="border-t-8 border-primary pt-4 mb-8">
        <div class="flex items-center space-x-3 mb-4">
          <span class="text-4xl">{locationData.flag}</span>
          <h1 class="text-3xl font-bold text-heading md:text-4xl">
            Hosting {locationData.name}
            {currentPage > 1 && <span class="text-muted"> - Halaman {currentPage}</span>}
          </h1>
        </div>
        <p class="text-lg text-muted max-w-2xl">
          {providers.length} provider hosting dengan data center di {formatLocationDisplay(locationData)}. Pilih hosting
          dengan latensi rendah untuk performa optimal.
          {currentPage > 1 && ` Menampilkan halaman ${currentPage} dari ${totalPages}.`}
        </p>
      </div>
    </div>
  </section>

  <!-- Providers List -->
  <ProviderList
    providers={paginatedProviders}
    currentPage={currentPage}
    totalPages={totalPages}
    baseUrl={`/direktori/lokasi/${locationSlug}/`}
    title={dynamicTitle}
    subtitle={subtitle}
    showPagination={totalPages > 1}
  />

</Layout>
