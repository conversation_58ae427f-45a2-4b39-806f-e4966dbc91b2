---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection } from 'astro:content';
import { getAllLocations } from '~/utils/locationUtils';
import BackToTop from '~/components/common/BackToTop.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';
import LocationCard from '~/components/direktori/common/LocationCard.astro';

// Get all hosting providers
const hostingProviders = await getCollection('hosting-providers');
const activeProviders = hostingProviders.filter(provider => provider.data.isActive);

// Get all locations
const allLocations = getAllLocations(activeProviders);

const metadata = {
  title: 'Semua Lokasi Data Center Hosting Indonesia - Direktori Lengkap',
  description: `Jelajahi ${allLocations.length} lokasi data center hosting Indonesia. Temukan provider hosting berdasarkan lokasi data center terdekat dengan target audience Anda.`,
  canonical: 'https://penasihathosting.com/direktori/lokasi/',
  openGraph: {
    type: 'website',
    images: [
      {
        url: 'https://img.penasihathosting.com/og/direktori-lokasi.webp',
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 bg-bg-muted border-b border-gray-200">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">Lokasi Data Center</li>
      </ol>
    </div>
  </nav>

  <!-- Header -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Disclosure -->
      <Disclosure />
      <div class="border-t-8 border-primary pt-4 mb-8">
        <h1 class="text-3xl font-bold text-heading md:text-4xl mb-4">
          Lokasi Data Center Hosting
        </h1>
        <p class="text-lg text-muted max-w-2xl">
          Pilih provider hosting berdasarkan lokasi data center. Semakin dekat data center dengan target audience,
          semakin cepat loading website Anda.
        </p>
      </div>
    </div>
  </section>

  <!-- Locations Grid -->
  <section class="pb-12">
    <div class="mx-auto max-w-global px-4 md:px-6">
      {allLocations.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {allLocations.map((location) => (
            <LocationCard location={location} />
          ))}
        </div>
      ) : (
        <div class="text-center py-12">
          <div class="text-6xl mb-4">🌍</div>
          <h3 class="text-xl font-semibold text-heading mb-2">
            Belum Ada Data Center
          </h3>
          <p class="text-muted">
            Informasi lokasi data center akan segera tersedia.
          </p>
        </div>
      )}
    </div>
  </section>

  <!-- Info Section -->
  <section class="py-12 bg-gray-50">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <div>
        <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-6">
          Mengapa Lokasi Data Center Penting?
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="bg-white rounded-md p-6 shadow-sm">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex h-10 w-10 items-center justify-center rounded-md bg-blue-100">
                <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 class="font-semibold text-heading">Kecepatan Loading</h3>
            </div>
            <p class="text-muted text-sm">
              Data center yang dekat dengan pengunjung website menghasilkan loading time yang lebih cepat 
              karena jarak tempuh data yang lebih pendek.
            </p>
          </div>

          <div class="bg-white rounded-md p-6 shadow-sm">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex h-10 w-10 items-center justify-center rounded-md bg-green-100">
                <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 class="font-semibold text-heading">SEO Performance</h3>
            </div>
            <p class="text-muted text-sm">
              Google mempertimbangkan kecepatan website sebagai faktor ranking. 
              Data center lokal membantu meningkatkan performa SEO.
            </p>
          </div>

          <div class="bg-white rounded-md p-6 shadow-sm">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex h-10 w-10 items-center justify-center rounded-md bg-purple-100">
                <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <h3 class="font-semibold text-heading">Kepatuhan Regulasi</h3>
            </div>
            <p class="text-muted text-sm">
              Beberapa industri memerlukan data disimpan di lokasi tertentu untuk 
              memenuhi regulasi pemerintah dan standar keamanan data.
            </p>
          </div>

          <div class="bg-white rounded-md p-6 shadow-sm">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex h-10 w-10 items-center justify-center rounded-md bg-orange-100">
                <svg class="h-5 w-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h3 class="font-semibold text-heading">User Experience</h3>
            </div>
            <p class="text-muted text-sm">
              Pengalaman pengguna yang lebih baik dengan loading cepat meningkatkan 
              engagement dan mengurangi bounce rate website.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <BackToTop />
</Layout>
