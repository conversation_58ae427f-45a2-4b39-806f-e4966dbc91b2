---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection } from 'astro:content';
import Button from '~/components/ui/Button.astro';
import ImageModal from '~/components/direktori/common/ImageModal.astro';
import PromoCodeCopy from '~/components/direktori/common/PromoCodeCopy.astro';
import RelatedProviders from '~/components/direktori/hosting/RelatedProviders.astro';
import SponsoredProviders from '~/components/direktori/common/SponsoredProviders.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';
import { formatPeriodToIndonesian } from '~/utils/utils';

export async function getStaticPaths() {
  const providers = await getCollection('hosting-providers');
  
  return providers
    .filter(provider => provider.data.isActive)
    .map((provider) => ({
      params: { slug: provider.data.slug },
      props: { provider },
    }));
}

const { provider } = Astro.props;

// Detect if current provider is sponsored
const isSponsored = provider.data.badges?.some(badge => badge.type === 'sponsored') || false;

// Resolve badge labels from content data
const sponsoredLabel = provider.data.badges?.find(b => b.type === 'sponsored')?.label || 'Sponsored';

// Get related providers (same categories, excluding current) - Random 9 providers
const allProviders = await getCollection('hosting-providers');
const filteredProviders = allProviders
  .filter(p =>
    p.data.isActive &&
    p.data.slug !== provider.data.slug &&
    p.data.categories.some(cat => provider.data.categories.includes(cat))
  );

// Shuffle array and take 9 random providers
const shuffled = filteredProviders.sort(() => 0.5 - Math.random());
const relatedProviders = shuffled.slice(0, 9);

// Detect control panels from the new controlPanels field
const controlPanels = (provider.data.controlPanels || []).map(panel => {
  switch(panel) {
    case 'cpanel':
      return { name: 'cPanel', slug: 'cpanel-hosting', icon: 'tabler:layout-dashboard' };
    case 'directadmin':
      return { name: 'DirectAdmin', slug: 'directadmin-hosting', icon: 'tabler:settings' };
    case 'plesk':
      return { name: 'Plesk', slug: 'plesk-hosting', icon: 'tabler:layout-grid' };
    default:
      return null;
  }
}).filter((cp): cp is NonNullable<typeof cp> => cp !== null);

// Determine if this is a cloud management platform or hosting provider
const isCloudManagementPlatform = provider.data.categories.includes('cloud-management-platform');
const hasDatacenters = provider.data.datacenters && provider.data.datacenters.length > 0;

const metadata = {
  title: `${provider.data.displayName || provider.data.name} - Review & Informasi`,
  description: `Informasi lengkap ${provider.data.name}: ${provider.data.description}`,
  canonical: `https://penasihathosting.com/direktori/hosting/${provider.data.slug}/`,
  openGraph: {
    type: 'website',
    images: [
      {
        url: provider.data.featuredImage || `https://img.penasihathosting.com/og/provider-${provider.data.slug}.webp`,
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="bg-bg-muted py-2 border-b border-gray-200">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/direktori-hosting/" class="text-muted hover:text-primary">Direktori</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">{provider.data.displayName || provider.data.name}</li>
      </ol>
    </div>
  </nav>

  	<!-- Provider Header -->
	<section class={`py-6 ${isSponsored ? 'bg-gradient-to-br from-purple-50 to-white' : ''}`}>
    	<div class="mx-auto max-w-4xl px-4 md:px-6">
      	<!-- Disclosure -->
      	<Disclosure />
      
      		<!-- Single Column Layout -->
		<div class={`${isSponsored ? 'pt-2' : 'border-t-8 border-primary pt-4'}`}>
        {isSponsored && (
          <div class="h-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-t mb-3"></div>
        )}
        	<!-- Desktop Layout -->
        	<div class="hidden md:flex items-start space-x-6 mb-8 relative">
          <img
            src={provider.data.logo}
            alt={`${provider.data.name} logo`}
            class="h-24 w-28 rounded-xl object-contain border bg-white border-gray-200 p-2"
            loading="eager"
          />
          <div class="flex-1 pr-40">
            	<div class="flex items-center space-x-3 mb-2">
              	<h1 class="text-3xl font-bold text-heading">
                {provider.data.displayName || provider.data.name}
              	</h1>
              {isSponsored && (
                <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">👑 {sponsoredLabel}</span>
              )}
            </div>

            <p class="text-muted mb-4">
              {provider.data.description}
            </p>

            <div class="flex items-center space-x-6 text-sm text-muted">
              <div class="flex items-center space-x-2">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>{provider.data.company.headquarters.join(', ')}</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span>Sejak {provider.data.company.founded}</span>
              </div>
            </div>
          </div>

          <!-- CTA Button - Desktop Top Right -->
            	<div class="absolute top-0 right-0">
              	<Button
                variant="primary"
                href={provider.data.affiliateLink || provider.data.website}
                target="_blank"
                rel="noopener"
                class={`rounded-lg px-4 py-2 text-sm font-semibold transition-colors ${isSponsored ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700' : ''}`}
                icon="tabler:external-link"
              	>
                Kunjungi Website
              	</Button>
            	</div>
        </div>

        <!-- Mobile Layout -->
        <div class="md:hidden mb-16">
          <div class="flex items-start space-x-4 mb-4">
            <img
              src={provider.data.logo}
              alt={`${provider.data.name} logo`}
              class="h-20 w-20 rounded-xl object-contain border bg-white border-gray-200 p-2"
              loading="eager"
            />
            <div class="flex-1">
              <div class="mb-2">
                <h1 class="text-2xl font-bold text-heading">
                  {provider.data.displayName || provider.data.name}
                </h1>
              </div>

              <p class="text-base text-muted mb-3">
                {provider.data.description}
              </p>

              <div class="flex flex-col space-y-2 text-sm text-muted">
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span>{provider.data.company.headquarters.join(', ')}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>Sejak {provider.data.company.founded}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- CTA Button - Mobile Full Width -->
          	<Button
            variant="primary"
            href={provider.data.affiliateLink || provider.data.website}
            target="_blank"
            rel="noopener"
            class={`w-full rounded-lg px-4 py-3 text-sm font-semibold transition-colors justify-center ${isSponsored ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700' : ''}`}
            icon="tabler:external-link"
          	>
            Kunjungi Website
          	</Button>
        </div>

        <!-- Sponsored Providers Section -->
        <SponsoredProviders providers={allProviders} currentSlug={provider.data.slug} />

        <!-- About Section -->
        {provider.data.longDescription && (
          <div class="mb-8">
            <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-4">Tentang {provider.data.displayName || provider.data.name}</h2>
            <div>
              <div class="prose prose-gray max-w-none">
                {provider.data.longDescription.split('\n\n').map((paragraph: string) => (
                  <p class="text-muted mb-4 leading-relaxed">{paragraph}</p>
                ))}
              </div>
            </div>
          </div>
        )}

        <!-- Harga & Promo Section -->
        <div class="mb-16">
          <h3 class="font-bold text-heading mb-4">Harga & Promo</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-fit">
            <!-- Harga Mulai -->
            <div class="bg-bg-section border border-gray-200 rounded-lg p-4">
              <div class="flex items-start gap-3">
                <div class="w-8 h-8 border border-gray-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <svg class="w-4 h-4 " fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-heading mb-2">Harga Mulai {provider.data.displayName || provider.data.name}</h3>
                  <div class="mb-3 flex items-center gap-1">
                    <div class="text-primary">
                      Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')}
                    </div>
                    <div class="text-sm text-muted mb-0">/bulan</div>
                  </div>
                  <a
                    href={provider.data.affiliateLink || provider.data.website}
                    target="_blank"
                    rel="noopener"
                    class="inline-flex items-center gap-1 text-sm !text-accent transition-colors font-medium"
                  >
                    Lihat detail harga
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- Promo -->
            {provider.data.pricing.promoDescription && (
              <div class="bg-gradient-to-r from-red-50 to-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 border border-red-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 bg-red-100">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <h3 class="font-semibold text-heading mb-2">Promo {provider.data.displayName || provider.data.name}</h3>
                    <div class="mb-3">
                      <div class="mb-2">{provider.data.pricing.promoDescription}</div>
                      {provider.data.pricing.promoCode && provider.data.pricing.promoCode !== "not needed" ? (
                        <div class="flex items-center gap-1">
                          <div class="text-sm text-muted mb-1">Kode kupon:</div>
                          <button
                            data-promo-code={provider.data.pricing.promoCode}
                            class="font-mono font-medium text-primary hover:text-primary/80 transition-colors border border-gray-300 rounded px-2 py-1 text-sm hover:bg-gray-50"
                            title="Copy kode promo"
                          >
                            {provider.data.pricing.promoCode}
                          </button>
                        </div>
                      ) : (
                        <div class="flex items-center gap-2">
                          <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          <span class="text-sm text-green-600 font-medium">Tidak memerlukan kode kupon</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <!-- Company Overview -->
        <div class="mb-16">
          <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-4">Informasi</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="font-semibold text-heading mb-2">Informasi Perusahaan</h3>
              <div class="space-y-1 text-sm">
                <div class="flex items-center gap-2">
                  <span class="font-medium min-w-[80px]">Nama:</span>
                  <span class="flex-1">{provider.data.company.name}</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="font-medium min-w-[80px]">Didirikan:</span>
                  <span class="flex-1">{provider.data.company.founded}</span>
                </div>
                <div class="flex items-start gap-2">
                  <span class="font-medium min-w-[80px]">Alamat:</span>
                  <span class="flex-1">{provider.data.company.address}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 class="font-semibold text-heading mb-2">Support Channel</h3>
              <div class="flex flex-wrap gap-2">
                {provider.data.company.supportChannels.map((channel) => (
                  <span class="inline-flex items-center px-2 py-1 rounded-md bg-bg-section text-primary border border-gray-200 text-sm font-medium">
                    {channel === 'live-chat' ? 'Live Chat' :
                     channel === 'ticket' ? 'Ticket System' :
                     channel === 'phone' ? 'Telepon' :
                     channel === 'email' ? 'Email' :
                     channel === 'documentation' ? 'Documentation' :
                     channel === 'community' ? 'Community' : channel}
                  </span>
                ))}
              </div>
            </div>
          </div>
          {/* Kategori Hosting */}
          <div class="mt-8">
            <h3 class="font-medium text-heading mb-2">Kategori</h3>
            <div class="flex flex-wrap gap-2">
              {provider.data.categories.map((category) => (
                <a
                  href={`/direktori/${category}/`}
                  class="inline-flex items-center px-2 py-1 rounded-md bg-bg-section border-gray-200 border text-sm font-medium hover:bg-bg-muted transition-colors hover:no-underline hover:text-primary"
                >
                  {category.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                </a>
              ))}
            </div>
          </div>

          {/* Control Panel */}
          {controlPanels.length > 0 && (
            <div class="mt-4">
              <h3 class="font-medium text-heading mb-2">Control Panel</h3>
              <div class="flex flex-wrap gap-2">
                {controlPanels.map((cp) => (
                  <a
                    href={`/direktori/${cp.slug}/`}
                    class="inline-flex items-center px-2 py-1 rounded-md bg-bg-section border-gray-200 border text-sm font-medium hover:bg-bg-muted transition-colors hover:no-underline hover:text-primary gap-1"
                  >                    
                    {cp.name}
                  </a>
                ))}
              </div>
            </div>
          )}
          
          {/* Lokasi Data Center atau Server Target - tampilkan berdasarkan tipe provider */}
          {hasDatacenters && (
            <div class="mt-8">
              <h3 class="font-medium text-heading mb-2">
                {isCloudManagementPlatform ? 'Server Target yang Didukung' : 'Lokasi Data Center'}
              </h3>
              <div class="flex flex-wrap gap-2">
                {provider.data.datacenters.map((dc) => (
                  <a
                    href={`/direktori/lokasi/${dc.location.toLowerCase().replace(/\s+/g, '-')}/`}
                    class="inline-flex items-center px-2 py-1 rounded-md bg-bg-section border-gray-200 border text-sm font-medium hover:bg-bg-muted transition-colors hover:no-underline hover:text-primary gap-1"
                  >
                    <span class="text-lg">{dc.flag}</span>
                    {dc.location}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>

        <!-- Media Gallery -->
        {provider.data.gallery && provider.data.gallery.length > 0 && (
          <div class="mb-16">
            <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-4">Media & Screenshots</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              {provider.data.gallery.map((image: string, index: number) => (
                <button
                  class="image-modal-trigger relative overflow-hidden rounded-sm border border-gray-200 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer text-left"
                  data-image={image}
                  data-alt={`${provider.data.name} screenshot ${index + 1}`}
                >
                  <img
                    src={image}
                    alt={`${provider.data.name} screenshot ${index + 1}`}
                    class="w-full h-48 object-cover"
                    loading="lazy"
                  />
                </button>
              ))}
            </div>
          </div>
        )}

        <!-- Features -->
        {provider.data.features && provider.data.features.length > 0 && (
          <div class="mb-16">
            <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-2">Fitur-fitur {provider.data.displayName || provider.data.name}</h2>
            <p class="text-muted text-sm mb-4">Berikut adalah berbagai fitur yang ditawarkan oleh {provider.data.displayName || provider.data.name} untuk mendukung kebutuhan hosting Anda secara optimal.</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              {provider.data.features.map((feature) => (
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-normal text-muted">{feature.name}</h3>
                    {feature.description && (
                      <p class="text-xs text-muted mt-1">{feature.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Paket Hosting Section - Compact Design */}
        {provider.data.pricing.plans.length > 0 && (
          <section class="mb-16">
            <h2 class="border-t-2 border-primary pt-2 text-2xl font-bold text-heading mb-2">Paket Hosting {provider.data.displayName || provider.data.name}</h2>
            <p class="text-sm text-muted mb-4">
              Informasi harga {provider.data.displayName || provider.data.name} ini diambil langsung dari website penyedia layanan atau dari materi yang tersedia untuk umum. Untuk mendapatkan informasi harga yang paling akurat dan terbaru, kami sarankan Anda mengunjungi langsung website {provider.data.displayName || provider.data.name} secara langsung.<br/>
              <br/>
              Informasi harga terakhir diperbarui pada {(() => {
                const raw = provider.data.modifiedAt;
                if (!raw) return "Tanggal tidak tersedia";
                const date = typeof raw === "string" ? new Date(raw) : raw;
                return isNaN(date.getTime())
                  ? "Tanggal tidak tersedia"
                  : date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
              })()}.
            </p>

            <!-- Compact Pricing Cards -->
            <div class="flex flex-col gap-3">
              {provider.data.pricing.plans.map((plan) => {
                const maxFeatures = 3;
                const featureList = plan.features.slice(0, maxFeatures).join(', ');
                const moreCount = plan.features.length - maxFeatures;
                return (
                  <div class="border rounded-lg bg-bg-section p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2 border-gray-200">
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-1 mb-1">
                        <h3 class="text-base font-semibold text-heading truncate">{plan.name}</h3>
                        {plan.promoCode && (
                          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700 border border-red-200">
                            🏷️ {plan.promoDescription || 'Promo Khusus'}
                          </span>
                        )}
                      </div>
                      <div class="flex items-center gap-1 mb-1">
                        <span class="font-bold text-primary">
                          Rp {plan.price.toLocaleString('id-ID')}
                        </span>
                        <span class="text-muted">/{formatPeriodToIndonesian(plan.period)}</span>
                      </div>
                      {plan.promoCode && (
                        <div class="text-xs text-green-600 font-medium mb-1">
                          Kode: <span class="font-mono bg-green-50 px-1 rounded">{plan.promoCode}</span>
                        </div>
                      )}
                      <div class="text-sm text-muted truncate">
                        {featureList}
                        {moreCount > 0 && `, +${moreCount} fitur`}
                      </div>
                    </div>
                    <div class="flex-shrink-0 flex flex-col gap-1 min-w-[120px]">
                      <a
                        href={provider.data.affiliateLink || provider.data.website}
                        target="_blank"
                        rel="noopener"
                        class="block rounded px-3 py-1.5 text-center font-medium text-sm transition-colors border border-gray-300 bg-bg-section hover:bg-bg-muted hover:no-underline hover:text-primary"
                      >
                        Pilih Paket
                      </a>
                    </div>
                  </div>
                );
              })}
            </div>
          </section>
        )}
      </div>
    </div>
  </section>

  <!-- Related Providers -->
  <RelatedProviders providers={relatedProviders} />

</Layout>

<!-- Include Components -->
<ImageModal />
<PromoCodeCopy />
