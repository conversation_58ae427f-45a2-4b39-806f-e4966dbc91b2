---
import Layout from '~/layouts/PageLayout.astro';
import ProviderList from '~/components/widgets/ProviderList.astro';
import BackToTop from '~/components/common/BackToTop.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';
import { getCollection } from 'astro:content';

// Get hosting providers
const hostingProviders = await getCollection('hosting-providers');

// Filter active providers
const activeProviders = hostingProviders.filter((provider) => provider.data.isActive);

// Get latest providers (sorted by modifiedAt/createdAt)
const latestProviders = activeProviders
  .sort((a, b) => {
    const dateA = new Date(a.data.modifiedAt || a.data.createdAt || 0);
    const dateB = new Date(b.data.modifiedAt || b.data.createdAt || 0);
    return dateB.getTime() - dateA.getTime();
  })
  .slice(0, 20); // Get latest 20 providers

// Popular categories and locations for suggestions
const popularCategories = [
  { name: 'Shared Hosting', slug: 'shared-hosting' },
  { name: 'Cloud Hosting', slug: 'cloud-hosting' },
  { name: 'VPS', slug: 'unmanaged-vps' },
  { name: 'WordPress Hosting', slug: 'wordpress-hosting' },
  { name: 'Dedicated Server', slug: 'dedicated-server' },
  { name: 'Reseller Hosting', slug: 'reseller-hosting' }
];

const popularLocations = [
  { name: 'Indonesia', slug: 'indonesia' },
  { name: 'Singapore', slug: 'singapore' },
  { name: 'United States', slug: 'united-states' }
];

// Stats
const totalProviders = activeProviders.length;

const metadata = {
  title: `${totalProviders}+ Provider Hosting Terdaftar di Direktori Hosting`,
  description: `Temukan provider hosting terbaik. Bandingkan fitur, harga, dan review dari ${totalProviders}+ provider hosting.`,
  canonical: 'https://penasihathosting.com/direktori-hosting/',
  openGraph: {
    type: 'website',
    title: `Direktori Hosting - ${totalProviders}+ Provider Hosting Terdaftar`,
    description: `Temukan provider hosting Indonesia terbaik. Bandingkan fitur, harga, dan review dari ${totalProviders}+ provider hosting.`,
    url: 'https://penasihathosting.com/direktori-hosting/',
    images: [
      {
        url: 'https://img.penasihathosting.com/og/direktori-hosting.webp',
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumb -->
  <nav class="py-2 border-b border-gray-200 bg-bg-muted">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary">Home</a>
        </li>
        <li class="text-gray-400">/</li>
        <li class="text-heading font-medium">Direktori Hosting</li>
      </ol>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="py-6">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Disclosure -->
      <Disclosure />
      <div>
        <div class="border-t-8 border-primary pt-4 mb-8">
          <h1 class="text-3xl font-bold tracking-tight text-heading md:text-4xl lg:text-5xl mb-4">
            Direktori Hosting Indonesia
          </h1>
          <p class="text-lg text-muted md:text-xl max-w-2xl">
            Temukan provider hosting terbaik dengan perbandingan lengkap fitur, harga, dan review dari {
              totalProviders
            }+ provider terpercaya.
          </p>
        </div>

        <!-- Search Box -->
        <div class="mx-auto">
          <div class="relative">
            <input
              type="text"
              placeholder="Cari provider hosting..."
              class="w-full rounded-md border border-gray-300 px-4 py-3 pl-10 focus:border-primary focus:ring-1 focus:ring-primary"
              id="search-input"
            />
            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <!-- Search Results -->
            <div
              id="search-results"
              class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg hidden z-10 max-h-80 overflow-y-auto"
            >
              <!-- Results will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Popular Categories -->
        <div class="mt-8">
          <h3 class="text-lg font-semibold text-heading mb-4">Kategori Populer</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <a 
              href="/direktori/shared-hosting/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">Shared Hosting</h4>
                  <p class="text-xs text-muted mt-1">Hosting terjangkau</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/cloud-hosting/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">Cloud Hosting</h4>
                  <p class="text-xs text-muted mt-1">Scalable & reliable</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/unmanaged-vps/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">VPS</h4>
                  <p class="text-xs text-muted mt-1">Virtual private server</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/wordpress-hosting/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">WordPress Hosting</h4>
                  <p class="text-xs text-muted mt-1">Optimized for WP</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/dedicated-server/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">Dedicated Server</h4>
                  <p class="text-xs text-muted mt-1">Full server control</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/colocation-server/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">Colocation Server</h4>
                  <p class="text-xs text-muted mt-1">Data center space</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/reseller-hosting/" 
              class="group bg-bg-section border border-gray-200 rounded-md p-4 hover:border-primary hover:bg-primary/5 transition-all duration-200 hover:no-underline"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/10 group-hover:bg-primary/20 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-heading text-sm group-hover:text-primary transition-colors">Reseller Hosting</h4>
                  <p class="text-xs text-muted mt-1">Sell hosting services</p>
                </div>
              </div>
            </a>

            <a 
              href="/direktori/kategori/" 
              class="group bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-md p-4 hover:no-underline hover:from-primary/10 hover:to-primary/20 hover:border-primary/40 transition-all duration-200"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary/20 group-hover:bg-primary/30 rounded-md flex items-center justify-center transition-colors">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7l2 2-2 2m0 8l2 2-2 2"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-primary text-sm group-hover:text-primary transition-colors">Lihat Semua</h4>
                  <p class="text-xs text-primary/70 mt-1">Explore all categories</p>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Custom Header Section -->
  <section class="pt-12">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <!-- Header with Submit Button -->
      <div>
        <h2 class="border-t-2 border-primary pt-2 text-xl font-normal text-heading mb-4">
          Provider Hosting Terbaru di Direktori Kami
        </h2>
      </div>
    </div>
  </section>

  <!-- Latest Providers -->
  <ProviderList
    providers={latestProviders}
    showPagination={false}
  />

  <!-- Back to Top Button -->
  <BackToTop />
</Layout>

<script define:vars={{ providers: activeProviders, popularCategories, popularLocations }}>
  // Search functionality - enhanced with categories and locations
  const initSearch = () => {
    // Small delay to ensure all elements are in the DOM
    setTimeout(() => {
      try {
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');

        if (!searchInput || !searchResults) return;

        let searchTimeout;

        // Remove existing event listeners to avoid duplicates
        const newSearchInput = searchInput.cloneNode(true);
        searchInput.parentNode.replaceChild(newSearchInput, searchInput);

        newSearchInput.addEventListener('input', function (e) {
          const query = e.target.value.toLowerCase().trim();

          // Clear previous timeout
          clearTimeout(searchTimeout);

          if (query.length === 0) {
            // Show suggestions when input is empty
            showSuggestions();
            return;
          }

          if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
          }

          // Debounce search
          searchTimeout = setTimeout(() => {
            performSearch(query);
          }, 300);
        });

        // Show suggestions on focus (only if input is empty)
        newSearchInput.addEventListener('focus', function (e) {
          if (e.target.value.trim().length === 0) {
            showSuggestions();
          }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function (e) {
          if (!e.target.closest('#search-input') && !e.target.closest('#search-results')) {
            searchResults.classList.add('hidden');
          }
        });

        function showSuggestions() {
          searchResults.innerHTML = `
            <div class="p-4">
              <div class="mb-4">
                <h4 class="text-sm font-medium text-heading mb-2">Kategori Populer</h4>
                <div class="space-y-1">
                  ${popularCategories.map(category => `
                    <a href="/direktori/${category.slug}/" class="block px-3 py-2 text-sm text-muted hover:bg-bg-muted hover:text-primary rounded-md transition-colors">
                      ${category.name}
                    </a>
                  `).join('')}
                </div>
              </div>
              <div>
                <h4 class="text-sm font-medium text-heading mb-2">Lokasi Populer</h4>
                <div class="space-y-1">
                  ${popularLocations.map(location => `
                    <a href="/direktori/lokasi/${location.slug}/" class="block px-3 py-2 text-sm text-muted hover:bg-bg-muted hover:text-primary rounded-md transition-colors">
                      ${location.name}
                    </a>
                  `).join('')}
                </div>
              </div>
            </div>
          `;
          searchResults.classList.remove('hidden');
        }

        function performSearch(query) {
          // Search providers
          const filteredProviders = providers.filter((provider) => {
            const name = (provider.data.displayName || provider.data.name).toLowerCase();
            const description = provider.data.description.toLowerCase();
            const categories = provider.data.categories.join(' ').toLowerCase();
            const locations = provider.data.datacenters?.map(dc => dc.location.toLowerCase()).join(' ') || '';
            const countries = provider.data.datacenters?.map(dc => dc.country.toLowerCase()).join(' ') || '';

            return name.includes(query) || 
                   description.includes(query) || 
                   categories.includes(query) || 
                   locations.includes(query) || 
                   countries.includes(query);
          });

          // Search categories
          const matchingCategories = popularCategories.filter(category => 
            category.name.toLowerCase().includes(query) || category.slug.includes(query)
          );

          // Search locations
          const matchingLocations = popularLocations.filter(location => 
            location.name.toLowerCase().includes(query) || location.slug.includes(query)
          );

          displayResults(filteredProviders, matchingCategories, matchingLocations, query);
        }

        function displayResults(providerResults, categoryResults, locationResults, query) {
          let resultsHTML = '';

          // Categories section
          if (categoryResults.length > 0) {
            resultsHTML += `
              <div class="border-b border-gray-100 last:border-0">
                <div class="px-3 py-2 bg-gray-50 text-xs font-medium text-muted uppercase tracking-wide">
                  Kategori
                </div>
                ${categoryResults.map(category => `
                  <a href="/direktori/${category.slug}/" class="block p-3 hover:bg-bg-muted border-b border-kategori-hover last:border-0 hover:no-underline">
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7l2 2-2 2m0 8l2 2-2 2"/>
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="font-medium text-sm text-heading">${category.name}</div>
                        <div class="text-xs text-muted">Lihat semua provider ${category.name.toLowerCase()}</div>
                      </div>
                    </div>
                  </a>
                `).join('')}
              </div>
            `;
          }

          // Locations section
          if (locationResults.length > 0) {
            resultsHTML += `
              <div class="border-b border-gray-100 last:border-0">
                <div class="px-3 py-2 bg-gray-50 text-xs font-medium text-muted uppercase tracking-wide">
                  Lokasi
                </div>
                ${locationResults.map(location => `
                  <a href="/direktori/lokasi/${location.slug}/" class="block p-3 hover:bg-bg-muted border-b border-kategori-hover last:border-0 hover:no-underline">
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="font-medium text-sm text-heading">${location.name}</div>
                        <div class="text-xs text-muted">Provider dengan data center di ${location.name}</div>
                      </div>
                    </div>
                  </a>
                `).join('')}
              </div>
            `;
          }

          // Providers section
          if (providerResults.length > 0) {
            resultsHTML += `
              <div class="border-b border-gray-100 last:border-0">
                <div class="px-3 py-2 bg-gray-50 text-xs font-medium text-muted uppercase tracking-wide">
                  Provider
                </div>
                ${providerResults.slice(0, 6).map(provider => `
                  <a href="/direktori/hosting/${provider.data.slug}/" class="block p-3 hover:bg-bg-muted border-b border-kategori-hover last:border-0 hover:no-underline">
                    <div class="flex items-center space-x-3">
                      <img src="${provider.data.logo}" alt="${provider.data.name}" class="w-8 h-8 rounded object-contain" />
                      <div class="flex-1">
                        <div class="font-medium text-sm text-heading">${provider.data.displayName || provider.data.name}</div>
                        <div class="text-xs text-muted">Mulai dari Rp ${provider.data.pricing.startingPrice.toLocaleString('id-ID')}/bulan</div>
                      </div>
                    </div>
                  </a>
                `).join('')}
                ${providerResults.length > 6 ? `
                  <div class="p-3 text-center border-t border-gray-100">
                    <a href="/direktori-hosting/" class="text-sm text-primary hover:underline">
                      Lihat semua ${providerResults.length} provider
                    </a>
                  </div>
                ` : ''}
              </div>
            `;
          }

          if (resultsHTML === '') {
            resultsHTML = `
              <div class="p-4 text-center text-muted">
                <p>Tidak ada hasil untuk "${query}"</p>
                <p class="text-xs mt-1">Coba kata kunci lain atau pilih dari kategori populer</p>
              </div>
            `;
          }

          searchResults.innerHTML = resultsHTML;
          searchResults.classList.remove('hidden');
        }

        console.log('Enhanced search functionality initialized successfully');
      } catch (error) {
        console.error('Error initializing search functionality:', error);
      }
    }, 100); // Small delay to ensure DOM is ready
  };

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initSearch);
  
  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initSearch);
  
  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initSearch);
  
  // Additional fallback for extra reliability
  window.addEventListener('load', initSearch);
</script>
