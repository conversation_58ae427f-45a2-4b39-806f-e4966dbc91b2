---
import PageLayout from '~/layouts/PageLayout.astro';
import CompareTable from '~/components/widgets/compare/CompareTable.tsx';
import { getCollection } from 'astro:content';
import { pickComparableFields } from '~/utils/compare';

// Static build: no SSR logic; filtering happens client-side

const allProviders = await getCollection('hosting-providers');
const initialData = allProviders.map(p => pickComparableFields(p));
const title = 'Perbandingan Hosting';
const description = 'Bandingkan penyedia hosting Indonesia.';
---
<PageLayout metadata={{ title, description }}>
  <section class="py-10">
    <div class="mx-auto max-w-global px-4 md:px-6">
      <h1 id="compare-page-heading" class="text-2xl font-semibold mb-2">{title}</h1>
      <p class="text-sm text-gray-600 mb-8">Pilih hingga 4 provider dari halaman direktori untuk dibandingkan. URL dapat dibagikan.</p>

      <CompareTable client:load all={initialData} />
    </div>
  </section>
</PageLayout>
