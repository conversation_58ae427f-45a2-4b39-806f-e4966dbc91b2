---
import Layout from '~/layouts/PageLayout.astro';
import { getCollection } from 'astro:content';
import BackToTop from '~/components/common/BackToTop.astro';
import Disclosure from '~/components/direktori/common/Disclosure.astro';
import Button from '~/components/ui/Button.astro';

// Get all hosting providers that have active promos
const allProviders = await getCollection('hosting-providers');
const filteredPromoProviders = allProviders.filter(provider => 
  provider.data.isActive && 
  (provider.data.pricing.promoCode || provider.data.pricing.promoDescription)
);

// Separate sponsored providers from regular promo providers
const sponsoredPromoProviders = filteredPromoProviders.filter(provider => {
  const badgeTypes = provider.data.badges?.map(badge => badge.type) || [];
  return badgeTypes.includes('sponsored');
}).sort((a, b) => a.data.name.localeCompare(b.data.name));

// Separate promoted providers
const promotedPromoProviders = filteredPromoProviders.filter(provider => {
  const badgeTypes = provider.data.badges?.map(badge => badge.type) || [];
  return badgeTypes.includes('promoted') && !badgeTypes.includes('sponsored');
}).sort((a, b) => a.data.name.localeCompare(b.data.name));

// Regular promo providers (excluding sponsored and promoted)
const regularPromoProviders = filteredPromoProviders.filter(provider => {
  const badgeTypes = provider.data.badges?.map(badge => badge.type) || [];
  return !badgeTypes.includes('sponsored') && !badgeTypes.includes('promoted');
});

// Sort regular providers by badge priority: recommended > no-badge
const promoProviders = regularPromoProviders.sort((a, b) => {
  // Get the highest priority badge for each provider
  const getBadgePriority = (provider) => {
    if (!provider.data.badges || provider.data.badges.length === 0) return 2; // no-badge
    
    const badgeTypes = provider.data.badges.map(badge => badge.type);
    if (badgeTypes.includes('recommended')) return 1;
    return 2; // no relevant badge
  };

  const priorityA = getBadgePriority(a);
  const priorityB = getBadgePriority(b);
  
  // If badge priorities are the same, sort alphabetically by name
  if (priorityA === priorityB) {
    return a.data.name.localeCompare(b.data.name);
  }
  
  return priorityA - priorityB;
});

const metadata = {
  title: 'Promo Hosting - Penawaran Terbaik untuk Web Hosting',
  description: 'Temukan penawaran terbaik dan hemat untuk layanan web hosting. Kami mengumpulkan promo dan diskon terbaru dari berbagai provider hosting untuk membantu Anda mendapatkan harga termurah.',
  canonical: 'https://penasihathosting.com/promo-hosting/',
  openGraph: {
    type: 'website',
    images: [
      {
        url: 'https://img.penasihathosting.com/og/promo-hosting.webp',
        width: 1200,
        height: 630,
      },
    ],
  },
};
---

<Layout metadata={metadata}>
  <!-- Breadcrumbs -->
  <section class="bg-bg-muted text-sm py-2 border-b border-gray-200">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-1 text-sm">
          <li>
            <a href="/" class="text-muted hover:text-primary transition-colors">
              Home
            </a>
          </li>
          <li class="flex items-center">
            <li class="text-gray-400">/</li>  
          </li>
          <li>
            <a href="/direktori-hosting/" class="text-muted hover:text-primary transition-colors">
              Direktori Hosting
            </a>
          </li>
          <li class="flex items-center">
          <li class="text-gray-400">/</li>

            <span class="text-heading font-medium">Promo Hosting</span>
          </li>
        </ol>
      </nav>
    </div>
  </section>

  <!-- Hero Section -->
  <section class="bg-bg-page dark:bg-bg-section py-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <!-- Disclosure -->
      <Disclosure />
      
      <div class="max-w-full lg:max-w-[65ch] border-t-8 border-primary pt-2">
        <h1 class="text-4xl md:text-5xl font-extrabold text-heading mb-4">
          Promo Hosting
        </h1>
        <p class="text-lg text-muted mb-6 leading-relaxed">
          Temukan penawaran terbaik dan hemat untuk layanan web hosting. Kami mengumpulkan promo dan diskon terbaru dari berbagai provider hosting untuk membantu Anda mendapatkan harga termurah.
        </p>
        
        <!-- Submit Promo CTA -->
        <div class="bg-bg-section border border-gray-200 rounded-lg p-4 mb-8">
          <div class="flex items-start gap-3">
            <div class="w-6 h-6 border border-gray-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-muted text-sm leading-relaxed">
                <strong>Submit Promo Hosting</strong> dari layanan web hosting atau web services Anda di halaman promo hosting ini. 100% gratis.
              </p>
              <div class="mt-3">
                <Button
                  href="https://tally.so/r/nP2j8d"
                  target="_blank"
                  rel="noopener nofollow"
                  variant="primary"
                  class="text-sm px-4 py-2"
                  icon="tabler:plus"
                >
                  Submit Promo Anda
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Sponsored Promo Providers Section -->
  {sponsoredPromoProviders.length > 0 && (
    <section class="pb-8">
      <div class="max-w-global mx-auto px-4 sm:px-6">
        <!-- Section Header -->
        <div class="mb-8">
          <h2 class="border-t-2 border-purple-500 pt-2 text-xl font-normal text-heading mb-2">
            Sponsored Promo Hosting
          </h2>
          <p class="text-muted">
            Provider hosting sponsor dengan penawaran khusus untuk pembaca PenasihatHosting.
          </p>
        </div>

        <!-- Sponsored Promo Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sponsoredPromoProviders.map((provider) => (
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl p-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden">

              <!-- Provider Logo & Name -->
              <div class="flex items-center mb-4">
                <img
                  src={provider.data.logo}
                  alt={`${provider.data.name} logo`}
                  class="h-12 w-16 rounded-lg object-contain border border-gray-200 bg-white p-1 flex-shrink-0 mr-3"
                  loading="lazy"
                />
                <div>
                  <h3 class="font-bold text-lg text-heading">
                    {provider.data.displayName || provider.data.name}
                  </h3>
                  <!-- Provider Badges -->
                  <div class="flex items-center gap-1 mt-1">
                    {provider.data.badges && provider.data.badges.map((badge) => {
                      if (badge.type === 'verified') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200">
                            ✓ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'sponsored') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200">
                            👑 {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'recommended') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-green-100 text-green-700 border border-green-200">
                            ⭐ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'promoted') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200">
                            🎯 {badge.label}
                          </span>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              </div>

              <!-- Price & Promo Info -->
              <div class="mb-4">
                <div class="flex items-baseline gap-2 mb-2">
                  <span class="text-2xl font-bold text-purple-600">
                    <span class="text-sm font-normal">Mulai</span>
                    {provider.data.pricing.startingPrice === 0 ? 'GRATIS' : `Rp ${provider.data.pricing.startingPrice.toLocaleString('id-ID')}`}
                  </span>
                  {provider.data.pricing.startingPrice > 0 && (
                    <span class="text-sm text-muted">/bulan</span>
                  )}
                </div>
                
                <!-- Promo Details -->
                {provider.data.pricing.promoDescription && (
                  <div class="bg-purple-100 border border-purple-300 rounded-lg p-3 mb-3">
                    <div class="text-purple-800 font-semibold text-sm mb-1">
                      🎉 {provider.data.pricing.promoDescription}
                    </div>
                    {provider.data.pricing.promoCode && (
                      <div class="text-xs text-purple-700">
                        Kode: <span class="font-mono bg-purple-200 px-2 py-1 rounded">{provider.data.pricing.promoCode}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <!-- Description -->
              <p class="text-muted text-sm line-clamp-3 mb-4">
                {provider.data.description}
              </p>

              <!-- Key Features -->
              <div class="mb-6">
                <div class="grid grid-cols-1 gap-2">
                  {provider.data.features.slice(0, 3).map((feature) => (
                    <div class="flex items-center text-xs">
                      <svg class="h-3 w-3 text-purple-500 flex-shrink-0 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span class="text-muted">{feature.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="space-y-2">
                <Button
                  href={provider.data.affiliateLink || provider.data.website}
                  target="_blank"
                  rel="noopener"
                  variant="primary"
                  class="w-full text-sm font-semibold py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 border-purple-600 hover:border-purple-700"
                  icon="tabler:external-link"
                >
                  Ambil Promo Sponsor
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )}

  <!-- Promoted Promo Providers Section -->
  {promotedPromoProviders.length > 0 && (
    <section class="pb-8">
      <div class="max-w-global mx-auto px-4 sm:px-6">
        <!-- Section Header -->
        <div class="mb-8">
          <h2 class="border-t-2 border-orange-500 pt-2 text-xl font-normal text-heading mb-2">
            Featured Promo Hosting
          </h2>
          <p class="text-muted">
            Provider hosting unggulan dengan penawaran promo terbaik yang kami rekomendasikan.
          </p>
        </div>

        <!-- Promoted Promo Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {promotedPromoProviders.map((provider) => (
            <div class="bg-gradient-to-br from-orange-50 to-orange-100 border-2 border-orange-300 rounded-xl p-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden">

              <!-- Provider Logo & Name -->
              <div class="flex items-center mb-4">
                <img
                  src={provider.data.logo}
                  alt={`${provider.data.name} logo`}
                  class="h-12 w-16 rounded-lg object-contain border border-gray-200 bg-white p-1 flex-shrink-0 mr-3"
                  loading="lazy"
                />
                <div>
                  <h3 class="font-bold text-lg text-heading">
                    {provider.data.displayName || provider.data.name}
                  </h3>
                  <!-- Provider Badges -->
                  <div class="flex items-center gap-1 mt-1">
                    {provider.data.badges && provider.data.badges.map((badge) => {
                      if (badge.type === 'verified') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200">
                            ✓ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'sponsored') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200">
                            👑 {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'recommended') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-green-100 text-green-700 border border-green-200">
                            ⭐ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'promoted') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200">
                            🎯 {badge.label}
                          </span>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              </div>

              <!-- Price & Promo Info -->
              <div class="mb-4">
                <div class="flex items-baseline gap-2 mb-2">
                  <span class="text-2xl font-bold text-orange-600">
                    <span class="text-sm font-normal">Mulai</span>
                    Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')}
                  </span>
                  <span class="text-sm text-muted">/bulan</span>
                </div>
                
                <!-- Promo Details -->
                {provider.data.pricing.promoDescription && (
                  <div class="bg-orange-100 border border-orange-300 rounded-lg p-3 mb-3">
                    <div class="text-orange-800 font-semibold text-sm mb-1">
                      🎉 {provider.data.pricing.promoDescription}
                    </div>
                    {provider.data.pricing.promoCode && (
                      <div class="text-xs text-orange-700">
                        Kode: <span class="font-mono bg-orange-200 px-2 py-1 rounded">{provider.data.pricing.promoCode}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <!-- Description -->
              <p class="text-muted text-sm line-clamp-3 mb-4">
                {provider.data.description}
              </p>

              <!-- Key Features -->
              <div class="mb-6">
                <div class="grid grid-cols-1 gap-2">
                  {provider.data.features.slice(0, 3).map((feature) => (
                    <div class="flex items-center text-xs">
                      <svg class="h-3 w-3 text-orange-500 flex-shrink-0 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span class="text-muted">{feature.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="space-y-2">
                <Button
                  href={provider.data.affiliateLink || provider.data.website}
                  target="_blank"
                  rel="noopener"
                  variant="primary"
                  class="w-full text-sm font-semibold py-3 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 border-orange-600 hover:border-orange-700"
                  icon="tabler:external-link"
                >
                  Ambil Promo Featured
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )}

  <!-- Regular Promo Providers List -->
  {promoProviders.length > 0 ? (
    <section class="pb-12">
      <div class="max-w-global mx-auto px-4 sm:px-6">
        <!-- Section Header -->
        <div class="mb-8">
          <h2 class="border-t-2 border-primary pt-2 text-xl font-normal text-heading mb-2">
            Provider Hosting dengan Promo Aktif
          </h2>
          <p class="text-muted">
            Temukan {promoProviders.length} provider hosting yang sedang memberikan promo dan diskon menarik untuk Anda.
          </p>
        </div>

        <!-- Promo Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {promoProviders.map((provider) => (
            <div class="bg-bg-section from-primary/5 to-primary/10 border-2 border-primary/20 rounded-xl p-6 transition-all duration-300 hover:bg-bg-muted relative overflow-hidden">

              <!-- Provider Logo & Name -->
              <div class="flex items-center mb-4">
                <img
                  src={provider.data.logo}
                  alt={`${provider.data.name} logo`}
                  class="h-12 w-16 rounded-lg object-contain border border-gray-200 bg-white p-1 flex-shrink-0 mr-3"
                  loading="lazy"
                />
                <div>
                  <h3 class="font-bold text-lg text-heading">
                    {provider.data.displayName || provider.data.name}
                  </h3>
                  <!-- Provider Badges -->
                  <div class="flex items-center gap-1 mt-1">
                    {provider.data.badges && provider.data.badges.map((badge) => {
                      if (badge.type === 'verified') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200">
                            ✓ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'sponsored') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200">
                            👑 {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'recommended') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-green-100 text-green-700 border border-green-200">
                            ⭐ {badge.label}
                          </span>
                        );
                      }
                      if (badge.type === 'promoted') {
                        return (
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200">
                            🎯 {badge.label}
                          </span>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              </div>

              <!-- Price & Promo Info -->
              <div class="mb-4">
                <div class="flex items-baseline gap-2 mb-2">
                  <span class="text-2xl font-bold text-primary">
                    <span class="text-sm font-normal">Mulai</span>
                    Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')}
                  </span>
                  <span class="text-sm text-muted">/bulan</span>
                </div>
                
                <!-- Promo Details -->
                {provider.data.pricing.promoDescription && (
                  <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                    <div class="text-red-700 font-semibold text-sm mb-1">
                      🎉 {provider.data.pricing.promoDescription}
                    </div>
                    {provider.data.pricing.promoCode && (
                      <div class="text-xs text-red-600">
                        Kode: <span class="font-mono bg-red-100 px-2 py-1 rounded">{provider.data.pricing.promoCode}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>


              <!-- Description -->
              <p class="text-muted text-sm line-clamp-3 mb-4">
                {provider.data.description}
              </p>

              <!-- Key Features -->
              <div class="mb-6">
                <div class="grid grid-cols-1 gap-2">
                  {provider.data.features.slice(0, 3).map((feature) => (
                    <div class="flex items-center text-xs">
                      <svg class="h-3 w-3 text-green-500 flex-shrink-0 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span class="text-muted">{feature.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="space-y-2">
                <Button
                  href={provider.data.affiliateLink || provider.data.website}
                  target="_blank"
                  rel="noopener"
                  variant="primary"
                  class="w-full text-sm font-semibold py-3 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                  icon="tabler:external-link"
                >
                  Ambil Promo
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  ) : null}

  <!-- Show empty state only if there are no promo providers at all (sponsored, promoted, or regular) -->
  {(sponsoredPromoProviders.length === 0 && promotedPromoProviders.length === 0 && promoProviders.length === 0) && (
    <section class="py-12">
      <div class="max-w-global mx-auto px-4 sm:px-6 text-center">
        <div class="border-t-2 border-primary pt-2 mb-4">
          <h2 class="text-2xl font-bold text-heading mb-4">
            Belum Ada Promo Aktif
          </h2>
        </div>
        <p class="text-muted mb-6">
          Saat ini belum ada provider hosting yang memiliki promo aktif. Silakan cek kembali nanti atau submit promo Anda.
        </p>
        <Button
          href="/direktori-hosting/"
          variant="primary"
          class="px-6 py-3"
          icon="tabler:arrow-left"
        >
          Lihat Semua Provider Hosting
        </Button>
      </div>
    </section>
  )}

  <!-- Back to Top -->
  <BackToTop />
</Layout>