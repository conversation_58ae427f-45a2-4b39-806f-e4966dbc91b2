---
import { getCollection, render } from 'astro:content';
import WikiHostingLayout from '~/layouts/WikiHostingLayout.astro';

export async function getStaticPaths() {
  const wikiHostingEntries = await getCollection('wikihosting');
  
  // Filter out draft entries in production
  const publishedEntries = wikiHostingEntries.filter(entry => {
    return import.meta.env.DEV || !entry.data.draft;
  });
  
  return publishedEntries.map(entry => ({
    params: { slug: entry.id },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content, headings } = await render(entry);
---

<WikiHostingLayout entry={entry} headings={headings}>
  <Content />
</WikiHostingLayout>