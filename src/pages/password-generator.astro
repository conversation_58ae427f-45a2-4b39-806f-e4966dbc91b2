---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolPlaceholder from '~/components/alat/shared/ToolPlaceholder.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import PasswordGeneratorForm from '~/components/alat/password-generator/PasswordGeneratorForm.astro';
import ResultsSection from '~/components/alat/password-generator/ResultsSection.astro';

const metadata = {
  title: 'Password Generator - Buat Password Kuat & Acak | Penasihat Hosting',
  description: 'Buat password kuat dan acak dengan mudah menggunakan tool Password Generator dari Penasihat Hosting.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/password-generator.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Mengapa Password Kuat Penting?",
    content: [
      "Di era digital saat ini, password adalah garis pertahanan pertama Anda terhadap akses tidak sah ke akun online, data pribadi, dan informasi sensitif. Menggunakan password yang lemah atau menggunakan password yang sama di banyak situs dapat membuat Anda rentan terhadap peretasan, pencurian identitas, dan kerugian finansial.",
      "Penjahat siber menggunakan berbagai metode untuk mencuri password, termasuk serangan brute-force (mencoba semua kombinasi yang mungkin) dan serangan kamus (menggunakan daftar kata umum). Password yang kuat dan unik secara signifikan memperlambat atau menggagalkan upaya ini."
    ]
  },
  {
    title: "Ciri-ciri Password yang Kuat",
    content: [
      "Password yang kuat adalah kombinasi dari beberapa faktor yang membuatnya sulit ditebak. Ciri-ciri utamanya meliputi:",
      "<ul class='list-disc list-inside'><li><strong>Panjang:</strong> Semakin panjang password, semakin sulit ditebak. Disarankan minimal 12-16 karakter.</li><li><strong>Kompleksitas:</strong> Mengandung campuran huruf besar dan kecil, angka, dan simbol.</li><li><strong>Keunikan:</strong> Gunakan password yang berbeda untuk setiap akun penting. Jika satu akun diretas, akun lainnya tetap aman.</li><li><strong>Keacakan:</strong> Jangan menggunakan informasi pribadi, kata-kata umum, urutan keyboard, atau pola yang mudah ditebak. Password yang dihasilkan secara acak adalah yang paling aman.</li></ul>"
    ]
  },
  {
    title: "Tips Mengelola Password",
    content: [
      "Menggunakan password kuat untuk setiap akun bisa jadi sulit diingat. Berikut beberapa tips untuk mengelolanya:",
      "<ul class='list-disc list-inside'><li><strong>Gunakan Pengelola Password:</strong> Aplikasi pengelola password dapat membuat, menyimpan, dan mengisi password unik dengan aman untuk semua situs web Anda. Anda hanya perlu mengingat satu password utama.</li><li><strong>Aktifkan Otentikasi Dua Faktor (2FA):</strong> Jika tersedia, selalu aktifkan 2FA. Ini menambahkan lapisan keamanan ekstra, biasanya memerlukan kode dari ponsel Anda selain password.</li><li><strong>Perbarui Password Secara Berkala:</strong> Meskipun kurang ditekankan saat menggunakan password unik dan kuat, mempertimbangkan untuk memperbarui password akun yang sangat sensitif secara berkala tetap merupakan praktik yang baik.</li></ul>"
    ]
  },
  {
    title: "Kesalahan Password yang Harus Dihindari",
    content: [
      "Beberapa kesalahan umum dapat membahayakan keamanan Anda. Hindari:",
      "<ul class='list-disc list-inside'><li>Menggunakan password yang sama untuk beberapa akun.</li><li>Menggunakan informasi pribadi (nama, tanggal lahir, nama hewan peliharaan).</li><li>Menggunakan kata-kata kamus atau frasa umum.</li><li>Menulis password di tempat yang mudah ditemukan.</li><li>Membagikan password dengan orang lain.</li><li>Mengklik tautan mencurigakan yang meminta Anda memasukkan password.</li></ul>",
      "Dengan menghindari kesalahan ini dan menggunakan password kuat yang unik, Anda dapat secara signifikan meningkatkan keamanan online Anda."
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="Password Generator">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="Password Generator"
    description="Buat password yang kuat dan unik untuk setiap akun online Anda. Pilih panjang dan jenis karakter yang diinginkan untuk keamanan maksimal."
    subtitle="Tool gratis untuk membuat password yang kuat dan acak."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <ToolInterface title="Password Generator">
        <PasswordGeneratorForm />
      </ToolInterface>
      
      <!-- Results Section -->
      <ResultsSection />
      
      <!-- Placeholder Content -->
      <ToolPlaceholder 
        icon="tabler:lock-question"
        title="Siap Membuat Password?"
        description="Sesuaikan pengaturan di atas dan klik 'Generate Password' untuk mendapatkan password yang kuat."
        id="passwordPlaceholderContent"
      />
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  #passwordResultsContainer {
    animation: fadeIn 0.3s ease-out forwards;
  }
</style>

<!-- Password Generator Logic -->
<script is:inline>
  class PasswordGenerator {
    constructor() {
      this.form = document.getElementById('passwordGeneratorForm');
      this.lengthInput = document.getElementById('passwordLength');
      this.includeUppercase = document.getElementById('includeUppercase');
      this.includeLowercase = document.getElementById('includeLowercase');
      this.includeNumbers = document.getElementById('includeNumbers');
      this.includeSymbols = document.getElementById('includeSymbols');
      this.excludeSimilar = document.getElementById('excludeSimilar');
      this.excludeAmbiguous = document.getElementById('excludeAmbiguous');
      this.passwordOutput = document.getElementById('generatedPassword');
      this.copyButton = document.getElementById('copyPasswordBtn');
      this.regenerateButton = document.getElementById('regeneratePasswordBtn');
      this.resultsContainer = document.getElementById('passwordResultsContainer');
      this.placeholderContent = document.getElementById('passwordPlaceholderContent');
      this.strengthIndicator = document.getElementById('passwordStrength');
      this.strengthText = document.getElementById('strengthText');
    }

    init() {
      // Set up form submission
      if (this.form) {
        this.form.addEventListener('submit', (e) => {
          e.preventDefault();
          this.generatePassword();
        });
      }

      // Set up copy button
      if (this.copyButton) {
        this.copyButton.addEventListener('click', () => this.copyPassword());
      }

      // Set up regenerate button
      if (this.regenerateButton) {
        this.regenerateButton.addEventListener('click', () => this.generatePassword());
      }

      // Set up length input for real-time generation
      if (this.lengthInput) {
        this.lengthInput.addEventListener('input', () => {
          if (this.passwordOutput.value) {
            this.generatePassword();
          }
        });
      }

      // Set up checkbox changes for real-time generation
      [this.includeUppercase, this.includeLowercase, this.includeNumbers, this.includeSymbols, this.excludeSimilar, this.excludeAmbiguous].forEach(checkbox => {
        if (checkbox) {
          checkbox.addEventListener('change', () => {
            if (this.passwordOutput.value) {
              this.generatePassword();
            }
          });
        }
      });

      // Generate initial password
      this.generatePassword();
    }

    generatePassword() {
      const length = parseInt(this.lengthInput?.value) || 16;
      const hasUppercase = this.includeUppercase?.checked || false;
      const hasLowercase = this.includeLowercase?.checked || false;
      const hasNumbers = this.includeNumbers?.checked || false;
      const hasSymbols = this.includeSymbols?.checked || false;
      const excludeSimilar = this.excludeSimilar?.checked || false;
      const excludeAmbiguous = this.excludeAmbiguous?.checked || false;

      // Validate that at least one character type is selected
      if (!hasUppercase && !hasLowercase && !hasNumbers && !hasSymbols) {
        this.showError('Pilih setidaknya satu jenis karakter');
        return;
      }

      // Generate character set
      let charset = '';
      if (hasUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      if (hasLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
      if (hasNumbers) charset += '0123456789';
      if (hasSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

      // Apply exclusions
      if (excludeSimilar) {
        charset = charset.replace(/[il1Lo0O]/g, '');
      }
      if (excludeAmbiguous) {
        charset = charset.replace(/[{}[\]()/\\\'"`~,;:.<>]/g, '');
      }

      // Generate password
      let password = '';
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
      }

      // Ensure password meets requirements
      password = this.ensureRequirements(password, hasUppercase, hasLowercase, hasNumbers, hasSymbols);

      // Update UI
      this.passwordOutput.value = password;
      this.updateStrengthIndicator(password);
      this.showResults();
    }

    ensureRequirements(password, hasUppercase, hasLowercase, hasNumbers, hasSymbols) {
      let result = password;
      
      // Ensure at least one character of each selected type
      if (hasUppercase && !/[A-Z]/.test(result)) {
        result = this.replaceRandomChar(result, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
      }
      if (hasLowercase && !/[a-z]/.test(result)) {
        result = this.replaceRandomChar(result, 'abcdefghijklmnopqrstuvwxyz');
      }
      if (hasNumbers && !/[0-9]/.test(result)) {
        result = this.replaceRandomChar(result, '0123456789');
      }
      if (hasSymbols && !/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(result)) {
        result = this.replaceRandomChar(result, '!@#$%^&*()_+-=[]{}|;:,.<>?');
      }

      return result;
    }

    replaceRandomChar(password, charset) {
      const randomIndex = Math.floor(Math.random() * password.length);
      const randomChar = charset[Math.floor(Math.random() * charset.length)];
      return password.substring(0, randomIndex) + randomChar + password.substring(randomIndex + 1);
    }

    updateStrengthIndicator(password) {
      let strength = 0;
      let feedback = '';

      // Length check
      if (password.length >= 12) strength += 2;
      else if (password.length >= 8) strength += 1;

      // Character variety checks
      if (/[A-Z]/.test(password)) strength += 1;
      if (/[a-z]/.test(password)) strength += 1;
      if (/[0-9]/.test(password)) strength += 1;
      if (/[^A-Za-z0-9]/.test(password)) strength += 1;

      // Determine strength level
      let strengthLevel = '';
      let strengthColor = '';

      if (strength >= 5) {
        strengthLevel = 'Sangat Kuat';
        strengthColor = 'bg-green-500';
        feedback = 'Password Anda sangat aman!';
      } else if (strength >= 4) {
        strengthLevel = 'Kuat';
        strengthColor = 'bg-blue-500';
        feedback = 'Password Anda cukup aman.';
      } else if (strength >= 3) {
        strengthLevel = 'Sedang';
        strengthColor = 'bg-yellow-500';
        feedback = 'Password Anda cukup baik, tapi bisa ditingkatkan.';
      } else {
        strengthLevel = 'Lemah';
        strengthColor = 'bg-red-500';
        feedback = 'Password Anda terlalu lemah. Tingkatkan kompleksitasnya.';
      }

      // Update UI
      if (this.strengthIndicator) {
        this.strengthIndicator.className = `w-3 h-3 rounded-full ${strengthColor}`;
      }
      if (this.strengthText) {
        this.strengthText.textContent = `${strengthLevel} - ${feedback}`;
      }
    }

    async copyPassword() {
      const password = this.passwordOutput?.value;
      if (!password) return;

      try {
        await navigator.clipboard.writeText(password);
        this.showCopyFeedback('Password berhasil disalin!');
      } catch (error) {
        // Fallback for older browsers
        this.passwordOutput?.select();
        document.execCommand('copy');
        this.showCopyFeedback('Password berhasil disalin!');
      }
    }

    showResults() {
      if (this.resultsContainer && this.placeholderContent) {
        this.resultsContainer.classList.remove('hidden');
        this.placeholderContent.classList.add('hidden');
      }
    }

    showError(message) {
      // Show error in the password output field
      if (this.passwordOutput) {
        this.passwordOutput.value = '';
        this.passwordOutput.placeholder = message;
        this.passwordOutput.classList.add('border-red-500');
        setTimeout(() => {
          this.passwordOutput.classList.remove('border-red-500');
          this.passwordOutput.placeholder = 'Password akan muncul di sini...';
        }, 3000);
      }
      console.error(message);
    }

    showCopyFeedback(message) {
      const feedbackElement = document.getElementById('copyFeedbackMessage');
      if (feedbackElement) {
        feedbackElement.textContent = message;
        feedbackElement.classList.remove('hidden');
        setTimeout(() => {
          feedbackElement.classList.add('hidden');
        }, 2000);
      }
    }
  }

  // Initialize generator when DOM is ready
  function initializePasswordGenerator() {
    try {
      const generator = new PasswordGenerator();
      generator.init();
      console.log('Password Generator initialized successfully');
    } catch (error) {
      console.error('Error initializing Password Generator:', error);
    }
  }

  // Try multiple initialization methods
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePasswordGenerator);
  } else {
    initializePasswordGenerator();
  }

  // Also try Astro page load event
  document.addEventListener('astro:page-load', initializePasswordGenerator);
</script> 