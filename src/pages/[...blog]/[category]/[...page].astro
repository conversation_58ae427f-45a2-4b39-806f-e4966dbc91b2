---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';
import { blogCategoryRobots, getStaticPathsBlogCategory, fetchPosts } from '~/utils/blog';

import Layout from '~/layouts/PageLayout.astro';
import BlogGrid from '~/components/blog/BlogGrid.astro';
import BlogFilter from '~/components/blog/BlogFilter.astro';
import Pagination from '~/components/blog/Pagination.astro';
import { Icon } from 'astro-icon/components';

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogCategory({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths> & { category: Record<string, string> };

const { page, category } = Astro.props as Props;

const currentPage = page.currentPage ?? 1;

// Get all posts to calculate filter counts
const allPosts = await fetchPosts();

// Determine the appropriate icon and layout based on category
const getCategoryIcon = (categorySlug: string) => {
  switch (categorySlug) {
    case 'review-hosting':
      return 'tabler:star';
    case 'panduan':
      return 'tabler:book';
    case 'tutorial':
      return 'tabler:school';
    default:
      return 'tabler:category';
  }
};

// All categories use grid layout for now
const categoryLayout = 'grid';

const getCategoryCardVariant = (categorySlug: string) => {
  switch (categorySlug) {
    case 'review-hosting':
      return 'review';
    case 'panduan':
      return 'guide-page';
    default:
      return 'regular';
  }
};

// Count posts by type for filter badges
const guidePageCount = allPosts.filter(post =>
  post.metadata?.layout === 'guide-page' ||
  post.metadata?.isGuidePage === true ||
  post.category?.slug === 'panduan' ||
  post.category?.title === 'Panduan'
).length;

const reviewCount = allPosts.filter(post =>
  post.category?.slug === 'review-hosting'
).length;

const tutorialCount = allPosts.filter(post =>
  post.category?.slug === 'tutorial' ||
  post.tags?.some(tag => tag.slug === 'tutorial')
).length;

// Create post type filters with counts
const postTypes = [
  { slug: 'all', title: 'Semua', icon: 'tabler:article', count: allPosts.length },
  { slug: 'guide-page', title: 'Panduan', icon: 'tabler:book', count: guidePageCount },
  { slug: 'review-hosting', title: 'Review', icon: 'tabler:star', count: reviewCount },
  { slug: 'tutorial', title: 'Tutorial', icon: 'tabler:school', count: tutorialCount },
];

const categoryIcon = getCategoryIcon(category.slug);
// Layout is always grid for now
// const categoryLayout is defined above
const categoryCardVariant = getCategoryCardVariant(category.slug);

const metadata = {
  title: `${category.title}${currentPage > 1 ? ` — Halaman ${currentPage}` : ''}`,
  description: `Artikel dalam kategori ${category.title} - Penasihat Hosting`,
  canonical: currentPage > 1 ? `https://penasihathosting.com/category/${category.slug}/${currentPage}/` : `https://penasihathosting.com/category/${category.slug}/`,
  robots: {
    index: blogCategoryRobots?.index && currentPage === 1,
    follow: blogCategoryRobots?.follow,
  },
};
---

<Layout metadata={metadata}>
  <section class="px-4 md:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-global">
    <div class="mb-8">
      <div class="flex items-center justify-center mb-4">
        <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full">
          <Icon name={categoryIcon} class="w-8 h-8 text-primary dark:text-primary-dark" />
        </div>
      </div>
      <h1 class="text-4xl md:text-5xl font-bold text-center mb-4 dark:text-white">{category.title}</h1>
      <p class="text-lg text-center text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
        Semua artikel dalam kategori {category.title}
      </p>
    </div>

    {/* Full-width Blog Filter */}
    <div class="mb-8">
      <BlogFilter postTypes={postTypes} activeItem={category.slug === 'panduan' ? 'guide-page' : category.slug} />
    </div>

    <div class="grid md:grid-cols-3 gap-8 md:gap-12">
      <div class="md:col-span-3">
        <BlogGrid
          posts={page.data}
          layout={categoryLayout}
          postCardVariant={categoryCardVariant}
        />

        <div class="mt-12">
          <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
        </div>
      </div>
    </div>
  </section>
</Layout>
