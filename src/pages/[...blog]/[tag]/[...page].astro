---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';
import { getStaticPathsBlogTag } from '~/utils/blog';

import Layout from '~/layouts/PageLayout.astro';
import BlogGrid from '~/components/blog/BlogGrid.astro';
import BlogFilter from '~/components/blog/BlogFilter.astro';
import Pagination from '~/components/blog/Pagination.astro';
import { Icon } from 'astro-icon/components';

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogTag({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page, tag } = Astro.props as Props;

const currentPage = page.currentPage ?? 1;

// Determine the appropriate icon based on tag
const getTagIcon = (tagSlug: string) => {
  switch (tagSlug) {
    case 'featured':
      return 'tabler:star';
    case 'panduan':
    case 'guide':
      return 'tabler:book';
    case 'tutorial':
      return 'tabler:school';
    case 'pemula':
      return 'tabler:user';
    default:
      return 'tabler:tag';
  }
};

const tagIcon = getTagIcon(tag.slug);

const metadata = {
  title: `Tag: ${tag.title}${currentPage > 1 ? ` — Halaman ${currentPage}` : ''}`,
  description: `Artikel dengan tag ${tag.title} - Penasihat Hosting`,
  canonical: `https://penasihathosting.com/tag/${tag.slug}/`,
  robots: {
    index: false,
    follow: false,
  },
};
---

<Layout metadata={metadata}>
  <section class="px-4 md:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-global">
    <div class="mb-8">
      <div class="flex items-center justify-center mb-4">
        <div class="bg-gray-100 dark:bg-gray-800 p-3 rounded-full">
          <Icon name={tagIcon} class="w-8 h-8 text-gray-700 dark:text-gray-300" />
        </div>
      </div>
      <h1 class="text-4xl md:text-5xl font-bold text-center mb-4 dark:text-white">Tag: {tag.title}</h1>
      <p class="text-lg text-center text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
        Semua artikel dengan tag {tag.title}
      </p>
    </div>

    <div>
      <div class="md:col-span-1">
        <div class="sticky top-24">
          <BlogFilter activeItem={tag.slug} isTag={true} />
        </div>
      </div>

      <div class="md:col-span-3">
        <BlogGrid
          posts={page.data}
          layout="grid"
        />

        <div class="mt-12">
          <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
        </div>
      </div>
    </div>
  </section>
</Layout>
