import type { APIRoute } from 'astro';
import { generateSearchIndex } from '~/utils/search-index';

export const GET: APIRoute = async () => {
  try {
    const searchIndex = await generateSearchIndex();
    
    // Add version timestamp to prevent caching during development
    const timestamp = new Date().getTime();
    const responseData = {
      ...searchIndex,
      _timestamp: timestamp
    };
    
    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate', // Prevent caching
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error generating search index:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to generate search index',
      providers: [],
      categories: [],
      pages: [],
      lastUpdated: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
