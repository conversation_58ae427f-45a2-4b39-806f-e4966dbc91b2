---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import HashGeneratorForm from '~/components/alat/hash-generator/HashGeneratorForm.astro';

const metadata = {
  title: 'Hash Generator - Buat Hash MD5, SHA-1, SHA-256, SHA-512 Online | Penasihat Hosting',
  description: 'Buat hash MD5, SHA-1, SHA-256, SHA-512 dari teks dengan cepat menggunakan tool Hash Generator online.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/hash-generator.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Apa Itu Fungsi Hash?",
    content: [
      "Fungsi hash adalah algoritma matematika yang mengubah data input (sering disebut 'pesan' atau 'input') dengan ukuran berapa pun menjadi string byte dengan ukuran tetap, yang dikenal sebagai 'nilai hash' atau 'digest'. Proses ini bersifat 'satu arah', artinya sangat sulit (atau praktis tidak mungkin) untuk merekayasa balik nilai hash untuk mendapatkan data input aslinya.",
      "Fungsi hash yang baik memiliki beberapa properti penting, termasuk deterministik (input yang sama selalu menghasilkan output hash yang sama), komputasi cepat, dan tahan terhadap tumbukan (collision resistance), di mana sangat sulit menemukan dua input berbeda yang menghasilkan nilai hash yang sama."
    ]
  },
  {
    title: "Kegunaan Hashing",
    content: [
      "Hashing memiliki banyak aplikasi di dunia komputasi dan keamanan, termasuk:",
      "<ul class='list-disc list-inside'><li><strong>Verifikasi Integritas Data:</strong> Membandingkan nilai hash dari sebuah file atau pesan sebelum dan sesudah transmisi untuk memastikan data tidak berubah.</li><li><strong>Penyimpanan Password:</strong> Website tidak menyimpan password pengguna dalam bentuk teks biasa, melainkan menyimpan nilai hash-nya. Saat login, password yang dimasukkan pengguna di-hash dan dibandingkan dengan hash yang tersimpan.</li><li><strong>Struktur Data (Hash Tables):</strong> Digunakan dalam tabel hash untuk pencarian data yang cepat.</li><li><strong>Kriptografi (Digital Signatures, Blockchains):</strong> Memainkan peran fundamental dalam tanda tangan digital dan teknologi blockchain.</li></ul>"
    ]
  },
  {
    title: "Tentang Algoritma SHA",
    content: [
      "SHA (Secure Hash Algorithm) adalah keluarga fungsi hash kriptografis yang dikembangkan oleh NIST (National Institute of Standards and Technology) AS. Beberapa varian SHA yang umum digunakan adalah:",
      "<ul class='list-disc list-inside'><li><strong>SHA-1:</strong> Meskipun masih banyak digunakan, SHA-1 secara kriptografis dianggap tidak aman karena kerentanannya terhadap serangan tumbukan dan tidak lagi direkomendasikan untuk aplikasi yang membutuhkan keamanan tinggi.</li><li><strong>SHA-256, SHA-512:</strong> Bagian dari keluarga SHA-2, algoritma ini dianggap lebih aman dibandingkan SHA-1 dan banyak digunakan dalam sertifikat SSL/TLS, blockchain (misalnya Bitcoin menggunakan SHA-256), dan aplikasi keamanan lainnya.</li><li><strong>MD5:</strong> (Message Digest 5) adalah fungsi hash yang lebih tua. Meskipun masih digunakan untuk verifikasi integritas file dalam beberapa kasus, MD5 memiliki kerentanan tumbukan yang signifikan dan tidak cocok untuk aplikasi keamanan seperti penyimpanan password atau tanda tangan digital.</li></ul>"
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="Hash Generator">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="Hash Generator"
    description="Hasilkan hash SHA-1, SHA-256, atau SHA-512 dari input teks Anda. Berguna untuk verifikasi integritas data."
    subtitle="Tool gratis untuk membuat hash dari teks menggunakan berbagai algoritma."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <ToolInterface title="Hash Generator">
        <HashGeneratorForm />
      </ToolInterface>
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  /* Add styling consistent with other tools if needed */
</style>

<!-- Hash Generator Logic -->
<script is:inline>
  class HashGenerator {
    constructor() {
      this.textInput = document.getElementById('textInput');
      this.algorithmSelect = document.getElementById('algorithmSelect');
      this.hashOutput = document.getElementById('hashOutput');
      this.copyButton = document.getElementById('copyHashBtn');
      this.clearButton = document.getElementById('clearButton');
      this.textInputErrorMessage = document.getElementById('textInputErrorMessage');
      this.copyFeedbackMessageElement = document.getElementById('copyFeedbackMessage');
    }

    init() {
      // Event listener for form submission (manual hashing)
      const form = document.getElementById('hashGeneratorForm');
      if (form) {
        form.addEventListener('submit', (e) => {
          e.preventDefault();
          this.generateHash();
        });
      }
      
      // Event listener for algorithm changes (only if text exists)
      if (this.algorithmSelect) {
        this.algorithmSelect.addEventListener('change', () => {
          if (this.textInput?.value.trim()) {
            this.generateHash();
          }
        });
      }
      
      // Event listener for copy button
      if (this.copyButton) {
        this.copyButton.addEventListener('click', () => this.copyHash());
      }
      
      // Event listener for clear button
      if (this.clearButton) {
        this.clearButton.addEventListener('click', () => this.clearAll());
      }
    }

    async generateHash() {
      const text = this.textInput?.value || '';
      const algorithm = this.algorithmSelect?.value || 'SHA-256';
      
      if (!text.trim()) {
        this.showError('Masukkan teks terlebih dahulu');
        this.hashOutput.value = '';
        return;
      }

      try {
        // Show loading state
        this.hashOutput.value = 'Generating hash...';
        this.hideError();
        
        // Use Web Crypto API for hashing
        const encoder = new TextEncoder();
        const data = encoder.encode(text);
        
        let hashBuffer;
        
        switch (algorithm) {
          case 'MD5':
            // Note: Web Crypto API doesn't support MD5, so we'll use a simple implementation
            // In production, you might want to use a proper MD5 library
            this.hashOutput.value = 'MD5 not supported in Web Crypto API';
            return;
          case 'SHA-1':
            hashBuffer = await crypto.subtle.digest('SHA-1', data);
            break;
          case 'SHA-256':
            hashBuffer = await crypto.subtle.digest('SHA-256', data);
            break;
          case 'SHA-512':
            hashBuffer = await crypto.subtle.digest('SHA-512', data);
            break;
          default:
            hashBuffer = await crypto.subtle.digest('SHA-256', data);
        }
        
        // Convert buffer to hex string
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        
        this.hashOutput.value = hashHex;
        this.hideError();
        
        // Show success feedback
        this.showSuccessFeedback(`Hash ${algorithm} berhasil di-generate!`);
      } catch (error) {
        console.error('Error generating hash:', error);
        this.showError('Error generating hash. Please try again.');
        this.hashOutput.value = '';
      }
    }

    async copyHash() {
      const hash = this.hashOutput?.value;
      if (!hash) return;

      try {
        await navigator.clipboard.writeText(hash);
        this.showCopyFeedback('Hash berhasil disalin!');
      } catch {
        // Fallback for older browsers
        this.hashOutput?.select();
        document.execCommand('copy');
        this.showCopyFeedback('Hash berhasil disalin!');
      }
    }

    clearAll() {
      this.textInput.value = '';
      this.hashOutput.value = '';
      this.hideError();
      this.hideCopyFeedback();
    }

    showError(message) {
      if (this.textInputErrorMessage) {
        this.textInputErrorMessage.textContent = message;
        this.textInputErrorMessage.classList.remove('hidden');
      }
    }

    hideError() {
      this.textInputErrorMessage?.classList.add('hidden');
    }

    showCopyFeedback(message) {
      if (this.copyFeedbackMessageElement) {
        this.copyFeedbackMessageElement.textContent = message;
        this.copyFeedbackMessageElement.classList.remove('hidden');
        setTimeout(() => this.hideCopyFeedback(), 2000);
      }
    }

    hideCopyFeedback() {
      this.copyFeedbackMessageElement?.classList.add('hidden');
    }

    showSuccessFeedback(message) {
      // You can implement success feedback display logic here
      console.log(message);
    }
  }

  // Initialize generator when DOM is ready
  function initializeHashGenerator() {
    try {
      const generator = new HashGenerator();
      generator.init();
      console.log('Hash Generator initialized successfully');
    } catch (error) {
      console.error('Error initializing Hash Generator:', error);
    }
  }

  // Try multiple initialization methods
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeHashGenerator);
  } else {
    initializeHashGenerator();
  }

  // Also try Astro page load event
  document.addEventListener('astro:page-load', initializeHashGenerator);
</script> 