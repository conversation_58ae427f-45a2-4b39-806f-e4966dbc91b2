---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import BackToTop from '~/components/common/BackToTop.astro';
import KamusNavigation from '~/components/blog/KamusNavigation.astro';
import KamusSearch from '~/components/blog/KamusSearch.astro';
import { getCollection, render } from 'astro:content';
import { getFormattedDate } from '~/utils/utils';

// Get the kamus content
const kamusEntries = await getCollection('kamus');
const kamusEntry = kamusEntries.find(entry => entry.id === 'hosting-dictionary');

if (!kamusEntry) {
  throw new Error('Kamus hosting dictionary not found');
}

const { Content } = await render(kamusEntry);

// Extract terms data
const alphabetSections = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 
  'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 
  'U', 'V', 'W', 'X', 'Y', 'Z'
];

const termCounts = {
  'A': 10, 'B': 10, 'C': 8, 'D': 10, 'E': 10, 'F': 10, 'G': 10, 'H': 10,
  'I': 10, 'J': 6, 'K': 8, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 10,
  'Q': 5, 'R': 10, 'S': 11, 'T': 10, 'U': 10, 'V': 10, 'W': 10, 'X': 5, 'Y': 4, 'Z': 2
};

const metadata = {
  title: kamusEntry.data.title,
  description: kamusEntry.data.description,
  canonical: 'https://penasihathosting.com/kamus-hosting/',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    url: 'https://penasihathosting.com/kamus-hosting/',
    siteName: 'PenasihatHosting',
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/July/kamus-hosting-indonesia.webp',
        width: 1200,
        height: 628,
      },
    ],
    type: 'article',
  },
};
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Breadcrumbs -->
  <section class="bg-bg-muted py-2 border-b border-gray-200">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm">
          <li>
            <a href="/" class="text-muted hover:text-primary transition-colors">
              Home
            </a>
          </li>
          <li class="text-gray-400">/</li>
          <li>
            <span class="text-heading font-medium">Kamus Hosting</span>
          </li>
        </ol>
      </nav>
    </div>
  </section>

  <!-- Hero Section -->
  <section class="bg-bg-page dark:bg-bg-section pt-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="max-w-full lg:max-w-[65ch] border-t-8 border-primary pt-2">
        <h1 class="text-4xl md:text-5xl font-extrabold text-heading mb-4">
          <span class="italic text-primary">Kamus</span> Hosting Indonesia
        </h1>
        <p class="text-lg text-muted mb-6 leading-relaxed">
          {kamusEntry.data.description}
        </p>
        
        <!-- Meta Info -->
        <div class="flex flex-wrap gap-4 text-sm text-muted mb-6">
          {kamusEntry.data.author && (
            <span class="flex items-center gap-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              {kamusEntry.data.author}
            </span>
          )}
          <span class="flex items-center gap-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            {getFormattedDate(new Date(kamusEntry.data.publishedAt))}
          </span>
          {kamusEntry.data.updateDate && (
            <span class="flex items-center gap-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Diperbarui {getFormattedDate(new Date(kamusEntry.data.updateDate))}
            </span>
          )}
          <span class="flex items-center gap-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            200+ Istilah
          </span>
        </div>
      </div>
    </div>
  </section>


  <!-- Search Section -->
  <section class="py-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="max-w-4xl">
        <KamusSearch />
      </div>
    </div>
  </section>

  <!-- Main Content Section -->
  <section class="pb-12">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="lg:flex lg:gap-8 max-w-6xl">
        
        <!-- Sticky Navigation -->
        <aside class="lg:w-64 lg:sticky lg:top-24 lg:self-start mb-8 lg:mb-0">
          <KamusNavigation 
            sections={alphabetSections} 
            termCounts={termCounts}
          />
        </aside>

        <!-- Dictionary Content -->
        <main class="lg:flex-1 min-w-0">
          <div class="prose prose-md dark:prose-invert max-w-none" id="kamus-content">
            <Content />
          </div>
        </main>
        
      </div>
    </div>
  </section>

  <!-- BackToTop Component -->
  <BackToTop />
</Layout>

<style>
  /* Smooth scrolling for the whole page */
  html {
    scroll-behavior: smooth;
  }
  
  /* Enhanced prose styling for dictionary content */
  .prose h2 {
    @apply scroll-mt-24 border-l-4 border-primary pl-4 bg-bg-section py-2 rounded-r-md mb-6;
  }
  
  .prose h3 {
    @apply scroll-mt-24 text-primary font-semibold;
  }
  
  /* Dictionary entry styling */
  .prose .dictionary-entry {
    @apply mb-6 p-4 bg-bg-section rounded-md border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  .prose .dictionary-term {
    @apply font-bold text-lg text-primary mb-2 block;
  }
  
  .prose .dictionary-definition {
    @apply text-muted leading-relaxed;
  }
</style>

<script>
  // Smooth scrolling for navigation links
  function setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const href = link.getAttribute('href');
        if (href) {
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({ 
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      });
    });
  }
  
  // Initialize on DOM loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupSmoothScrolling();
  });
</script>
