---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolPlaceholder from '~/components/alat/shared/ToolPlaceholder.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import TabNavigation from '~/components/alat/kalkulator-uptime/TabNavigation.astro';
import UptimeCalculatorForm from '~/components/alat/kalkulator-uptime/UptimeCalculatorForm.astro';
import DowntimeCalculatorForm from '~/components/alat/kalkulator-uptime/DowntimeCalculatorForm.astro';
import ResultsSection from '~/components/alat/kalkulator-uptime/ResultsSection.astro';

const metadata = {
  title: 'Kalkulator Uptime & Downtime - Hitung Waktu Online/Offline Website | Penasihat Hosting',
  description: 'Kalkulator uptime dan downtime untuk menghitung waktu online/offline website berdasarkan persentase. Ketahui berapa lama website akan offline atau online dalam setahun, sebulan, seminggu, atau sehari.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/kalkulator-uptime.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Apa Itu Uptime dan Mengapa Penting?",
    content: [
      "Uptime adalah metrik yang mengukur total waktu layanan (seperti website atau server hosting) tersedia dan beroperasi dalam periode waktu tertentu. Biasanya dinyatakan dalam persentase. Angka uptime yang tinggi menunjukkan bahwa layanan tersebut jarang mengalami gangguan atau downtime.",
      "Mengapa uptime penting? Bagi sebagian besar website, terutama yang digunakan untuk bisnis, e-commerce, atau layanan online, ketersediaan yang tinggi adalah krusial. Setiap menit downtime dapat berarti kerugian pendapatan, pengalaman pengguna yang buruk, rusaknya reputasi merek, dan bahkan dampak negatif pada peringkat mesin pencari karena Google dan mesin pencari lainnya mempertimbangkan ketersediaan situs.",
      "Kalkulator ini membantu Anda memvisualisasikan dampak dari persentase uptime tertentu menjadi total waktu downtime yang bisa terjadi dalam setahun, sebulan, seminggu, atau sehari."
    ]
  },
  {
    title: "Memahami Angka Uptime dalam Realitas",
    content: [
      "Melihat angka persentase uptime seperti 99.9% mungkin terlihat sempurna, namun penting untuk mengetahui berapa banyak waktu downtime yang sebenarnya direpresentasikan oleh angka tersebut dalam periode waktu yang lebih mudah dipahami. Berikut adalah perkiraan downtime untuk persentase uptime umum berdasarkan perhitungan standar (365.25 hari/tahun, 30.44 hari/bulan):",
      "<ul class='list-disc list-inside'><li><strong>99.9% Uptime:</strong> Sekitar 8 jam 46 menit downtime per tahun, atau 43.8 menit per bulan.</li><li><strong>99.99% Uptime:</strong> Sekitar 52 menit 36 detik downtime per tahun, atau 4.38 menit per bulan.</li><li><strong>99.999% Uptime:</strong> Sekitar 5 menit 15 detik downtime per tahun, atau 26.3 detik per bulan.</li></ul>",
      "Angka-angka ini menunjukkan bahwa perbedaan desimal kecil pada persentase uptime dapat berarti perbedaan jam atau hari downtime dalam setahun. Memilih hosting dengan uptime guarantee yang lebih tinggi sangat penting untuk website atau aplikasi yang membutuhkan ketersediaan maksimal."
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="Kalkulator Uptime">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="Kalkulator Uptime & Downtime"
    description="Hitung waktu uptime dan downtime berdasarkan persentase hosting Anda. Ketahui berapa lama website akan online atau offline dalam periode waktu tertentu dengan dua kalkulator yang mudah digunakan."
    subtitle="Tool gratis untuk menghitung estimasi uptime dan downtime berdasarkan persentase hosting Anda."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Calculator Interface -->
    <div class="mb-12">
      <ToolInterface title="Kalkulator Uptime & Downtime">
        <!-- Tab Navigation -->
        <TabNavigation />

        <!-- Uptime Calculator Content -->
        <UptimeCalculatorForm />

        <!-- Downtime Calculator Content -->
        <DowntimeCalculatorForm />
      </ToolInterface>
      
      <!-- Results Section -->
      <ResultsSection />
      
      <!-- Placeholder Content -->
      <ToolPlaceholder 
        icon="tabler:calculator"
        title="Kalkulator Siap Digunakan"
        description="Masukkan nilai uptime atau downtime untuk melihat perhitungan waktu online/offline website Anda."
      />
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  /* Matching existing site design patterns */
  .uptime-preset-btn, .downtime-preset-btn {
    @apply px-3 py-2 text-sm font-medium border text-default bg-bg-input rounded-lg hover:bg-primary hover:text-white hover:border-primary transition-colors duration-200;
  }

  .uptime-preset-btn.active, .downtime-preset-btn.active {
    @apply bg-primary text-white border-primary;
  }

  /* Focus states matching site patterns */
  input:focus, button:focus {
    @apply outline-none ring-2 ring-primary ring-opacity-50;
  }

  /* Tab content transitions */
  .tab-content {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Results animation */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  #resultsContainer {
    animation: fadeIn 0.3s ease-out forwards;
  }
</style>

<!-- Calculator Logic -->
<script is:inline>
  class UptimeCalculator {
    constructor() {
      // Tab elements
      this.uptimeTab = document.getElementById('uptimeTab');
      this.downtimeTab = document.getElementById('downtimeTab');
      this.uptimeContent = document.getElementById('uptimeContent');
      this.downtimeContent = document.getElementById('downtimeContent');
      this.resultsContainer = document.getElementById('resultsContainer');
      this.placeholderContent = document.getElementById('placeholderContent');
      
      // Form elements
      this.uptimeForm = document.getElementById('uptimeForm');
      this.downtimeForm = document.getElementById('downtimeForm');
    }
    
    init() {
      // Set up tab switching
      if (this.uptimeTab && this.downtimeTab) {
        this.uptimeTab.addEventListener('click', () => this.switchTab('uptime'));
        this.downtimeTab.addEventListener('click', () => this.switchTab('downtime'));
      } else {
        console.error('Tab elements not found');
      }
      
      // Set up form submissions
      if (this.uptimeForm) {
        this.uptimeForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.calculateUptime();
        });
      }
      
      if (this.downtimeForm) {
        this.downtimeForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.calculateDowntime();
        });
      }
      
      // Set up preset buttons
      document.querySelectorAll('.uptime-preset-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const value = e.target.getAttribute('data-value');
          document.getElementById('uptimeInput').value = value;
          this.calculateUptime();
        });
      });
      
      document.querySelectorAll('.downtime-preset-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const value = e.target.getAttribute('data-value');
          document.getElementById('downtimeInput').value = value;
          this.calculateDowntime();
        });
      });
      
      // Show the first tab by default
      this.switchTab('uptime');
    }
    
    // Switch between uptime and downtime calculator tabs
    switchTab(tab) {
      if (tab === 'uptime') {
        this.uptimeTab?.classList.add('active');
        this.downtimeTab?.classList.remove('active');
        this.uptimeContent?.classList.remove('hidden');
        this.downtimeContent?.classList.add('hidden');
      } else {
        this.uptimeTab?.classList.remove('active');
        this.downtimeTab?.classList.add('active');
        this.uptimeContent?.classList.add('hidden');
        this.downtimeContent?.classList.remove('hidden');
      }
    }
    
    // Calculate uptime and show downtime results
    calculateUptime() {
      const uptimeInput = document.getElementById('uptimeInput');
      const errorElement = document.getElementById('uptimeErrorMessage');
      
      // Validate input
      const uptime = parseFloat(uptimeInput?.value);
      if (isNaN(uptime) || uptime < 0 || uptime > 100) {
        errorElement.textContent = 'Masukkan nilai antara 0 dan 100';
        errorElement?.classList.remove('hidden');
        return;
      }
      
      // Clear any previous errors
      errorElement?.classList.add('hidden');
      
      // Calculate downtime (100% - uptime%)
      const downtimePercent = 100 - uptime;
      this.showDowntimeResults(downtimePercent);
    }
    
    // Calculate downtime and show uptime results
    calculateDowntime() {
      const downtimeInput = document.getElementById('downtimeInput');
      const errorElement = document.getElementById('downtimeErrorMessage');
      
      // Validate input
      const downtime = parseFloat(downtimeInput?.value);
      if (isNaN(downtime) || downtime < 0 || downtime > 100) {
        errorElement.textContent = 'Masukkan nilai antara 0 dan 100';
        errorElement?.classList.remove('hidden');
        return;
      }
      
      // Clear any previous errors
      errorElement?.classList.add('hidden');
      
      // Calculate uptime (100% - downtime%)
      const uptimePercent = 100 - downtime;
      this.showUptimeResults(uptimePercent);
    }
    
    // Show downtime calculation results
    showDowntimeResults(downtimePercent) {
      if (!this.resultsContainer || !this.placeholderContent) {
        console.error('Results container or placeholder not found');
        return;
      }
      
      // Show results container and hide placeholder
      this.resultsContainer.classList.remove('hidden');
      this.placeholderContent.classList.add('hidden');
      
      // Show downtime results and hide uptime results
      document.getElementById('uptimeResults')?.classList.remove('hidden');
      document.getElementById('downtimeResults')?.classList.add('hidden');
      
      // Calculate times
      const yearly = (downtimePercent / 100) * 365 * 24 * 60 * 60; // seconds in a year
      const monthly = yearly / 12;
      const weekly = yearly / 52;
      const daily = yearly / 365;
      
      // Update the UI with formatted times
      this.updateElementText('yearlyDowntime', this.formatTime(yearly));
      this.updateElementText('monthlyDowntime', this.formatTime(monthly));
      this.updateElementText('weeklyDowntime', this.formatTime(weekly));
      this.updateElementText('dailyDowntime', this.formatTime(daily));
    }
    
    // Show uptime calculation results
    showUptimeResults(uptimePercent) {
      if (!this.resultsContainer || !this.placeholderContent) {
        console.error('Results container or placeholder not found');
        return;
      }
      
      // Show results container and hide placeholder
      this.resultsContainer.classList.remove('hidden');
      this.placeholderContent.classList.add('hidden');
      
      // Show uptime results and hide downtime results
      document.getElementById('uptimeResults')?.classList.add('hidden');
      document.getElementById('downtimeResults')?.classList.remove('hidden');
      
      // Calculate times
      const yearly = (uptimePercent / 100) * 365 * 24 * 60 * 60; // seconds in a year
      const monthly = yearly / 12;
      const weekly = yearly / 52;
      const daily = yearly / 365;
      
      // Update the UI with formatted times
      this.updateElementText('yearlyUptime', this.formatTime(yearly));
      this.updateElementText('monthlyUptime', this.formatTime(monthly));
      this.updateElementText('weeklyUptime', this.formatTime(weekly));
      this.updateElementText('dailyUptime', this.formatTime(daily));
      this.updateElementText('calculatedUptime', `${uptimePercent.toFixed(3)}%`);
    }
    
    // Helper to safely update element text
    updateElementText(id, text) {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = text;
      } else {
        console.warn(`Element with id '${id}' not found`);
      }
    }
    
    // Format time in a human-readable format
    formatTime(seconds) {
      if (seconds < 1) {
        // Less than 1 second, show milliseconds
        return Math.round(seconds * 1000) + ' milidetik';
      } else if (seconds < 60) {
        // Less than 1 minute, show seconds
        return Math.round(seconds) + ' detik';
      } else if (seconds < 3600) {
        // Less than 1 hour, show minutes and seconds
        const mins = Math.floor(seconds / 60);
        const secs = Math.round(seconds % 60);
        return `${mins} menit ${secs} detik`;
      } else if (seconds < 86400) {
        // Less than 1 day, show hours and minutes
        const hours = Math.floor(seconds / 3600);
        const mins = Math.round((seconds % 3600) / 60);
        return `${hours} jam ${mins} menit`;
      } else {
        // Show days and hours
        const days = Math.floor(seconds / 86400);
        const hours = Math.round((seconds % 86400) / 3600);
        return `${days} hari ${hours} jam`;
      }
    }
  }
  
  // Initialize calculator on Astro page load
  document.addEventListener('astro:page-load', () => {
    // Small delay to ensure all elements are in the DOM after transition
    setTimeout(() => {
      try {
        const calculator = new UptimeCalculator();
        calculator.init();
      } catch (error) {
        console.error('Error initializing Uptime Calculator on astro:page-load:', error);
      }
    }, 100); 
  });
</script> 