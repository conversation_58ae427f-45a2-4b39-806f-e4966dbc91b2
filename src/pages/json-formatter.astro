---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import JsonFormatterForm from '~/components/alat/json-formatter/JsonFormatterForm.astro';

const metadata = {
  title: 'JSON Formatter - Format & Validasi JSON Online | Penasihat Hosting',
  description: 'Format dan validasi string JSON dengan mudah menggunakan tool JSON Formatter online dari Penasihat Hosting.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/json-formatter.webp',
      }
    ]
  }
};

// Info sections data
const infoSections = [
  {
    title: "Apa Itu JSON dan Mengapa Penting?",
    content: [
      "JSON (JavaScript Object Notation) adalah format pertukaran data yang ringan dan mudah dibaca oleh manusia serta mudah di-parse dan di-generate oleh mesin. Format ini dibangun berdasarkan struktur data yang umum ditemui di banyak bahasa pemrograman, menjadikannya ideal untuk mengirimkan data antara server dan klien web.",
      "Pentingnya JSON terletak pada kemampuannya untuk merepresentasikan struktur data yang kompleks dengan cara yang sederhana dan standar. Ini memfasilitasi komunikasi data antara sistem yang berbeda, API, konfigurasi file, dan banyak lagi."
    ]
  },
  {
    title: "Mengapa Menggunakan JSON Formatter?",
    content: [
      "Ketika bekerja dengan data JSON, terutama yang berasal dari API atau file log, seringkali formatnya padat tanpa spasi atau indentasi, membuatnya sulit dibaca. JSON formatter membantu dengan:",
      "<ul class='list-disc list-inside'><li><strong>Keterbacaan:</strong> Menambahkan spasi putih dan indentasi yang konsisten.</li><li><strong>Validasi:</strong> Memeriksa apakah string JSON memiliki sintaks yang benar.</li><li><strong>Debugging:</strong> Memudahkan identifikasi kesalahan dalam struktur data JSON.</li></ul>"
    ]
  },
  {
    title: "Struktur Dasar JSON",
    content: [
      "JSON dibangun di atas dua struktur dasar:",
      "<ul class='list-disc list-inside'><li><strong>Objek:</strong> Koleksi pasangan kunci-nilai yang tidak berurutan. Dimulai dengan `{` dan diakhiri dengan `}`, dengan pasangan kunci-nilai dipisahkan oleh koma. Kunci harus berupa string dalam tanda kutip ganda (`\"`). Nilai bisa berupa string, angka, boolean, null, objek lain, atau array. Contoh: `\"nama\":\"nilai\"`.</li><li><strong>Array:</strong> Koleksi nilai yang berurutan. Dimulai dengan `[` dan diakhiri dengan `]`, dengan nilai dipisahkan oleh koma. Contoh: `[\"nilai1\", \"nilai2\"]`.</li></ul>",
      "Kombinasi objek dan array memungkinkan representasi struktur data yang kompleks."
    ]
  }
];
---

<ToolLayout metadata={metadata} currentPage="JSON Formatter">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="JSON Formatter"
    description="Rapikan dan periksa keabsahan struktur data JSON Anda dengan mudah. Input JSON mentah dan dapatkan output yang terformat rapi dan mudah dibaca."
    subtitle="Tool gratis untuk memformat dan memvalidasi JSON."
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <ToolInterface title="JSON Formatter">
        <JsonFormatterForm />
      </ToolInterface>
    </div>
    
    <!-- Information Sections -->
    <div class="mb-12">
      {infoSections.map((section) => (
        <ToolInfoSection 
          title={section.title}
          content={section.content}
          className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
        />
      ))}
    </div>
  </ToolContainer>
</ToolLayout>

<style>
  /* Add styling consistent with other tools if needed */
</style>

<!-- JSON Formatter Logic -->
<script is:inline>
  class JsonFormatter {
    constructor() {
      this.form = document.getElementById('jsonFormatterForm');
      this.jsonInput = document.getElementById('jsonInput');
      this.jsonOutput = document.getElementById('jsonOutput');
      this.errorMessageElement = document.getElementById('jsonErrorMessage');
    }

    init() {
      if (this.form) {
        this.form.addEventListener('submit', (e) => {
          e.preventDefault();
          this.formatJson();
        });
      }
    }

    formatJson() {
      const jsonString = this.jsonInput?.value;
      if (!jsonString) {
        if (this.errorMessageElement) {
          this.errorMessageElement.textContent = 'Input JSON tidak boleh kosong.';
          this.errorMessageElement.classList.remove('hidden');
        }
        return;
      }

      try {
        // Parse JSON to validate and then stringify with formatting
        const parsedJson = JSON.parse(jsonString);
        const formattedJson = JSON.stringify(parsedJson, null, 2);
        
        if (this.jsonOutput) {
          this.jsonOutput.value = formattedJson;
        }
        
        if (this.errorMessageElement) {
          this.errorMessageElement.classList.add('hidden');
        }
      } catch (error) {
        if (this.errorMessageElement) {
          this.errorMessageElement.textContent = `Error: ${error.message}`;
          this.errorMessageElement.classList.remove('hidden');
        }
        if (this.jsonOutput) {
          this.jsonOutput.value = '';
        }
      }
    }
  }

  // Initialize formatter on Astro page load
  document.addEventListener('astro:page-load', () => {
    setTimeout(() => {
      try {
        const formatter = new JsonFormatter();
        formatter.init();
      } catch (error) {
        console.error('Error initializing JSON Formatter on astro:page-load:', error);
      }
    }, 100);
  });
</script> 