---
import Layout from '~/layouts/PageLayout.astro';
import HomeHero from '~/components/home/<USER>';
import IntroductionSection from '~/components/home/<USER>';
import HostingComparisonTable from '~/components/home/<USER>';
import HostingRecommendationSection from '~/components/home/<USER>';
import { idcloudhost } from '~/components/home/<USER>/data/hosting-terbaik/idcloudhost';
import { kencengsolusindo } from '~/components/home/<USER>/data/hosting-terbaik/kencengsolusindo';
import { domainesia } from '~/components/home/<USER>/data/hosting-terbaik/domainesia';
import { warnahost } from '~/components/home/<USER>/data/hosting-terbaik/warnahost';
import { jagoanhosting } from '~/components/home/<USER>/data/hosting-terbaik/jagoanhosting';
import { hostinganid } from '~/components/home/<USER>/data/hosting-terbaik/hostinganid';
import { getCollection } from 'astro:content';

const metadata = {
  title: '6 Hosting Terbaik Indonesia 2025 (Review Uptime & Speed)',
  description: 'Review web hosting terbaik ini merupakan hasil dari penelitian mendalam yang kami lakukan hingga tahun 2025.',
  ignoreTitleTemplate: true,
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/web-hosting-terbaik-indonesia.webp',
      }
    ]
  }
};

// Dynamic total providers from directory (active only)
const allProviders = await getCollection('hosting-providers');
const totalProviders = allProviders.filter(p => p.data.isActive).length;

const heroProps = {
  title: '6 Web Hosting Terbaik Indonesia 2025',
  description: 'Review web hosting terbaik ini merupakan hasil dari penelitian mendalam yang kami lakukan hingga tahun 2025.',
  author: 'Willya Randika',
  date: 'August 5, 2025',
  url: 'https://penasihathosting.com/',
};

const introductionContent = {
  paragraphs: [
    "Anda serius ingin <a href='/cara-membuat-website/'>membuat website</a>?",
    "Langkah pertama yang harus Anda ambil adalah memilih web hosting yang andal.",
    "Web hosting yang tidak hanya memiliki server yang cepat, tetapi juga uptime yang stabil dan layanan dukungan yang responsif.",
    `Masalahnya, ada lebih dari <a href='/direktori-hosting/'>${totalProviders}+ pilihan web hosting</a> yang tersedia.`,
    "Bagaimana cara menemukan web hosting terbaik di antara ratusan pilihan tersebut? Memang tidak mudah, bisa memakan waktu dan cukup melelahkan.",
    "Namun ada kabar baik. Kami di Penasihat Hosting telah melakukan riset hosting sejak 2019. Hasil riset kami dapat membantu Anda menemukan hosting terbaik yang sesuai dengan kebutuhan Anda, tanpa perlu repot-repot."
  ]
};


// Reviews data
const reviews = [
  idcloudhost,
  kencengsolusindo,
  domainesia,
  warnahost,
  jagoanhosting,
  hostinganid
];

---

<Layout metadata={metadata}>
  <HomeHero {...heroProps} />
  <IntroductionSection defaultContent={introductionContent} />
  <HostingComparisonTable />
  <HostingRecommendationSection
    reviews={reviews}
    title="Pilihan Penasihat Hosting: 6 Shared Hosting Terbaik Indonesia di 2025"
    intro="Berdasarkan hasil evaluasi ini, saya siap memberikan rekomendasi hosting terbaik untuk Anda:"
    paragraphs={[
      'Saya baru saja menyelesaikan evaluasi ulang untuk awal tahun 2025.',
      'Proses ini meliputi pengujian kecepatan (load testing) semua provider hosting yang masuk dalam daftar review saya tahun ini.'
    ]}
    hostingList={reviews.map(review => review.displayName || review.providerName)}
    showHostingList={true}
    showSidebar={true}
    showFAQ={true}
  />
</Layout>
