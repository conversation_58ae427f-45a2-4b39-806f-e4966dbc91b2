---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import { Icon } from 'astro-icon/components';

const metadata = {
  title: 'Hubu<PERSON><PERSON> Kami - Penasihat Hosting',
  description: 'Hubungi tim <PERSON>hat Hosting untuk pertanyaan seputar web hosting, domain, dan pembuatan website melalui form kontak atau email.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/hubungi-kami-contact-form.webp',
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Hero Section - Matching NYC design patterns -->
  <section class="bg-bg-page dark:bg-dark py-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="text-xs text-section mb-6">
        Hu<PERSON><PERSON>i tim <PERSON>hat Hosting untuk pertanyaan seputar web hosting, domain, dan pembuatan website.
      </div>
      <div class="max-w-full lg:max-w-[65ch]">
        <div class="border-t-8 border-primary pt-2 mb-4">
          <h1 class="text-4xl md:text-5xl font-extrabold text-heading mb-3">
            Hubungi Kami
          </h1>
          <p class="text-lg text-default mb-4">
            Kirim pesan kepada kami melalui form di bawah ini atau hubungi langsung melalui email untuk pertanyaan seputar hosting, domain, dan pembuatan website.
          </p>
          <div class="flex items-center text-sm text-section mb-4">
            <span class="font-medium">Penasihat Hosting</span>
            <span class="mx-2">•</span>
            <span>Form Kontak</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content Section - Matching NYC design patterns -->
  <section class="bg-bg-page dark:bg-dark pb-12">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Contact Form - 2/3 Width -->
        <div class="lg:col-span-2">
          <!-- Contact Form -->
          <div class="py-4 sm:py-6 bg-bg-section px-4 sm:px-6 rounded-lg mb-6">
            <h2 class="text-xl font-bold text-heading mb-6 flex items-center">
              <Icon name="tabler:mail" class="w-6 h-6 mr-3 text-primary" />
              Form Kontak
            </h2>

            <form
              action="https://api.web3forms.com/submit"
              method="POST"
              id="contactForm"
              class="needs-validation space-y-6"
              data-astro-reload
              novalidate
            >
              <!-- Web3Forms Access Key -->
              <input type="hidden" name="access_key" value="556655dd-0f77-4b99-9776-0e995cd3c1b0" />

              <!-- Honeypot Spam Protection -->
              <input type="checkbox" class="hidden" style="display:none" name="botcheck" />

              <!-- Custom Subject -->
              <input type="hidden" name="subject" value="Pesan Baru dari Form Kontak - Penasihat Hosting" />

              <!-- From Name -->
              <input type="hidden" name="from_name" value="Penasihat Hosting Contact Form" />

              <!-- Name Field -->
              <div>
                <label for="name" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                  Nama Lengkap <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  placeholder="Masukkan nama lengkap Anda"
                  class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                />
                <div class="empty-feedback invalid-feedback text-red-400 text-sm mt-1">
                  Mohon masukkan nama lengkap Anda.
                </div>
              </div>

              <!-- Email Field -->
              <div>
                <label for="email" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                  Email <span class="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                />
                <div class="empty-feedback text-red-400 text-sm mt-1">
                  Mohon masukkan alamat email Anda.
                </div>
                <div class="invalid-feedback text-red-400 text-sm mt-1">
                  Mohon masukkan alamat email yang valid.
                </div>
              </div>

              <!-- Reason for Contact Field -->
              <div>
                <label for="reason" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                  Alasan Menghubungi <span class="text-red-500">*</span>
                </label>
                <select
                  id="reason"
                  name="reason"
                  required
                  class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                >
                  <option value="">Pilih alasan menghubungi kami</option>
                  <option value="Pertanyaan Hosting">Pertanyaan tentang Web Hosting</option>
                  <option value="Pertanyaan Domain">Pertanyaan tentang Domain</option>
                  <option value="Pembuatan Website">Bantuan Pembuatan Website</option>
                  <option value="Review Hosting">Permintaan Review Hosting</option>
                  <option value="Kerjasama">Kerjasama & Partnership</option>
                  <option value="Masalah Teknis">Masalah Teknis Website</option>
                  <option value="Lainnya">Lainnya</option>
                </select>
                <div class="empty-feedback invalid-feedback text-red-400 text-sm mt-1">
                  Mohon pilih alasan menghubungi kami.
                </div>
              </div>

              <!-- Message Field -->
              <div>
                <label for="message" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                  Pesan <span class="text-red-500">*</span>
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows="6"
                  placeholder="Tulis pesan Anda di sini..."
                  class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-vertical"
                ></textarea>
                <div class="empty-feedback invalid-feedback text-red-400 text-sm mt-1">
                  Mohon tulis pesan Anda.
                </div>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
              >
                <Icon name="tabler:send" class="w-5 h-5 mr-2" />
                Kirim Pesan
              </button>

              <!-- Result Message -->
              <div id="result" class="mt-3 text-center"></div>
            </form>
          </div>
        </div>

        <!-- Contact Information Sidebar - 1/3 Width -->
        <div class="lg:col-span-1">
          <!-- Email Contact -->
          <div class="py-4 sm:py-6 bg-bg-section px-4 sm:px-6 rounded-lg mb-6">
            <h3 class="text-xl font-bold text-heading mb-4 flex items-center">
              <Icon name="tabler:mail" class="w-6 h-6 mr-3 text-primary" />
              Email Langsung
            </h3>
            <p class="text-sm text-default mb-4">
              Untuk pertanyaan mendesak, Anda juga dapat menghubungi kami langsung melalui email:
            </p>
            <a
              href="mailto:<EMAIL>"
              class="inline-block bg-primary hover:bg-accent text-white font-medium py-2 px-4 rounded-lg transition duration-300 text-sm"
              style="color: white !important;"
            >
              randi[at]penasihathosting.com
            </a>
          </div>

          <!-- Guidelines -->
          <div class="py-4 sm:py-6 bg-bg-section px-4 sm:px-6 rounded-lg mb-6">
            <h3 class="text-xl font-bold text-heading mb-4 flex items-center">
              <Icon name="tabler:info-circle" class="w-6 h-6 mr-3 text-primary" />
              Panduan Kontak
            </h3>
            <div class="space-y-4">
              <div>
                <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2 flex items-center">
                  <Icon name="tabler:check" class="w-4 h-4 mr-2 text-green-500" />
                  Kami Dapat Membantu:
                </h4>
                <ul class="text-sm text-gray-600 dark:text-gray-300 space-y-1 ml-6">
                  <li>• Pertanyaan tentang web hosting dan domain</li>
                  <li>• Panduan pemilihan hosting yang tepat</li>
                  <li>• Informasi umum tentang pembuatan website</li>
                  <li>• Review hosting dan rekomendasi</li>
                </ul>
              </div>

              <div>
                <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-2 flex items-center">
                  <Icon name="tabler:alert-triangle" class="w-4 h-4 mr-2 text-red-500" />
                  Mohon Perhatikan:
                </h4>
                <ul class="text-sm text-gray-600 dark:text-gray-300 space-y-1 ml-6">
                  <li>• Tidak semua pertanyaan dapat kami jawab</li>
                  <li>• Fokus pada topik hosting, domain, dan website</li>
                  <li>• Tidak melayani konten ilegal atau melanggar hukum</li>
                  <li>• Masalah teknis spesifik sebaiknya hubungi provider langsung</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Response Time -->
          <div class="py-4 sm:py-6 bg-bg-section px-4 sm:px-6 rounded-lg">
            <h3 class="text-xl font-bold text-gray-700 dark:text-gray-200 mb-4 flex items-center">
              <Icon name="tabler:clock" class="w-6 h-6 mr-3 text-primary" />
              Waktu Respon
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Kami berusaha membalas pesan dalam waktu 1-2 hari kerja. Untuk pertanyaan mendesak, silakan gunakan email langsung.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>

<style>
  /* Form validation styles matching Web3Forms patterns */
  .invalid-feedback,
  .empty-feedback {
    display: none;
  }

  .was-validated :placeholder-shown:invalid ~ .empty-feedback {
    display: block;
  }

  .was-validated :not(:placeholder-shown):invalid ~ .invalid-feedback {
    display: block;
  }

  .is-invalid,
  .was-validated :invalid {
    border-color: #dc3545;
  }

  /* Focus states matching site patterns */
  input:focus,
  select:focus,
  textarea:focus {
    outline: none;
  }

  /* Success and error message styles */
  .text-green-500 {
    color: #10b981;
  }

  .text-red-500 {
    color: #ef4444;
  }
</style>

<script is:inline>
  // Web3Forms Contact Form Handler
  document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("contactForm");
    const result = document.getElementById("result");

    if (!form || !result) return;

    form.addEventListener("submit", function (e) {
      e.preventDefault();
      form.classList.add("was-validated");

      if (!form.checkValidity()) {
        form.querySelectorAll(":invalid")[0].focus();
        return;
      }

      const formData = new FormData(form);
      const object = Object.fromEntries(formData);
      const json = JSON.stringify(object);

      result.innerHTML = "Mengirim pesan...";
      result.className = "mt-3 text-center text-blue-600";

      fetch("https://api.web3forms.com/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: json,
      })
        .then(async (response) => {
          const responseJson = await response.json();
          if (response.status == 200) {
            result.classList.remove("text-blue-600");
            result.classList.add("text-green-500");
            result.innerHTML = "✅ Pesan berhasil dikirim! Kami akan membalas dalam 1-2 hari kerja.";
          } else {
            console.log(response);
            result.classList.remove("text-blue-600");
            result.classList.add("text-red-500");
            result.innerHTML = "❌ " + responseJson.message;
          }
        })
        .catch((error) => {
          console.log(error);
          result.classList.remove("text-blue-600");
          result.classList.add("text-red-500");
          result.innerHTML = "❌ Terjadi kesalahan! Silakan coba lagi atau hubungi kami melalui email.";
        })
        .then(function () {
          form.reset();
          form.classList.remove("was-validated");
          setTimeout(() => {
            result.style.display = "none";
          }, 8000);
        });
    });
  });
</script>
