---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import BackToTop from '~/components/common/BackToTop.astro';
import Button from '~/components/ui/Button.astro';

const metadata = {
  title: 'Tools & Utilities - Alat Developer Gratis | Penasihat Hosting',
  description: 'Kumpulan tools dan utilities gratis untuk developer dan webmaster. Termasuk kalkulator uptime, password generator, hash generator, JSON formatter, Base64 converter, dan URL encoder.',
  canonical: 'https://penasihathosting.com/alat/',
  openGraph: {
    type: 'website',
    title: 'Tools & Utilities - Alat Developer Gratis | Penasihat Hosting',
    description: 'Kumpulan tools dan utilities gratis untuk developer dan webmaster di Indonesia.',
    url: 'https://penasihathosting.com/alat/',
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/tools-utilities.webp',
        width: 1200,
        height: 630,
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Breadcrumbs -->
  <section class="bg-bg-muted py-2 border-b border-gray-200">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm">
          <li>
            <a href="/" class="text-muted hover:text-primary transition-colors">
              Home
            </a>
          </li>
          <li class="text-gray-400">/</li>
          <li>
            <span class="text-heading font-medium">Tools & Utilities</span>
          </li>
        </ol>
      </nav>
    </div>
  </section>

  <!-- Hero Section -->
  <section class="bg-bg-page dark:bg-bg-section py-8">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="max-w-full lg:max-w-[65ch] border-t-8 border-primary pt-2">
        <h1 class="text-4xl md:text-5xl font-extrabold text-heading mb-4">
          Tools & <span class="italic text-primary">Utilities</span>
        </h1>
        <p class=" text-muted mb-6 leading-relaxed">
          Kumpulan tools dan utilities gratis untuk mempermudah pekerjaan developer dan webmaster dalam mengelola website dan aplikasi web.
        </p>
        <p class="text-sm text-muted">
          Semua tools berjalan di browser • Data tidak disimpan di server kami
        </p>
      </div>
    </div>
  </section>

  <!-- Content Section -->
  <section class="pb-12">
    <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="max-w-4xl">


        <!-- Sponsor Section -->
        <div class="mb-12">
          <div class="border-t-2 border-primary pt-2 mb-6">
            <div class="flex items-center mb-4">
   
              <h2 class="text-2xl font-bold text-heading">Sponsor</h2>
            </div>
          </div>

          <div class="bg-bg-section border border-gray-200 rounded-md p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-start">
                <div class="w-16 h-16 bg-white border border-gray-200 rounded-md flex items-center justify-center mr-4 flex-shrink-0 p-2">
                  <img 
                    src="https://cdn.penasihathosting.com/hosting/kinsta/Kinsta-logo.webp" 
                    alt="Kinsta Logo" 
                    class="w-full h-full object-contain"
                  />
                </div>
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h3 class="font-semibold text-heading">Kinsta Hosting</h3>
                    <span class="ml-3 text-xs font-medium text-muted bg-bg-muted px-2 py-1 rounded">SPONSOR</span>
                  </div>
                  <p class="text-sm leading-relaxed">Managed WordPress hosting dengan infrastruktur Google Cloud dan dukungan WordPress Expert. Cocok untuk website bisnis yang membutuhkan keamanan dan performa tinggi.</p>
                </div>
              </div>
              <div class="ml-6 flex-shrink-0">
                <Button
                  variant="primary"
                  href="https://penasihathosting.com/go/kinsta"
                  text="Lihat Penawaran"
                  icon="tabler:arrow-right"
                  rel="nofollow"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- Tools & Utilities -->
        <div class="mb-12">
          <div class="border-t-2 border-primary pt-2 mb-6">
            <div class="flex items-center mb-4">

              <h2 class="text-2xl font-bold text-heading">Tools & Utilities</h2>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Kalkulator Uptime -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-blue-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">Kalkulator Uptime</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Hitung persentase uptime dan downtime website atau server Anda. Berguna untuk menganalisis keandalan hosting dan memahami dampak downtime terhadap bisnis.</p>
                  <a href="/kalkulator-uptime/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- Password Generator -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-green-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">Password Generator</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Buat password kuat dan acak untuk melindungi akun hosting, database, dan aplikasi web Anda. Dapat dikustomisasi dengan berbagai karakter dan panjang.</p>
                  <a href="/password-generator/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- Hash Generator -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-purple-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">Hash Generator</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Generate hash MD5, SHA-1, SHA-256 untuk verifikasi integritas file, password hashing, atau kebutuhan keamanan lainnya dalam pengembangan web.</p>
                  <a href="/hash-generator/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- JSON Formatter -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-yellow-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">JSON Formatter</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Format, validate, dan beautify JSON data. Berguna untuk debugging API, konfigurasi aplikasi, dan development website yang menggunakan JSON.</p>
                  <a href="/json-formatter/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- Base64 Converter -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-red-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">Base64 Converter</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Encode dan decode data ke/dari format Base64. Sering digunakan untuk encoding gambar di CSS, data URL, atau transfer data dalam web development.</p>
                  <a href="/base64-converter/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <!-- URL Encoder -->
            <div class="bg-bg-section border border-gray-200 rounded-md p-6">
              <div class="flex items-start mb-4">
                <div class="w-8 h-8 bg-indigo-500/10 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class=" font-semibold text-heading mb-2">URL Encoder</h3>
                  <p class="text-muted text-sm leading-relaxed mb-4">Encode dan decode URL untuk menangani karakter khusus dalam URL. Berguna untuk parameter URL, query strings, dan pengembangan aplikasi web.</p>
                  <a href="/url-encoder/" class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm">
                    <span>Buka Tool</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>
  </section>

  <BackToTop />
</Layout>
