@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .bg-page {
    background-color: var(--aw-color-bg-page);
  }
  .bg-dark {
    background-color: var(--aw-color-bg-page-dark);
  }
  .bg-light {
    background-color: var(--aw-color-bg-page);
  }
  .text-page {
    color: var(--aw-color-text-page);
  }
  .text-muted {
    color: var(--aw-color-text-muted);
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-full border-gray-400 border bg-transparent font-medium text-center text-base text-page leading-snug transition py-3.5 px-6 md:px-8 ease-in duration-200 focus:ring-blue-500 focus:ring-offset-blue-200 focus:ring-2 focus:ring-offset-2 hover:bg-gray-100 hover:border-gray-600 dark:text-slate-300 dark:border-slate-500 dark:hover:bg-slate-800 dark:hover:border-slate-800 cursor-pointer;
  }

  .btn-primary {
    @apply btn font-semibold bg-primary text-white border-primary hover:bg-secondary hover:border-secondary hover:text-white dark:text-white dark:bg-primary dark:border-primary dark:hover:border-secondary dark:hover:bg-secondary;
  }

  .btn-secondary {
    @apply btn;
  }

  .btn-tertiary {
    @apply btn border-none shadow-none text-muted hover:text-gray-900 dark:text-gray-400 dark:hover:text-white;
  }
}

#header.scroll > div:first-child {
  @apply bg-page md:bg-white/90 md:backdrop-blur-md;
  box-shadow: 0 0.375rem 1.5rem 0 rgb(140 152 164 / 13%);
}
.dark #header.scroll > div:first-child,
#header.scroll.dark > div:first-child {
  @apply bg-page md:bg-[#030621e6] border-b border-gray-500/20;
  box-shadow: none;
}
/* #header.scroll > div:last-child {
  @apply py-3;
} */

#header.expanded nav {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  bottom: 70px !important;
  padding: 0 5px;
}

.dropdown:focus .dropdown-menu,
.dropdown:focus-within .dropdown-menu,
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Mega Menu Styles - Desktop */
@media (min-width: 768px) {
  .dropdown .dropdown-menu {
    @apply opacity-0 invisible translate-y-2 transition-all duration-300 ease-out;
  }

  .dropdown:hover .dropdown-menu,
  .dropdown:focus-within .dropdown-menu {
    @apply opacity-100 visible translate-y-0;
  }

  /* Mega Menu specific positioning */
  .dropdown .dropdown-menu[class*="md:w-[680px]"] {
    @apply left-1/2 transform -translate-x-1/2;
    top: 100%;
  }

  .dropdown .dropdown-menu[class*="md:w-[720px]"] {
    @apply left-1/2 transform -translate-x-1/2;
    top: 100%;
  }

  /* Enhanced hover effects for mega menu items */
  .dropdown .dropdown-menu a {
    @apply transition-all duration-200;
  }
}

/* Dropdown Menu Styles */
.dropdown .dropdown-menu {
  @apply absolute left-0 right-0 md:left-auto md:right-auto mt-1 w-full md:w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none transition-all duration-200 ease-in-out origin-top-right z-50;
  margin-top: -0.5rem;
}

/* Mega Menu specific styles */
.dropdown .dropdown-menu[class*="md:w-[680px]"] {
  @apply md:left-1/2 md:transform md:-translate-x-1/2 md:w-[680px];
}

.dropdown .dropdown-menu[class*="md:w-[720px]"] {
  @apply md:left-1/2 md:transform md:-translate-x-1/2 md:w-[720px];
}

/* Show dropdown when expanded */
.dropdown.expanded .dropdown-menu {
  @apply opacity-100 visible translate-y-0;
}

/* Dropdown menu items */
.dropdown-menu a {
  @apply block px-4 py-2.5 text-sm transition-colors duration-150;
  color: var(--aw-color-text-default);
}

.dropdown-menu a:hover {
  color: var(--aw-color-text-default);
}

/* Mobile specific styles */
@media (max-width: 767px) {
  .dropdown .dropdown-menu {
    @apply relative mt-0 w-full shadow-none rounded-none border-l-0 border-r-0 border-gray-100 dark:border-gray-700;
    transform: none;
    opacity: 0;
    max-height: 0;
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out;
    overflow: hidden;
  }
  
  .dropdown.expanded .dropdown-menu {
    @apply border-t border-gray-100 dark:border-gray-700;
    max-height: 80vh;
    opacity: 1;
    overflow-y: auto;
  }
  
  /* Mega menu specific mobile styles */
  .dropdown .dropdown-menu[class*="md:w-[680px]"],
  .dropdown .dropdown-menu[class*="md:w-[720px]"] {
    @apply p-0 m-0 w-full max-w-none rounded-none border-0;
    transform: none !important;
    position: static;
    box-shadow: none;
  }
  
  .dropdown-menu a {
    @apply py-3 px-6 border-b border-gray-100 dark:border-gray-700;
  }
  
  /* Grid layout for mega menu items */
  .dropdown-menu .grid {
    @apply grid-cols-1 gap-0 !important;
  }
  
  /* Featured item in mega menu */
  .dropdown-menu .col-span-2 {
    @apply col-span-1;
  }
  
  /* Adjust padding for better mobile touch targets */
  .dropdown-menu a, 
  .dropdown-menu .p-6 {
    @apply py-4 px-6;
  }
}

/* Gradient border animation for featured items */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-border {
  background: linear-gradient(-45deg, #C10007, #A50006, #8B0005, #C10007);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
}

/* Responsive mega menu */
@media (max-width: 768px) {
  .dropdown .dropdown-menu[class*='md:w-[680px]'] {
    @apply w-full left-0 transform-none;
  }
}

[astro-icon].icon-light > * {
  stroke-width: 1.2;
}

[astro-icon].icon-bold > * {
  stroke-width: 2.4;
}

[data-aw-toggle-menu] path {
  @apply transition;
}
[data-aw-toggle-menu].expanded g > path:first-child {
  @apply -rotate-45 translate-y-[15px] translate-x-[-3px];
}

[data-aw-toggle-menu].expanded g > path:last-child {
  @apply rotate-45 translate-y-[-8px] translate-x-[14px];
}

/* To deprecated */

.dd *:first-child {
  margin-top: 0;
}
