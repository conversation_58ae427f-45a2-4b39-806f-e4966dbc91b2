import { 
  searchState, 
  resetSearchState, 
  setSearchTimeout, 
  clearSearchTimeout,
  type SearchElements 
} from './searchState';
import { executeSearch, clearSearchResults, navigateResults, selectCurrentResult } from './searchEngine';
import { renderSearchResults, clearResultsDisplay } from './searchResultsRenderer';

/**
 * Open search overlay with animation
 */
export function openSearch(elements: SearchElements): void {
  const { overlay, input } = elements;
  
  searchState.isOpen = true;
  overlay.classList.remove('hidden');
  
  // Use CSS transitions instead of manual manipulation
  requestAnimationFrame(() => {
    overlay.classList.add('open');
  });
  
  // Focus input after animation starts
  setTimeout(() => input.focus(), 100);
  
  // Prevent body scroll
  document.body.style.overflow = 'hidden';
}

/**
 * Close search overlay with animation
 */
export function closeSearch(elements: SearchElements): void {
  const { overlay, input, resultsContainer } = elements;
  
  searchState.isOpen = false;
  overlay.classList.remove('open');
  
  // Wait for animation to complete before hiding
  setTimeout(() => {
    overlay.classList.add('hidden');
    document.body.style.overflow = '';
    input.value = '';
    resetSearchState();
    clearResultsDisplay(resultsContainer);
  }, 300); // Match CSS transition duration
}

/**
 * Handle search input with debouncing
 */
export function handleSearchInput(e: Event, elements: SearchElements): void {
  const query = (e.target as HTMLInputElement).value;
  searchState.query = query;
  
  clearSearchTimeout();
  
  if (query.length >= 2) {
    setSearchTimeout(() => {
      executeSearch(query);
      if (elements.resultsContainer) {
        renderSearchResults(elements.resultsContainer);
      }
    });
  } else {
    clearSearchResults();
    clearResultsDisplay(elements.resultsContainer);
  }
}

/**
 * Handle keyboard navigation in search
 */
export function handleSearchKeydown(e: KeyboardEvent, elements: SearchElements): void {
  switch (e.key) {
    case 'Escape':
      e.preventDefault();
      closeSearch(elements);
      break;
    case 'ArrowDown':
      e.preventDefault();
      navigateResults(1);
      break;
    case 'ArrowUp':
      e.preventDefault();
      navigateResults(-1);
      break;
    case 'Enter':
      e.preventDefault();
      selectCurrentResult();
      break;
  }
}

/**
 * Handle global escape key to close search
 */
export function handleGlobalKeydown(e: KeyboardEvent, elements: SearchElements): void {
  if (e.key === 'Escape' && searchState.isOpen) {
    closeSearch(elements);
  }
}

/**
 * Setup all event listeners for search functionality
 */
export function setupSearchEventListeners(elements: SearchElements): () => void {
  const { input, closeBtn, backdrop, resultsContainer } = elements;

  // Event handler functions with proper binding
  const handleInputChange = (e: Event) => handleSearchInput(e, elements);
  const handleKeydown = (e: KeyboardEvent) => handleSearchKeydown(e, elements);
  const handleGlobalKey = (e: KeyboardEvent) => handleGlobalKeydown(e, elements);
  const handleClose = () => closeSearch(elements);
  const handleOpen = () => openSearch(elements);
  const handleCloseEvent = () => closeSearch(elements);

  // Handle clicks on search results
  const handleResultClick = (e: Event) => {
    const target = e.target as HTMLElement;
    const resultLink = target.closest('a[data-result-index]') as HTMLAnchorElement;
    
    if (resultLink) {
      e.preventDefault();
      
      // Close search first
      closeSearch(elements);
      
      // Use Astro's navigation if available, fallback to location
      setTimeout(() => {
        // Check if we're in an Astro environment with View Transitions
        if (typeof window !== 'undefined' && 'astro' in window) {
          // Use Astro's router for better View Transitions
          window.location.href = resultLink.href;
        } else {
          // Fallback for non-Astro environments
          window.location.href = resultLink.href;
        }
      }, 300); // Match animation duration
    }
  };

  // Remove existing listeners first to prevent duplicates (View Transitions best practice)
  document.removeEventListener('search:open', handleOpen);
  document.removeEventListener('search:close', handleCloseEvent);
  input.removeEventListener('input', handleInputChange);
  input.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('keydown', handleGlobalKey);
  closeBtn?.removeEventListener('click', handleClose);
  backdrop?.removeEventListener('click', handleClose);
  resultsContainer?.removeEventListener('click', handleResultClick);

  // Add fresh event listeners
  document.addEventListener('search:open', handleOpen);
  document.addEventListener('search:close', handleCloseEvent);
  input.addEventListener('input', handleInputChange);
  input.addEventListener('keydown', handleKeydown);
  document.addEventListener('keydown', handleGlobalKey);
  closeBtn?.addEventListener('click', handleClose);
  backdrop?.addEventListener('click', handleClose);
  resultsContainer?.addEventListener('click', handleResultClick);

  // Return cleanup function
  return () => {
    document.removeEventListener('search:open', handleOpen);
    document.removeEventListener('search:close', handleCloseEvent);
    input.removeEventListener('input', handleInputChange);
    input.removeEventListener('keydown', handleKeydown);
    document.removeEventListener('keydown', handleGlobalKey);
    closeBtn?.removeEventListener('click', handleClose);
    backdrop?.removeEventListener('click', handleClose);
    resultsContainer?.removeEventListener('click', handleResultClick);
  };
}
