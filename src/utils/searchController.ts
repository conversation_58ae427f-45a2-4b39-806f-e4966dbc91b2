import { getSearchElements, loadSearchIndex, searchState, resetSearchState } from './searchState';
import { setupSearchEventListeners } from './searchEventHandlers';

let cleanupFunction: (() => void) | null = null;

/**
 * Initialize search functionality
 */
export async function initializeSearch(): Promise<void> {
  // Small delay to ensure all elements are in the DOM after transition
  setTimeout(async () => {
    try {
      // Get DOM elements
      const elements = getSearchElements();
      if (!elements) {
        return;
      }

      // Load search index
      await loadSearchIndex();

      // Cleanup previous listeners if they exist
      if (cleanupFunction) {
        cleanupFunction();
      }

      // Setup event listeners
      cleanupFunction = setupSearchEventListeners(elements);

    } catch (error) {
      console.error('Error initializing search:', error);
    }
  }, 100); // Small delay to ensure DOM is ready
}

/**
 * Cleanup search functionality
 */
export function cleanupSearch(): void {
  if (cleanupFunction) {
    cleanupFunction();
    cleanupFunction = null;
  }
}

/**
 * Setup Astro lifecycle events for search
 */
export function setupSearchLifecycle(): void {
  // Primary event for Astro View Transitions - this is the most important one
  document.addEventListener('astro:page-load', initializeSearch);

  // Fallback for direct navigation and initial page load
  document.addEventListener('DOMContentLoaded', initializeSearch);

  // Additional reliability for View Transitions
  document.addEventListener('astro:after-swap', () => {
    // Small delay to ensure DOM is fully swapped
    setTimeout(initializeSearch, 50);
  });

  // Add View Transition specific events
  document.addEventListener('astro:before-preparation', () => {
    // Cleanup before page transition
    if (searchState.isOpen) {
      const elements = getSearchElements();
      if (elements) {
        // Import closeSearch dynamically to avoid circular dependency
        import('./searchEventHandlers').then(({ closeSearch }) => {
          closeSearch(elements);
        });
      }
    }
  });

  document.addEventListener('astro:after-preparation', () => {
    // Reset state after preparation
    resetSearchState();
  });
}
