import { getCollection } from 'astro:content';

export interface SearchProvider {
  id: string;
  name: string;
  displayName?: string;
  slug: string;
  categories: string[];
  locations: string[];
  features: string[];
  startingPrice: number;
  formattedPrice: string;
  url: string;
  description: string;
  logo: string;
  featured?: boolean; // Menandai provider unggulan
  type: 'provider';
}

export interface SearchCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  url: string;
  providerCount: number;
  featured: boolean;
  type: 'category';
}

export interface SearchPage {
  id: string;
  title: string;
  slug: string;
  description: string;
  url: string;
  content?: string;
  type: 'page';
}

export interface SearchReview {
  id: string;
  title: string;
  slug: string;
  description: string;
  url: string;
  content?: string;
  type: 'review';
}

export interface SearchIndex {
  providers: SearchProvider[];
  categories: SearchCategory[];
  reviews: SearchReview[];
  pages: SearchPage[];
  lastUpdated: string;
}

/**
 * Generate search index from content collections
 */
export async function generateSearchIndex(): Promise<SearchIndex> {
  const [providers, categories, blogPosts] = await Promise.all([
    getCollection('hosting-providers'),
    getCollection('hosting-categories'),
    getCollection('post')
  ]);

  // Process providers
  const searchProviders: SearchProvider[] = providers.map(provider => {
    const supportChannels = provider.data.company?.supportChannels || [];
    const features = [
      ...(provider.data.controlPanels || []),
      ...(provider.data.features?.map(f => f.name) || []),
      ...supportChannels
    ];

    return {
      id: provider.data.slug,
      name: provider.data.name,
      displayName: provider.data.displayName,
      slug: provider.data.slug,
      categories: provider.data.categories,
      locations: provider.data.datacenters?.map(dc => dc.location.toLowerCase()) || [],
      features,
      startingPrice: provider.data.pricing?.startingPrice || 0,
      formattedPrice: `Mulai dari Rp ${(provider.data.pricing?.startingPrice || 0).toLocaleString('id-ID')}/bulan`,
      url: `/direktori/hosting/${provider.data.slug}/`,
      description: provider.data.description || '',
      logo: provider.data.logo || '',
      type: 'provider' as const
    };
  });

  // Process categories
  const searchCategories: SearchCategory[] = categories.map(category => {
    const providerCount = providers.filter(provider => {
      // Cek di array 'categories' utama
      const inCategories = provider.data.categories.includes(category.id);
      
      // Cek di array 'controlPanels', buat pencocokan lebih fleksibel
      // Misal: category.id 'cpanel-hosting' akan cocok dengan controlPanel 'cpanel'
      const inControlPanels = provider.data.controlPanels?.some(panel => 
        category.id.toLowerCase().includes(panel.toLowerCase())
      ) || false;

      return inCategories || inControlPanels;
    }).length;

    return {
      id: category.id,
      name: category.data.title,
      slug: category.id,
      description: category.data.description || '',
      url: `/direktori/${category.id}/`,
      providerCount,
      featured: category.data.featured || false,
      type: 'category' as const
    };
  });

  // Process blog posts - separate reviews from regular pages
  const allBlogPages = blogPosts.map(post => {
    // Generate slug from file path
    const slug = post.id.replace(/\.(md|mdx)$/, '');
    return {
      id: post.id,
      title: post.data.title,
      slug: slug,
      description: post.data.excerpt || '',
      url: `/${slug}/`,  
      content: post.body || '',
      category: post.data.category || '',
      rawPost: post
    };
  });

  // Separate reviews from regular pages
  const reviewPosts = allBlogPages.filter(post => post.category === 'Review Hosting');
  const regularPages = allBlogPages.filter(post => post.category !== 'Review Hosting');

  // Create SearchReview objects
  const searchReviews: SearchReview[] = reviewPosts.map(post => ({
    id: post.id,
    title: post.title,
    slug: post.slug,
    description: post.description,
    url: post.url,
    content: post.content,
    type: 'review' as const
  }));

  // Create SearchPage objects for regular posts
  const blogPages: SearchPage[] = regularPages.map(post => ({
    id: post.id,
    title: post.title,
    slug: post.slug,
    description: post.description,
    url: post.url,
    content: post.content,
    type: 'page' as const
  }));

  const searchPages: SearchPage[] = [
    ...blogPages
  ];

  return {
    providers: searchProviders,
    categories: searchCategories,
    reviews: searchReviews,
    pages: searchPages,
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Get search suggestions based on query
 */
export function getSearchSuggestions(query: string, searchIndex: SearchIndex): string[] {
  if (!query || query.length < 2) return [];

  const suggestions = new Set<string>();
  const lowerQuery = query.toLowerCase();

  // Add provider names
  searchIndex.providers.forEach(provider => {
    if (provider.name.toLowerCase().includes(lowerQuery)) {
      suggestions.add(provider.name);
    }
    if (provider.displayName?.toLowerCase().includes(lowerQuery)) {
      suggestions.add(provider.displayName);
    }
  });

  // Add category names
  searchIndex.categories.forEach(category => {
    if (category.name.toLowerCase().includes(lowerQuery)) {
      suggestions.add(category.name);
    }
  });

  // Add common search terms
  const commonTerms = [
    'shared hosting', 'vps hosting', 'cloud hosting', 'wordpress hosting',
    'dedicated server', 'reseller hosting', 'indonesia', 'singapore',
    'murah', 'cpanel', 'plesk', 'directadmin'
  ];

  commonTerms.forEach(term => {
    if (term.includes(lowerQuery)) {
      suggestions.add(term);
    }
  });

  return Array.from(suggestions).slice(0, 6);
}
