import type { <PERSON>Index, SearchProvider, SearchCategory, SearchPage, SearchReview } from './search-index';

export interface SearchState {
  isOpen: boolean;
  query: string;
  selectedIndex: number;
  results: (SearchProvider | SearchCategory | SearchReview | SearchPage)[];
}

export interface SearchElements {
  overlay: HTMLElement;
  input: HTMLInputElement;
  closeBtn: HTMLElement | null;
  backdrop: HTMLElement | null;
  resultsContainer: HTMLElement | null;
}

// Global search state
export const searchState: SearchState = {
  isOpen: false,
  query: '',
  selectedIndex: -1,
  results: []
};

// Global search index
export let searchIndex: SearchIndex | null = null;

// Search timeout for debouncing
export let searchTimeout: number | null = null;

/**
 * Load search index from API with caching optimization
 */
export async function loadSearchIndex(): Promise<void> {
  if (searchIndex) return; // Already loaded
  
  try {
    // Use cache-first strategy for better performance
    const timestamp = new Date().getTime();
    const response = await fetch(`/search-index.json?t=${timestamp}`, {
      // Add cache control for better performance
      headers: {
        'Cache-Control': 'public, max-age=300' // 5 minutes cache
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load search index: ${response.status}`);
    }
    
    const data = await response.json();
    // Remove the timestamp before storing
    delete data._timestamp;
    searchIndex = data;
    
    // Optional: Store in sessionStorage for faster subsequent loads
    try {
      sessionStorage.setItem('search-index', JSON.stringify(searchIndex));
    } catch {
      // Ignore storage errors (e.g., quota exceeded)
    }
  } catch (error) {
    console.warn('Search index not found. Search functionality will be limited.', error);
    
    // Try to load from sessionStorage as fallback
    try {
      const cached = sessionStorage.getItem('search-index');
      if (cached) {
        searchIndex = JSON.parse(cached);
      }
    } catch {
      // Ignore cache errors
    }
  }
}

/**
 * Get all required DOM elements for search
 */
export function getSearchElements(): SearchElements | null {
  const overlay = document.getElementById('search-overlay');
  const input = document.querySelector('[data-search-input]') as HTMLInputElement;
  const closeBtn = document.querySelector('[data-search-close]') as HTMLElement | null;
  const backdrop = document.querySelector('[data-search-backdrop]') as HTMLElement | null;
  const resultsContainer = document.querySelector('[data-search-results-container]') as HTMLElement | null;

  if (!overlay || !input) {
    console.warn('Search overlay elements not found');
    return null;
  }

  return {
    overlay,
    input,
    closeBtn,
    backdrop,
    resultsContainer
  };
}

/**
 * Reset search state to initial values
 */
export function resetSearchState(): void {
  searchState.isOpen = false;
  searchState.query = '';
  searchState.selectedIndex = -1;
  searchState.results = [];
}

/**
 * Clear search timeout
 */
export function clearSearchTimeout(): void {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
    searchTimeout = null;
  }
}

/**
 * Set search timeout with debouncing
 */
export function setSearchTimeout(callback: () => void, delay: number = 150): void {
  clearSearchTimeout();
  searchTimeout = window.setTimeout(callback, delay);
}
