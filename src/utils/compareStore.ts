import { signal } from '@preact/signals';

// Types
export interface CompareState {
  items: string[]; // provider slugs
  updatedAt: number;
}

const LOCAL_STORAGE_KEY = 'ph_compare_providers_v1';
const MAX_ITEMS = 4;

// Internal signal state
const compareState = signal<CompareState>({ items: [], updatedAt: Date.now() });

// Helpers
function save() {
  try {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(compareState.value));
  } catch {
    // ignore quota / privacy errors
  }
}

function load(): CompareState | null {
  try {
    const raw = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (!raw) return null;
    const parsed = JSON.parse(raw);
    if (!parsed || !Array.isArray(parsed.items)) return null;
    return { items: parsed.items.slice(0, MAX_ITEMS), updatedAt: parsed.updatedAt || Date.now() };
  } catch {
    return null;
  }
}

// Public API
export function initCompareStore() {
  if (typeof window === 'undefined') return;
  const existing = load();
  if (existing) {
    compareState.value = existing;
  } else {
    save();
  }
}

export function getCompareItems() {
  return compareState.value.items;
}

export function isCompared(slug: string) {
  return compareState.value.items.includes(slug);
}

export function toggleCompare(slug: string): { added: boolean; reason?: string } {
  const items = [...compareState.value.items];
  const idx = items.indexOf(slug);
  if (idx >= 0) {
    items.splice(idx, 1);
    compareState.value = { items, updatedAt: Date.now() };
    save();
    dispatchEvent(new CustomEvent('compare:removed', { detail: { slug, items } }));
    dispatchEvent(new CustomEvent('compare:change', { detail: { items } }));
    return { added: false };
  }
  if (items.length >= MAX_ITEMS) {
    dispatchEvent(new CustomEvent('compare:limit', { detail: { max: MAX_ITEMS } }));
    return { added: false, reason: 'limit' };
  }
  items.push(slug);
  compareState.value = { items, updatedAt: Date.now() };
  save();
  dispatchEvent(new CustomEvent('compare:added', { detail: { slug, items } }));
  dispatchEvent(new CustomEvent('compare:change', { detail: { items } }));
  return { added: true };
}

export function clearCompare() {
  compareState.value = { items: [], updatedAt: Date.now() };
  save();
  dispatchEvent(new CustomEvent('compare:cleared'));
  dispatchEvent(new CustomEvent('compare:change', { detail: { items: [] } }));
}

export function onCompareChange(cb: (items: string[]) => void) {
  function handler(e: Event) {
    const detail = (e as CustomEvent).detail;
    cb(detail.items);
  }
  window.addEventListener('compare:change', handler as EventListener);
  return () => window.removeEventListener('compare:change', handler as EventListener);
}

export function onCompareLimit(cb: (max: number) => void) {
  function handler(e: Event) {
    const detail = (e as CustomEvent).detail;
    cb(detail.max);
  }
  window.addEventListener('compare:limit', handler as EventListener);
  return () => window.removeEventListener('compare:limit', handler as EventListener);
}

export function setCompareItems(list: string[]) {
  const unique = Array.from(new Set(list)).slice(0, 4).filter(Boolean);
  compareState.value = { items: unique, updatedAt: Date.now() };
  save();
  dispatchEvent(new CustomEvent('compare:change', { detail: { items: unique } }));
}
