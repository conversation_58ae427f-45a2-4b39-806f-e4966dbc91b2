import type { CollectionEntry } from 'astro:content';

export interface LocationData {
  name: string;           // "Jakarta"
  slug: string;           // "jakarta" 
  country: string;        // "Indonesia"
  flag: string;           // "🇮🇩"
  providerCount: number;  // 15
  providers: CollectionEntry<'hosting-providers'>[];
}

/**
 * Convert location name to URL-friendly slug
 */
export function getLocationSlug(location: string): string {
  return location
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

/**
 * Extract all unique locations from providers
 */
export function getAllLocations(providers: CollectionEntry<'hosting-providers'>[]): LocationData[] {
  const locationMap = new Map<string, LocationData>();

  providers.forEach(provider => {
    if (provider.data.datacenters && provider.data.datacenters.length > 0) {
      provider.data.datacenters.forEach(datacenter => {
        const slug = getLocationSlug(datacenter.location);
        
        if (locationMap.has(slug)) {
          const existing = locationMap.get(slug)!;
          existing.providerCount++;
          existing.providers.push(provider);
        } else {
          locationMap.set(slug, {
            name: datacenter.location,
            slug: slug,
            country: datacenter.country,
            flag: datacenter.flag,
            providerCount: 1,
            providers: [provider]
          });
        }
      });
    }
  });

  // Sort by provider count (descending) then by name
  return Array.from(locationMap.values()).sort((a, b) => {
    if (b.providerCount !== a.providerCount) {
      return b.providerCount - a.providerCount;
    }
    return a.name.localeCompare(b.name);
  });
}

/**
 * Get providers that have data center in specific location
 */
export function getProvidersByLocation(
  providers: CollectionEntry<'hosting-providers'>[], 
  locationSlug: string
): CollectionEntry<'hosting-providers'>[] {
  return providers.filter(provider => {
    if (!provider.data.datacenters || provider.data.datacenters.length === 0) {
      return false;
    }
    
    return provider.data.datacenters.some(datacenter => 
      getLocationSlug(datacenter.location) === locationSlug
    );
  });
}

/**
 * Get location data by slug
 */
export function getLocationBySlug(
  providers: CollectionEntry<'hosting-providers'>[], 
  locationSlug: string
): LocationData | null {
  const allLocations = getAllLocations(providers);
  return allLocations.find(location => location.slug === locationSlug) || null;
}

/**
 * Generate location paths for static generation
 */
export function getLocationPaths(providers: CollectionEntry<'hosting-providers'>[]): string[] {
  const locations = getAllLocations(providers);
  return locations.map(location => location.slug);
}

/**
 * Get popular locations (top 6 by provider count)
 */
export function getPopularLocations(providers: CollectionEntry<'hosting-providers'>[]): LocationData[] {
  const allLocations = getAllLocations(providers);
  return allLocations.slice(0, 6);
}

/**
 * Format location display name with country
 */
export function formatLocationDisplay(location: LocationData): string {
  return `${location.name}, ${location.country}`;
}

/**
 * Get location statistics
 */
export function getLocationStats(providers: CollectionEntry<'hosting-providers'>[]): {
  totalLocations: number;
  totalProviders: number;
  topLocation: LocationData | null;
} {
  const locations = getAllLocations(providers);
  const totalProviders = providers.filter(p => p.data.datacenters && p.data.datacenters.length > 0).length;
  
  return {
    totalLocations: locations.length,
    totalProviders,
    topLocation: locations.length > 0 ? locations[0] : null
  };
}
