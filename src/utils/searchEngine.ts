import type { SearchProvider, SearchCategory, SearchPage, SearchReview } from './search-index';
import { searchIndex, searchState } from './searchState';

/**
 * Perform search across all content types with category priority
 */
export function performSearch(query: string): (SearchProvider | SearchCategory | SearchReview | SearchPage)[] {
  if (!searchIndex) {
    return [];
  }

  // Enhanced search implementation with category priority
  const categoryResults: (SearchCategory & { relevanceScore: number })[] = [];
  const providerResults: (SearchProvider & { relevanceScore: number })[] = [];
  const reviewResults: (SearchReview & { relevanceScore: number })[] = [];
  const pageResults: (SearchPage & { relevanceScore: number })[] = [];
  const lowerQuery = query.toLowerCase().trim();

  // Daftar stop words umum dalam Bahasa Indonesia
  const stopWords = new Set(['apa', 'itu', 'dan', 'di', 'ke', 'dari', 'yang', 'untuk', 'dengan', 'adalah', 'cara', 'bagaimana', 'mengapa']);

  // Pisahkan query menjadi kata-kata dan filter stop words
  const queryWords = lowerQuery.split(/\s+/).filter(word => word.length > 2 && !stopWords.has(word));

  // Jika query hanya berisi stop words atau terlalu pendek, jangan cari provider
  const isGenericQuery = queryWords.length === 0 && lowerQuery.length > 2;

  const calculateScore = (text: string, weight: number): number => {
    const lowerText = text.toLowerCase();
    let score = 0;
    if (queryWords.length > 0) {
      // Prioritaskan kecocokan semua kata kunci
      const allWordsMatch = queryWords.every(word => lowerText.includes(word));
      if (allWordsMatch) {
        score = weight;
      }
    } else {
      // Jika tidak ada kata kunci (hanya stop words), cocokkan frasa asli
      if (lowerText.includes(lowerQuery)) {
        score = weight;
      }
    }
    return score;
  };

  // Search categories FIRST (highest priority for exact matches)
  searchIndex.categories?.forEach((category: SearchCategory) => {
    let relevanceScore = 0;
    
    // Exact match in category name gets highest score
    if (category.name.toLowerCase() === lowerQuery) {
      relevanceScore = 100;
    } else if (category.name.toLowerCase().includes(lowerQuery)) {
      relevanceScore = 90;
    } else if (category.description.toLowerCase().includes(lowerQuery)) {
      relevanceScore = 70;
    }
    
    if (relevanceScore > 0) {
      categoryResults.push({ ...category, relevanceScore });
    }
  });

  // Search reviews (priority after categories, before providers)
  searchIndex.reviews?.forEach((review: SearchReview) => {
    let relevanceScore = 0;
    
    // Reviews get higher priority than regular providers
    relevanceScore = Math.max(
      calculateScore(review.title, 100), // Judul review (bobot tertinggi)
      calculateScore(review.description || '', 80), // Deskripsi
      calculateScore(review.content || '', 60) // Konten (bobot terendah)
    );
    
    if (relevanceScore > 0) {
      reviewResults.push({ ...review, relevanceScore });
    }
  });

  // Search providers (hanya jika query tidak generik)
  if (!isGenericQuery) {
    searchIndex.providers?.forEach((provider: SearchProvider) => {
      let relevanceScore = 0;

      // Skor berdasarkan nama, kategori, dan fitur. HINDARI deskripsi.
      relevanceScore = Math.max(
        calculateScore(provider.name, 100), // Nama provider (bobot tertinggi)
        ...provider.categories.map(cat => calculateScore(cat, 80)), // Kategori
        ...provider.features.map(feat => calculateScore(feat, 70)) // Fitur
      );

      if (relevanceScore > 0) {
        if (provider.featured) {
          relevanceScore += 5; // Tambahan skor untuk provider unggulan
        }
        relevanceScore += Math.random(); // Sedikit acak untuk urutan
        providerResults.push({ ...provider, relevanceScore });
      }
    });
  }

  // Search pages (lowest priority)
  searchIndex.pages?.forEach((page: SearchPage) => {
    let relevanceScore = 0;
    
    // Untuk halaman, kita ingin mencocokkan frasa "apa itu" secara langsung.
    relevanceScore = Math.max(
      calculateScore(page.title, 100), // Judul (bobot tertinggi)
      calculateScore(page.description || '', 80), // Deskripsi
      calculateScore(page.content || '', 60) // Konten (bobot terendah)
    );
    
    if (relevanceScore > 0) {
      pageResults.push({ ...page, relevanceScore });
    }
  });

  // Sort by relevance and combine results with new priority (categories > reviews > providers > pages)
  const sortedCategories = categoryResults.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  const sortedReviews = reviewResults.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  const sortedProviders = providerResults.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  const sortedPages = pageResults.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));

  // Combine results: Categories first, then reviews, then providers, then pages
  const combinedResults = [
    ...sortedCategories.slice(0, 3),  // Max 3 categories
    ...sortedReviews.slice(0, 5),     // Max 5 reviews (increase a bit as priority)
    ...sortedProviders.slice(0, 5),   // Max 5 providers
    ...sortedPages.slice(0, 4)        // Max 4 pages
  ];

  return combinedResults.slice(0, 12) as (SearchProvider | SearchCategory | SearchReview | SearchPage)[];
}

/**
 * Execute search with debouncing and update state
 */
export function executeSearch(query: string): void {
  const results = performSearch(query);
  searchState.results = results;
  searchState.selectedIndex = -1;
}

/**
 * Clear search results
 */
export function clearSearchResults(): void {
  searchState.results = [];
  searchState.selectedIndex = -1;
}

/**
 * Navigate through search results
 */
export function navigateResults(direction: number): void {
  const resultItems = document.querySelectorAll('[data-result-index]');
  if (resultItems.length === 0) return;

  // Remove current selection
  resultItems[searchState.selectedIndex]?.classList.remove('bg-primary/10');

  // Update selection
  searchState.selectedIndex += direction;
  
  if (searchState.selectedIndex >= resultItems.length) {
    searchState.selectedIndex = 0;
  } else if (searchState.selectedIndex < 0) {
    searchState.selectedIndex = resultItems.length - 1;
  }

  // Add new selection
  const selectedItem = resultItems[searchState.selectedIndex];
  selectedItem?.classList.add('bg-primary/10');
  selectedItem?.scrollIntoView({ block: 'nearest' });
}

/**
 * Select current result and navigate to it
 */
export function selectCurrentResult(): void {
  const resultItems = document.querySelectorAll('[data-result-index]');
  const selectedItem = resultItems[searchState.selectedIndex] as HTMLAnchorElement;
  
  if (selectedItem) {
    // Close search modal first using custom event
    document.dispatchEvent(new CustomEvent('search:close'));
    
    // Wait for animation to complete before navigating
    setTimeout(() => {
      // Use consistent navigation logic
      if (typeof window !== 'undefined' && 'astro' in window) {
        // Use Astro's router for better View Transitions
        window.location.href = selectedItem.href;
      } else {
        // Fallback
        window.location.href = selectedItem.href;
      }
    }, 300); // Match animation duration
  }
}
