import type { StatusType, BadgeColorType } from '~/types';

/**
 * Get CSS color class based on status type
 */
export const getStatusColor = (status: StatusType): string => {
  switch (status) {
    case 'excellent': return 'text-green-600';
    case 'good': return 'text-green-500';
    case 'stable': return 'text-blue-500';
    case 'ok': return 'text-yellow-600';
    case 'poor': return 'text-red-500';
    case 'failed': return 'text-red-600';
    case 'fastest': return 'text-green-600';
    case 'fast': return 'text-green-500';
    case 'slower': return 'text-yellow-600';
    case 'slowest': return 'text-red-500';
    case 'best': return 'text-green-600';
    case 'slow': return 'text-red-500';
    case 'lowest': return 'text-green-600';
    case 'budget': return 'text-green-500';
    case 'fair': return 'text-blue-500';
    case 'cheap': return 'text-green-500';
    case 'higher': return 'text-red-500';
    case 'none': return 'text-red-500';
    case 'promoted': return 'text-orange-600 dark:text-orange-400';
    default: return 'text-gray-600';
  }
};

/**
 * Get CSS badge color classes based on badge color type
 */
export const getBadgeColor = (color: BadgeColorType): string => {
  switch (color) {
    case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'orange': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'red': return 'bg-red-100 text-red-800 border-red-200';
    case 'yellow': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'green': return 'bg-green-100 text-green-800 border-green-200';
    case 'purple': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'promoted': return 'bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/50 dark:text-orange-300';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Get rank icon emoji for top 3 positions
 */
export const getRankIcon = (rank: number): string | null => {
  if (rank === 1) return '🏆';
  if (rank === 2) return '🥈';
  if (rank === 3) return '🥉';
  return null; // For ranks 4+, we'll just show the number
};

/**
 * Get status description text based on status type
 */
export const getStatusDescription = (status: StatusType, type: 'uptime' | 'loadTime' | 'support' | 'price'): string => {
  switch (type) {
    case 'uptime':
      switch (status) {
        case 'excellent': return '✓ Stabil';
        case 'stable': return 'Sangat Stabil';
        case 'good': return 'Baik';
        case 'ok': return 'OK';
        case 'poor': return 'Buruk';
        default: return '';
      }
    
    case 'loadTime':
      switch (status) {
        case 'fastest': return '✓ Tercepat';
        case 'fast': return '✓ Cepat';
        case 'ok': return '✓ OK';
        case 'slower': return '⚠ Lambat';
        case 'slowest': return '⚠ Paling Lambat';
        case 'failed': return '✗ Gagal';
        default: return '';
      }
    
    case 'support':
      switch (status) {
        case 'best': return '✓ Tercepat';
        case 'good': return '✓ Cepat';
        case 'ok': return '✓ Cukup Cepat';
        case 'slow': return '⚠ Lambat';
        case 'slowest': return '⚠ Sangat Lambat';
        case 'poor': return '✗ Terbatas';
        default: return '';
      }
    
    case 'price':
      switch (status) {
        case 'lowest': return '💰 Termurah';
        case 'budget': return '💰 Budget Friendly';
        case 'fair': return '💰 Fair Value';
        case 'cheap': return '💰 Affordable';
        case 'higher': return '💰 Lebih Mahal';
        default: return '';
      }
    
    default:
      return '';
  }
};