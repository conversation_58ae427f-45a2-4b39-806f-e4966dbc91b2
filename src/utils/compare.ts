import type { CollectionEntry } from 'astro:content';

// Types for normalized output
export interface ComparableProvider {
  slug: string;
  name: string;
  displayName?: string;
  logo?: string;
  startingPrice?: number;
  promoDescription?: string;
  promoCode?: string;
  features: string[];
  datacenters: { flag?: string; location: string }[];
  headquarters?: string;
  founded?: string | number;
  support: { key: string; label: string; icon: string }[];
  controlPanels: { key: string; label: string; icon: string }[];
  badges: { type: string; label: string }[];
  affiliateLink?: string;
  website?: string;
}

const SUPPORT_ICON_MAP: Record<string, { label: string; icon: string }> = {
  'live-chat': { label: 'Live Chat', icon: '💬' },
  ticket: { label: 'Ticket', icon: '🎫' },
  phone: { label: 'Phone', icon: '📞' },
  email: { label: 'Email', icon: '✉️' },
  documentation: { label: 'Docs', icon: '📚' },
  community: { label: 'Community', icon: '👥' },
  whatsapp: { label: 'WhatsApp', icon: '🟢' }
};

const CONTROL_PANEL_ICON_MAP: Record<string, { label: string; icon: string }> = {
  cpanel: { label: 'cPanel', icon: '🛠️' },
  directadmin: { label: 'DirectAdmin', icon: '🧩' },
  plesk: { label: 'Plesk', icon: '📋' }
};

export function formatSupportChannels(channels: string[] = []) {
  return channels
    .map(c => ({ key: c, ...(SUPPORT_ICON_MAP[c] || { label: c, icon: '❔' }) }))
    .filter(Boolean);
}

export function formatControlPanels(panels: string[] = []) {
  return panels
    .map(p => ({ key: p, ...(CONTROL_PANEL_ICON_MAP[p] || { label: p, icon: '⚙️' }) }))
    .filter(Boolean);
}

export function pickComparableFields(provider: CollectionEntry<'hosting-providers'>): ComparableProvider {
  const p = provider.data;
  return {
    slug: p.slug,
    name: p.name,
    displayName: p.displayName,
    logo: p.logo,
    startingPrice: p.pricing?.startingPrice,
    promoDescription: p.pricing?.promoDescription,
    promoCode: p.pricing?.promoCode,
    features: (p.features || []).slice(0, 6).map((f: string | { name: string; description?: string }) => typeof f === 'string' ? f : f.name).filter(Boolean),
    datacenters: (p.datacenters || []).slice(0, 12).map((d: { location?: string; country?: string; flag?: string }) => ({ location: d.location || d.country || '', flag: d.flag })),
    headquarters: p.company?.headquarters?.[0],
    founded: p.company?.founded,
    support: formatSupportChannels(p.company?.supportChannels || []),
    controlPanels: formatControlPanels(p.controlPanels || []),
    badges: p.badges || [],
    affiliateLink: p.affiliateLink,
    website: p.website
  };
}
