import { getCollection } from 'astro:content';

export interface CategoryStats {
  [key: string]: number;
}

export interface LocationStats {
  [key: string]: number;
}

/**
 * Get provider count by category
 */
export async function getProviderCountByCategory(): Promise<CategoryStats> {
  const providers = await getCollection('hosting-providers');
  const activeProviders = providers.filter(provider => provider.data.isActive !== false);
  
  const categoryStats: CategoryStats = {};
  
  activeProviders.forEach(provider => {
    if (provider.data.categories && provider.data.categories.length > 0) {
      provider.data.categories.forEach(category => {
        categoryStats[category] = (categoryStats[category] || 0) + 1;
      });
    }
  });
  
  return categoryStats;
}

/**
 * Get provider count by location
 */
export async function getProviderCountByLocation(): Promise<LocationStats> {
  const providers = await getCollection('hosting-providers');
  const activeProviders = providers.filter(provider => provider.data.isActive !== false);
  
  const locationStats: LocationStats = {};
  
  activeProviders.forEach(provider => {
    if (provider.data.datacenters && provider.data.datacenters.length > 0) {
      provider.data.datacenters.forEach(datacenter => {
        const locationSlug = datacenter.location.toLowerCase().replace(/\s+/g, '-');
        locationStats[locationSlug] = (locationStats[locationSlug] || 0) + 1;
      });
    }
  });
  
  return locationStats;
}

/**
 * Get total active provider count
 */
export async function getTotalProviderCount(): Promise<number> {
  const providers = await getCollection('hosting-providers');
  return providers.filter(provider => provider.data.isActive !== false).length;
}

/**
 * Format provider count for display
 */
export function formatProviderCount(count: number): string {
  if (count === 0) return '0 provider tersedia';
  if (count === 1) return '1 provider tersedia';
  return `${count} provider tersedia`;
}

/**
 * Format total provider count for mega menu
 */
export function formatTotalProviderCount(count: number): string {
  if (count < 10) return `${count} provider terpercaya`;
  if (count < 50) return `${count}+ provider terpercaya`;
  if (count < 100) return `${Math.floor(count / 10) * 10}+ provider terpercaya`;
  return `${Math.floor(count / 10) * 10}+ provider terpercaya`;
}
