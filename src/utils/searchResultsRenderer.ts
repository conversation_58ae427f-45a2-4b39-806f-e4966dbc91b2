import type { Search<PERSON>rovider, SearchCategory, SearchPage, SearchReview } from './search-index';
import { searchState } from './searchState';

/**
 * Render search results to HTML
 */
export function renderSearchResults(resultsContainer: HTMLElement): void {
  if (!resultsContainer) return;

  if (searchState.results.length === 0) {
    renderNoResultsMessage(resultsContainer);
    return;
  }

  const groupedResults = {
    categories: searchState.results.filter(r => r.type === 'category') as SearchCategory[],
    providers: searchState.results.filter(r => r.type === 'provider') as SearchProvider[],
    reviews: searchState.results.filter(r => r.type === 'review') as SearchReview[],
    pages: searchState.results.filter(r => r.type === 'page') as SearchPage[]
  };

  let html = '';

  // Categories section FIRST (highest priority)
  if (groupedResults.categories.length > 0) {
    html += renderCategoriesSection(groupedResults.categories);
  }

  // Reviews section SECOND (now before providers)
  if (groupedResults.reviews.length > 0) {
    html += renderReviewsSection(groupedResults.reviews);
  }

  // Providers (Direktori) section THIRD
  if (groupedResults.providers.length > 0) {
    html += renderProvidersSection(groupedResults.providers);
  }

  // Pages section FOURTH
  if (groupedResults.pages.length > 0) {
    html += renderPagesSection(groupedResults.pages);
  }

  resultsContainer.innerHTML = html;
}

/**
 * Render categories section
 */
function renderCategoriesSection(categories: SearchCategory[]): string {
  return `
    <div class="p-2 border-b border-gray-100 dark:border-gray-600">
      <h3 class="text-xs font-semibold text-primary uppercase tracking-wide px-2 py-1 flex items-center">
        <span class="w-2 h-2 bg-primary rounded-full mr-2"></span>
        Kategori Hosting
      </h3>
      ${categories.map((category) => `
        <a href="${category.url}" 
           class="search-result-item flex items-center p-3 rounded-md hover:bg-bg-muted hover:no-underline transition-colors border border-transparent hover:border-primary/20"
           data-result-index="${searchState.results.indexOf(category)}">
          <div class="w-10 h-10 bg-gradient-to-br from-primary/10 to-primary/20 rounded-md flex items-center justify-center mr-3">
            <span class="text-primary font-bold text-sm">${category.name.charAt(0).toUpperCase()}</span>
          </div>
          <div class="flex-1 min-w-0">
            <div class="font-semibold text-sm text-heading">${category.name}</div>
            <div class="text-xs text-muted">${category.providerCount} provider tersedia</div>
          </div>
          <div class="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
            Kategori
          </div>
        </a>
      `).join('')}
    </div>
  `;
}

/**
 * Render providers section
 */
function renderProvidersSection(providers: SearchProvider[]): string {
  return `
    <div class="p-2">
      <h3 class="text-xs font-semibold text-muted uppercase tracking-wide px-2 py-1">Direktori Hosting</h3>
      ${providers.map((provider) => `
        <a href="${provider.url}" 
           class="search-result-item flex items-center p-2 rounded-md hover:bg-bg-muted hover:no-underline transition-colors"
           data-result-index="${searchState.results.indexOf(provider)}">
          <img src="${provider.logo}" alt="${provider.name}" class="w-8 h-8 rounded object-contain mr-3" />
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm text-heading">${provider.name}</div>
            <div class="text-xs text-muted truncate">${provider.formattedPrice}</div>
          </div>
          <div class="text-xs bg-gray-100 dark:bg-gray-700 text-muted px-2 py-1 rounded-full">
            Direktori
          </div>
        </a>
      `).join('')}
    </div>
  `;
}

/**
 * Render reviews section
 */
function renderReviewsSection(reviews: SearchReview[]): string {
  return `
    <div class="p-2 border-b border-gray-100 dark:border-gray-600">
      <h3 class="text-xs font-semibold text-muted uppercase tracking-wide px-2 py-1">Review Hosting</h3>
      ${reviews.map((review) => `
        <a href="${review.url}" 
           class="search-result-item flex items-center p-2 rounded-md hover:bg-bg-muted hover:no-underline transition-colors"
           data-result-index="${searchState.results.indexOf(review)}">
          <div class="w-8 h-8 bg-muted/10 rounded-md flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-muted" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm text-heading">${review.title}</div>
            <div class="text-xs text-muted truncate">${review.description}</div>
          </div>
          <div class="text-xs bg-gray-100 dark:bg-gray-700 text-muted px-2 py-1 rounded-full">
            Review
          </div>
        </a>
      `).join('')}
    </div>
  `;
}

/**
 * Render pages section
 */
function renderPagesSection(pages: SearchPage[]): string {
  return `
    <div class="p-2">
      <h3 class="text-xs font-semibold text-muted uppercase tracking-wide px-2 py-1">Halaman</h3>
      ${pages.map((page) => `
        <a href="${page.url}" 
           class="search-result-item flex items-center p-2 rounded-md hover:bg-bg-muted hover:no-underline transition-colors"
           data-result-index="${searchState.results.indexOf(page)}">
          <div class="w-8 h-8 bg-muted/10 rounded-md flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm text-heading">${page.title}</div>
            <div class="text-xs text-muted truncate">${page.description}</div>
          </div>
        </a>
      `).join('')}
    </div>
  `;
}

/**
 * Render no results message
 */
function renderNoResultsMessage(resultsContainer: HTMLElement): void {
  resultsContainer.innerHTML = `
    <div class="p-8 text-center">
      <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
      </div>
      <h3 class="text-sm font-medium text-heading mb-1">Tidak ada hasil ditemukan</h3>
      <p class="text-xs text-muted">Coba kata kunci yang berbeda atau lebih spesifik</p>
    </div>
  `;
}

/**
 * Clear results display
 */
export function clearResultsDisplay(resultsContainer: HTMLElement | null): void {
  if (resultsContainer) {
    resultsContainer.innerHTML = '';
  }
}
