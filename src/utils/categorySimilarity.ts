import { getCollection } from 'astro:content';

// Internal cache so we compute similarity only once per build
let initialized = false as boolean;
let categoryToProviders = new Map<string, Set<string>>();
let providerToCategories = new Map<string, Set<string>>();
let categorySet = new Set<string>();

// Map control panel values to category slugs used on pages
const CONTROL_PANEL_CATEGORY: Record<string, string> = {
  cpanel: 'cpanel-hosting',
  directadmin: 'directadmin-hosting',
  plesk: 'plesk-hosting',
};

// Families and upgrade paths for bonus scoring
const FAMILY: Record<string, string> = {
  'unmanaged-vps': 'vps',
  'managed-vps': 'vps',
  'vps-windows': 'vps',
  'cloud-hosting': 'cloud',
  'shared-hosting': 'shared',
  'wordpress-hosting': 'wordpress',
  'dedicated-server': 'dedicated',
  'bare-metal-server': 'dedicated',
  'colocation-server': 'dedicated',
  'cpanel-hosting': 'panel',
  'directadmin-hosting': 'panel',
  'plesk-hosting': 'panel',
  'reseller-hosting': 'reseller',
  'domain': 'domain',
  'reseller-domain': 'domain',
  'email-hosting': 'email',
};

const UPGRADE_NEIGHBORS: Record<string, string[]> = {
  'shared-hosting': ['cloud-hosting', 'unmanaged-vps', 'wordpress-hosting'],
  'cloud-hosting': ['unmanaged-vps', 'managed-vps', 'dedicated-server'],
  'unmanaged-vps': ['managed-vps', 'vps-windows', 'dedicated-server', 'cloud-hosting'],
  'managed-vps': ['unmanaged-vps', 'cloud-hosting', 'dedicated-server'],
  'vps-windows': ['unmanaged-vps', 'managed-vps'],
  'dedicated-server': ['bare-metal-server', 'colocation-server', 'managed-vps'],
  'bare-metal-server': ['dedicated-server', 'colocation-server'],
  'colocation-server': ['dedicated-server', 'bare-metal-server'],
  'wordpress-hosting': ['shared-hosting', 'managed-vps', 'cloud-hosting'],
  'cpanel-hosting': ['directadmin-hosting', 'plesk-hosting', 'shared-hosting'],
  'directadmin-hosting': ['cpanel-hosting', 'plesk-hosting', 'shared-hosting'],
  'plesk-hosting': ['cpanel-hosting', 'directadmin-hosting', 'shared-hosting'],
  'reseller-hosting': ['reseller-domain', 'domain', 'shared-hosting'],
  'reseller-domain': ['domain', 'reseller-hosting'],
  'domain': ['reseller-domain', 'reseller-hosting'],
  'email-hosting': ['shared-hosting', 'wordpress-hosting'],
};

async function ensureIndex() {
  if (initialized) return;

  const providers = await getCollection('hosting-providers');
  const categories = await getCollection('hosting-categories');

  categorySet = new Set(categories.map((c) => c.id));

  for (const p of providers) {
    if (p.data.isActive === false) continue;

    const pid = p.id;
    const cats = new Set<string>();

    // Regular categories
    for (const c of p.data.categories || []) {
      cats.add(c);
    }

    // Control panels mapped to category slugs
    for (const panel of p.data.controlPanels || []) {
      const mapped = CONTROL_PANEL_CATEGORY[panel];
      if (mapped) cats.add(mapped);
    }

    providerToCategories.set(pid, cats);

    // Fill category->providers map
    for (const c of cats) {
      if (!categoryToProviders.has(c)) categoryToProviders.set(c, new Set());
      categoryToProviders.get(c)!.add(pid);
    }
  }

  // Ensure all known categories exist in the map, even if empty
  for (const c of categorySet) {
    if (!categoryToProviders.has(c)) categoryToProviders.set(c, new Set());
  }

  initialized = true;
}

function jaccard(a: Set<string>, b: Set<string>): number {
  if (a.size === 0 && b.size === 0) return 0;
  let inter = 0;
  for (const x of a) if (b.has(x)) inter++;
  const union = a.size + b.size - inter;
  return union === 0 ? 0 : inter / union;
}

function sameFamily(a: string, b: string): boolean {
  return FAMILY[a] && FAMILY[a] === FAMILY[b];
}

function isUpgradeNeighbor(a: string, b: string): boolean {
  return (UPGRADE_NEIGHBORS[a] || []).includes(b);
}

export interface SimilarCategory {
  slug: string;
  score: number;
}

export async function getSimilarCategories(
  categorySlug: string,
  maxItems = 6,
  exclude: string[] = []
): Promise<SimilarCategory[]> {
  await ensureIndex();

  if (!categorySet.has(categorySlug)) return [];

  const baseProviders = categoryToProviders.get(categorySlug) || new Set();
  const excludeSet = new Set<string>([categorySlug, ...exclude]);

  const results: SimilarCategory[] = [];

  for (const other of categorySet) {
    if (excludeSet.has(other)) continue;

    const otherProviders = categoryToProviders.get(other) || new Set();
    let score = jaccard(baseProviders, otherProviders);

    // Bonus weights
    if (sameFamily(categorySlug, other)) score += 0.15;
    if (isUpgradeNeighbor(categorySlug, other)) score += 0.1;

    // Very small categories may be less useful; light de-boost
    if (otherProviders.size <= 2) score -= 0.05;

    if (score > 0) results.push({ slug: other, score });
  }

  // Sort by score desc and slice
  results.sort((a, b) => b.score - a.score);
  return results.slice(0, maxItems);
}
