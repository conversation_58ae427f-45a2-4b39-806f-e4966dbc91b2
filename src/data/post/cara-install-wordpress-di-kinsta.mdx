---
title: "Cara Install WordPress di Kinsta"
publishDate: 2024-09-30
updateDate: 2024-09-30
category: "Tutorial"
image: https://img.penasihathosting.com/2025/May/cara-install-wordpress-di-kinsta.webp
excerpt: "Panduan langkah demi langkah cara mudah menginstal WordPress di platform hosting Kinsta, termasuk memilih lokasi server dan mengisi detail penting."
metadata:
  title: "Panduan Lengkap: Cara Mudah Install WordPress di Kinsta"
  description: "Bingung cara instal WordPress di Kinsta? Ikuti panduan lengkap kami langkah demi langkah untuk setup yang cepat dan tepat, termasuk tips memilih lokasi server."
---

Se<PERSON>ar<PERSON>, menginstal WordPress di Kinsta itu mudah, bahkan lebih mudah dibandingkan menggunakan WordPress Manager atau WP Toolkit di cPanel.

Namun, jika Anda merasa kesulitan atau ingin memastikan pemilihan lokasi server website Anda tepat, mengingat banyaknya pilihan server yang tersedia di Kinsta, saya akan memandu Anda melalui prosesnya di bawah ini.

### 1\. Login ke MyKinsta

Pertama, jika Anda belum login ke MyKinsta Anda, silahkan kunjungi URL berikut: <a href="https://my.kinsta.com/login" rel="nofollow">https://my.kinsta.com/login</a>, kemudian masukkan email dan password Anda:

![Halaman login MyKinsta](https://img.penasihathosting.com/2025/May/1.%20Login%20ke%20MyKinsta.webp "Login to MyKinsta")

Selanjutnya, klik menu "WordPress Site" dan pilih tombol "Create a site".

![Tombol Create a site di dashboard MyKinsta](https://img.penasihathosting.com/2025/May/create%20a%20site.webp)

### 2\. Pilih Install WordPress

Anda akan melihat tiga pilihan: Install WordPress, Empty Environment (hanya berisi PHP tanpa WordPress), dan Clone (ditujukan untuk meng-clone environment yang sudah ada sebelumnya, misalnya untuk kebutuhan website staging/development).

Pilih opsi "Install WordPress" dan klik tombol "Continue".

![Pilihan instalasi WordPress di Kinsta](https://img.penasihathosting.com/2025/May/2.%20Pilih%20Install%20WordPress.webp)

### 3\. Isi Nama Website Anda dan Pilih Lokasi Server

Selanjutnya, Anda perlu mengisi bidang nama website (ini hanya akan muncul di MyKinsta, bukan di WordPress Anda).

Yang terpenting pada langkah ini adalah pemilihan lokasi server. Jika website Anda berbahasa Indonesia atau menargetkan pengunjung dari Indonesia, saya sarankan memilih lokasi server di Singapura, karena memiliki performa yang lebih tinggi. Perhatikan label "Boosted" pada server Singapura.

Jika website Anda menargetkan pengunjung internasional, saya merekomendasikan pemilihan server di AS.

Setelah memilih, klik tombol "Continue".

![Pengisian nama website dan pemilihan lokasi server di Kinsta](https://img.penasihathosting.com/2025/May/isi%20nama%20website.webp "Isi nama website dan pilih lokasi server")

### 4\. Isi Semua Detail

Pastikan Anda mengisi dengan teliti pada tahap ini dan jangan lupa menyimpan informasi username dan password Anda. Jika Anda lupa atau tidak menyimpannya, Anda mungkin perlu melakukan perubahan password melalui PHPMyAdmin (tidak ramah untuk pengguna pemula), melakukan reset password (jika SMTP email Anda telah diatur, yang kemungkinan besar belum), atau meminta bantuan support Kinsta melalui live chat.

Isi semua detail berikut:

- Judul WordPress Anda
- Username
- Password (pastikan menggunakan password yang kuat)
- Email admin
- Dan bahasa WordPress
- Optional, namun jika Anda membutuhkan plugin WooCommerce, Yoast SEO atau EDD, maka Anda bisa langsung menginstallnya diawal

Setelah selesai, klik tombol "Continue".

![Pengisian detail instalasi WordPress seperti judul, username, dan password](https://img.penasihathosting.com/2025/May/isi%20semua%20detail.webp)

Sistem akan menginstal WordPress Anda berdasarkan detail yang telah Anda isi. Pada tahap ini, Anda hanya perlu menunggu hingga prosesnya selesai, yang biasanya memakan waktu sekitar 5-10 menit. Anda bisa menutup halaman ini selama menunggu.

![Proses instalasi WordPress sedang berjalan](https://img.penasihathosting.com/2025/May/proses%20install.webp)

Setelah proses selesai, Anda akan menerima pemberitahuan melalui email dan akan melihat nama WordPress Anda dengan label "Live" di dashboard MyKinsta.

![Dashboard MyKinsta menampilkan situs WordPress yang sudah live](https://img.penasihathosting.com/2025/May/wordpress%20sites%20kinsta.webp) 