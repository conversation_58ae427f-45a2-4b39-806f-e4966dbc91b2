---
title: "Backup File Website di cPanel: Panduan <PERSON>gka<PERSON>"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "backup"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/backup-cpanel.webp
excerpt: "Cara melakukan backup dan restore file website di cPanel. Panduan lengkap untuk melindungi data website Anda dengan backup otomatis dan manual."
metadata:
  title: "Backup File Website di cPanel: Panduan Lengkap (Edisi 2025)"
  description: "Pelajari cara melakukan backup dan restore file website di cPanel, termasuk backup otomatis dan manual, dengan panduan lengkap dan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 7
  chapterTitle: "Backup File Website"
---

# Backup File Website di cPanel: Panduan Lengkap

Setelah Anda memahami cara [mengelola file website di cPanel](/file-manager-cpanel/), langkah penting selanjutnya adalah mempelajari cara melakukan backup. Backup adalah salah satu aspek terpenting dalam mengelola website karena melindungi data Anda dari kehilangan akibat kesalahan manusia, serangan malware, atau kegagalan server.

## Pentingnya Backup Website

Apabila Anda pernah kehilangan handphone, komputer/laptop, atau barang berharga lainnya, Anda pasti merasa kesal, sakit hati, galau atau apapun emosi yang Anda rasakan, tapi disamping kehilangan barang berharga, Anda pun kehilangan data penting di dalamnya yang mungkin belum Anda cadangkan.

Agar tragedi semacam itu tidak terjadi pada website Anda, cPanel telah membuat segalanya menjadi mudah dengan menyediakan fitur bakcup, sehingga Anda dapat mengembalikan file website Anda bilamana Anda kehilangan sebuah file atau seluruh isi file website Anda.

Beberapa alasan mengapa backup website sangat penting:

1. **Perlindungan dari kesalahan manusia** - Kesalahan saat mengedit file dapat merusak website
2. **Pemulihan dari serangan malware** - Memulihkan website ke keadaan sebelum terinfeksi
3. **Migrasi website** - Memudahkan proses pemindahan website ke hosting baru
4. **Pengujian perubahan** - Memungkinkan Anda menguji perubahan besar tanpa risiko
5. **Ketenangan pikiran** - Mengetahui data Anda aman jika terjadi masalah

## Jenis-jenis Backup di cPanel

Di cPanel, ada beberapa jenis backup yang dapat Anda lakukan:

1. **Full Backup** - Mencadangkan seluruh akun hosting, termasuk file, database, dan pengaturan email
2. **Partial Backup** - Mencadangkan komponen tertentu saja:
   - Home Directory (file website)
   - MySQL Databases (database)
   - Email Forwarders & Filters (pengaturan email)

## Backup Otomatis di cPanel

Nah, di cPanel sendiri, biasanya penyedia hosting memasukkan fitur backup otomatis, dimana website Anda akan di backup secara otomatis oleh sistem dan di simpan di server, jadi Anda tidak perlu melakukan backup secara manual dengan men download nya.

Fitur backup otomatis yang cukup banyak digunakan adalah JetBackup.

![Menu JetBackup untuk backup otomatis di cPanel](https://img.penasihathosting.com/2025/cpanel/JetBackup-1024x445.webp)

Dengan JetBackup, Anda bahkan memiliki akses untuk melihat file backup full, database, cron job bahkan email backup sekalipun.

Tergantung dari provider hosting yang Anda gunakan, ada yang akan melakukan backup website setiap hari sekali, ada yang seminggu sekali ada yang setiap tiga hari sekali.

### Cara Menggunakan JetBackup

Jika hosting Anda menyediakan JetBackup, berikut cara menggunakannya:

1. **Akses JetBackup** - Klik ikon JetBackup di cPanel
2. **Pilih jenis backup** - Full Backup, Home Directory, MySQL Databases, atau Email
3. **Pilih versi backup** - Backup diurutkan berdasarkan tanggal
4. **Pilih file atau folder** - Untuk restore parsial
5. **Klik Restore** - Untuk memulihkan file atau folder yang dipilih

JetBackup biasanya menyimpan beberapa versi backup, sehingga Anda dapat memilih titik waktu tertentu untuk dipulihkan.

## Backup Manual di cPanel

Tetapi, kalau cPanel yang Anda gunakan tidak memiliki fitur backup otomatis, Anda mungkin perlu melakukan backup secara manual. Misalnya melalui fitur Softaculous di cPanel.

**Atau jika Anda pengguna WordPress, Anda bisa memanfaatkan plugin backup seperti [UpDraftPlus](https://id.wordpress.org/plugins/updraftplus/) yang bisa membackup website Anda ke cloud langsung ke account Google Drive, Dropbox, dan lain-lain.**

Jika Anda ingin melakukannya secara manual di cPanel, Anda dapat mengikuti tutorial berikut:

Pertama, klik "Backup Wizard" pada cPanel Anda seperti terlihat dibawah ini (Anda juga dapat mengklik "Backup", tetapi  agar lebih mudah prosesnya, maka pilihlah "Backup Wizard").

![Menu Backup Wizard di cPanel](https://img.penasihathosting.com/2025/cpanel/Backup-wizard-1024x658.webp)

Kemudian Anda akan diarahkan kehalaman berikut ini:

![Langkah-langkah Backup Wizard di cPanel](https://img.penasihathosting.com/2025/cpanel/Backup-wizard-steps-1024x633.webp)

Dari sini, klik tombol "Backup" untuk memulai proses backup file website Anda. 

Kemudian Anda akan dihadapkan pada pilihan dimana Anda harus memilih antara membackup keseluruhan file (full backup) atau partial backup (Home Directory, MySQL Databases, Email Forwarders & Filters).

Demi kemudahan Anda kedepannya, pilihlah full backup dan simpan ditempat yang aman seperti di HDD external atau Flashdisk Anda, atau bila Anda tidak terlalu paranoid terhadap keamanan, Anda boleh menyimpannya dikomputer/laptop Anda.

![Pilihan Full Backup di Backup Wizard cPanel](https://img.penasihathosting.com/2025/cpanel/Full-backups-1024x553.webp)

Terakhir, Anda diminta untuk memilih tempat/destinasi backup, yang mana Anda cukup pilih "Home Directory".

![Halaman untuk generate full backup di cPanel](https://img.penasihathosting.com/2025/cpanel/Generate-backup-1024x909.webp)

Anda juga dapat memasukkan alamat email apabila Anda ingin menerima email pemberitahuan ketika proses backup telah selesai dilakukan dan untuk menyelesaikan proses backup, klik tombol "Generate Backup".

### Backup Parsial

Selain full backup, Anda juga dapat melakukan backup parsial untuk komponen tertentu:

#### Backup Home Directory

1. Klik "Backup" di cPanel
2. Di bagian "Partial Backups", klik "Download a Home Directory Backup"
3. Tunggu proses backup selesai
4. Klik link download yang muncul

#### Backup MySQL Databases

1. Klik "Backup" di cPanel
2. Di bagian "Partial Backups", klik "Download a MySQL Database Backup"
3. Pilih database yang ingin dibackup
4. Klik "Generate Backup"

#### Backup Email Forwarders

1. Klik "Backup" di cPanel
2. Di bagian "Partial Backups", klik "Download Email Forwarders"
3. Tunggu proses backup selesai
4. Klik link download yang muncul

## Restore Backup di cPanel

Memiliki backup tidak ada gunanya jika Anda tidak tahu cara memulihkannya. Berikut cara melakukan restore backup di cPanel:

### Restore Full Backup

1. Klik "Backup Wizard" di cPanel
2. Klik "Restore"
3. Pilih jenis backup yang ingin dipulihkan
4. Upload file backup atau pilih dari server
5. Klik "Restore"

### Restore Parsial

Untuk restore parsial, langkahnya mirip dengan restore full backup, tetapi Anda perlu memilih komponen spesifik yang ingin dipulihkan:

1. Klik "Backup Wizard" di cPanel
2. Klik "Restore"
3. Pilih jenis backup parsial (Home Directory, MySQL Database, atau Email Forwarders)
4. Upload file backup atau pilih dari server
5. Pilih file atau folder spesifik yang ingin dipulihkan
6. Klik "Restore"

## Jadwal Backup yang Direkomendasikan

Seberapa sering Anda harus melakukan backup tergantung pada seberapa sering website Anda diperbarui:

1. **Website yang sering diperbarui** (blog aktif, e-commerce):
   - Full backup: Mingguan
   - Database backup: Harian
   - File backup: Setiap kali ada perubahan signifikan

2. **Website yang jarang diperbarui** (website statis, portofolio):
   - Full backup: Bulanan
   - Database backup: Mingguan
   - File backup: Setiap kali ada perubahan

## Praktik Terbaik Backup Website

Berikut beberapa praktik terbaik untuk backup website:

1. **Ikuti aturan 3-2-1** - Buat 3 salinan backup, simpan di 2 media berbeda, dan 1 di lokasi offsite
2. **Otomatisasi proses backup** - Gunakan fitur backup otomatis atau plugin
3. **Verifikasi backup** - Pastikan backup dapat dipulihkan dengan benar
4. **Enkripsi backup** - Lindungi data sensitif dengan enkripsi
5. **Dokumentasikan proses** - Catat langkah-langkah backup dan restore
6. **Rotasi backup** - Hapus backup lama untuk menghemat ruang
7. **Backup sebelum perubahan besar** - Selalu backup sebelum update atau perubahan signifikan

## Mengecek Disk Space

Tidak peduli apapun yang dikatakan oleh penyedia hosting kepada Anda, disk space TIDAK akan pernah unlimited. Selalu ada batasan dalam penggunaannya, dan sebagai webmaster baru, Anda perlu mengetahui berapa banyak space yang Anda pakai.

Juga penting mengecek disk space Anda secara periodik untuk melihat bagian mana dari website Anda yang paling banyak mengambil space.

Klik "Disk Usage".

![Menu Disk Usage di cPanel](https://img.penasihathosting.com/2025/cpanel/Disk-usage-1024x645.webp)

Kemudian Anda akan diarahkan ke halaman "Disk Usage" seperti berikut:

![Informasi detail pemakaian disk space di cPanel](https://img.penasihathosting.com/2025/cpanel/Informasi-disk-usage-764x1024.webp)

Halaman ini memberikan Anda informasi tentang bagian-bagian yang berbeda dari file manager Anda dan menunjukan berapa banyak pemakaian disk dari tiap bagian-bagian tersebut.

Anda juga dapat melihat total pemakaian disk pada table pertama, sebagai contoh pada gambar diatas menunjukan total penggunaan disk adalah 372.29 MB.

### Mengelola Disk Space untuk Backup

Backup dapat menghabiskan banyak disk space. Berikut beberapa tips untuk mengelola disk space untuk backup:

1. **Kompresi file** - Gunakan format kompresi seperti ZIP atau TAR.GZ
2. **Hapus file sementara** - Bersihkan file cache dan temporary
3. **Backup selektif** - Backup hanya file penting, abaikan file cache dan log
4. **Gunakan penyimpanan eksternal** - Simpan backup di cloud storage atau hard drive eksternal
5. **Rotasi backup** - Hapus backup lama secara berkala

## Kesimpulan

Backup file website adalah langkah penting dalam mengelola website Anda. Dengan melakukan backup secara teratur, Anda dapat melindungi data Anda dari kehilangan dan memastikan website Anda dapat dipulihkan dengan cepat jika terjadi masalah.

Pada [bab berikutnya](/install-ssl-cpanel/), kita akan mempelajari cara menginstal SSL di cPanel, langkah penting untuk mengamankan website Anda.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips Penting</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Jangan hanya mengandalkan backup otomatis dari hosting. Selalu buat backup manual secara berkala dan simpan di lokasi yang berbeda. Ingat, backup terbaik adalah yang tidak pernah Anda butuhkan, tetapi selalu tersedia ketika Anda membutuhkannya.
  </p>
</div>
