---
title: "Review WarnaHost"
publishDate: 2018-12-11
updateDate: 2025-05-13
category: "Review Hosting"
author: "<PERSON><PERSON>"
excerpt: "WarnaHost offers cheap hosting with improved support and JetBackup, but suffers from poor uptime stability and slow server response times during load tests."
image: https://img.penasihathosting.com/2025/May/review-warna-host.webp
tags:
  - "Review Hosting Indonesia"
metadata:
  title: "Review WarnaHost"
  description: "Review WarnaHost."
  noticeType: update
---

import TLDRHighlight from '~/components/blog/TLDRHighlight.astro';
import Accordion from '~/components/ui/Accordion.astro';
import AccordionItem from '~/components/ui/AccordionItem.astro';
import LinkButton from '~/components/ui/LinkButton.astro'; // Assuming it might be used later

WarnaHost atau Warna Hosting memiliki tujuan untuk menyediakan [layanan web hosting](https://penasihathosting.com/) bagi kebutuhan personal maupun corporate dengan harga yang bersaing.

Didirikan pada tahun 2010, mereka berkantor pusat di Mamboro, <PERSON><PERSON>, Kota Palu, Sulawesi Tengah.

Kini WarnaHost sudah melayani lebih dari 10.000+ pelanggan. Sebuah pencapaian yang bagus dalam 10 tahun berdirinya, bukan?

Dan untuk mengetahui bagaimana kualitas WarnaHost, saya telah membeli [paket hosting murah](https://penasihathosting.com/hosting-murah/) yang dilengkapi dengan [control panel cPanel](https://penasihathosting.com/panduan-cpanel/). Ini saya gunakan sebagai _sample_ dalam penelitian saya, sehingga hasilnya dapat memberikan gambaran dan jawaban mengenai apakah WarnaHost merupakan pilihan yang tepat untuk rumah website Anda selanjutnya.

## Rangkuman Data Hasil Penelitian

| **RATING KAMI**                 | 3.5/5                      |
| :------------------------------ | :------------------------- |
| **SAMPLE**                      | Paket "Hemat Cloud"        |
| **RATA-RATA UPTIME (%)**        | 🔴 **99.381%**             |
| **AVG. RESPONSE TIMES (Load Test)** | 🔴 Lambat                  |
| **HARGA (Hemat Cloud)**         | 🟢 Mulai dari Rp 15.000/bln |
| **GARANSI REFUND**              | 🟢 45 Hari                 |

{/* Original table shortcode: [table id=22 responsive="scroll" /] */}

## Kelebihan Menggunakan WarnaHost

WarnaHost adalah provider hosting yang transparan.

Pada halaman paket [hosting murah](https://penasihathosting.com/hosting-murah/) mereka, Anda akan menemukan detail spesifikasi hosting yang lengkap, termasuk spesifikasi perangkat keras yang digunakan. Mereka mencantumkan penggunaan prosesor Intel E5-V4 dan NVMe SSD Gen 4, meskipun tampaknya mereka belum mencantumkan spesifikasi RAM. Jarang sekali Anda menemukan informasi tentang spesifikasi prosesor di halaman paket hosting penyedia lain.

Meski spesifikasi teknis ini tidak selalu berkorelasi langsung dengan kualitas layanan hosting, transparansi semacam ini patut diapresiasi. Banyak penyedia hosting lain yang bahkan enggan memberikan informasi detail tentang spesifikasi server mereka kepada pelanggan.

Jadi, apa saja kelebihan-kelebihan lain WarnaHost?

### 1. Pengujian Support WarnaHost 2024: Lebih Baik, Tapi Masih Ada Ruang Untuk Improvement

Sama seperti pengujian support pada provider lainnya, saya melakukan beberapa kali pengujian untuk mengetahui bagaimana kualitas layanan support dari WarnaHost.

Dan agar review WarnaHost ini lebih up-to-date, maka saya melakukan pengujian ulang atau kali keenam pada bulan Juli 2024.

Karena kontennya cukup panjang, maka saya bagi menjadi enam bagian dalam accordion dibawah ini:

<Accordion>
  <AccordionItem title="Pengujian Pertama">
    Saya melakukan pengetesan terhadap layanan support WarnaHost melalui live chat. Perlu diketahui bahwa support via live chat belum tersedia 24 jam, baru tersedia mulai jam 9 pagi sampai 10 malam.
    Jadi, pesan saya ditanggapi oleh Kang Ivan. Setelah menanyakan beberapa pertanyaan teknis, saya menilai supportnya sangat baik, pertanyaan saya juga dijawab dengan cepat dan sangat jelas.
    {/* Image Removed: warnahost-chat-1.webp */}
    Berbeda dengan kebanyakan support pada penyedia hosting lainnya dimana ketika Anda menanyakan hal-hal berbau teknis, mereka hanya akan mengirimkan Anda sebuah link panduan dan Anda perlu menyelesaikan masalah Anda sendiri.
    Tapi di WarnaHost, Anda akan mendapatkan support yang tidak seperti 'robot', tapi lebih melayani.
  </AccordionItem>
  <AccordionItem title="Pengujian kedua - Desember 2020">
    Sebenarnya, support adalah faktor yang sangat sulit untuk di uji, karena kasus atau masalah konsumen berbeda-beda. 
    Pengujian yang saya lakukan pun hanya pengujian yang sederhana. Berbeda dengan pengujian uptime dan speed yang angka nya pasti.
    Tapi pengujian support berbeda.
    Jadi, saya tidak bisa menjamin sepenuhnya bahwa support yang akan Anda terima akan sama bagus dan cepat nya dengan hasil pengujian yang saya lakukan.
    Pada pengujian pertama, pengujian yang saya lakukan sederhana sekali, hanya menanyakan beberapa pertanyaan dasar. Namun, pada pengujian kedua ini, saya coba melakukan pengujian yang sifatnya agak teknis.
    Bagaimana cara nya?
    Saya dengan sengaja membuat error pada website test WarnaHost, yaitu _error establishing a database connection._ Sebuah error yang faktanya mudah untuk diatasi bahkan oleh staf teknis yang masih baru atau junior sekalipun.
    Saya percaya error ini dapat diselesaikan dalam waktu kurang dari 10 menit.
    Saya mengirimkan tiket mengeluhkan website saya muncul error pada 28 September 2020 pukul 12:19 pm.
    Kemudian 6 menit setelahnya saya mendapatkan balasan untuk memastikan apakah user database dan password sudah benar.
    Begini percakapan melalui tiket nya:
    {/* Image Removed: warnahost-chat-2.webp */}
    Mereka memberikan solusi dari error yang sengaja saya buat sebelumnya. Harus saya akui, solusinya benar, tapi saya ingin mereka yang memperbaiki nya sendiri, karena tidak semua orang paham hal-hal yang berbau teknis, bukan?
    Maka ketika mereka meminta diberikan izin untuk mengakses kontrol panel untuk dilakukan pengecekan lebih lanjut, saya meng-iyakan nya.
    {/* Image Removed: warnahost-chat-3.webp */}
    Dan 5 menit setelah nya, error pada website test WarnaHost saya sudah berhasil diatasi.
    Total waktu dibutuhkan adalah 11 menit.
    Poin plus nya adalah mereka meminta izin untuk mengakses halaman cPanel dan saya temukan mereka satu dari dua provider yang meminta izin terlebih dahulu untuk mengakses cPanel klien. Saya pikir privasi sangat dijaga di WarnaHost.
  </AccordionItem>
  <AccordionItem title="Pengujian ketiga - September 2021">
    Agar review ini lebih akurat, pada update terakhir di bulan September 2021, saya melakukan pengujian ulang terhadap support WarnaHost.
    Ini penting dilakukan guna mendapatkan informasi terkait perkembangan WarnaHost dari waktu ke waktu. Tidak ada yang tahu, bisa saja mereka saat ini mengalami masalah dalam layanan supportnya, bukan?
    Maka update penting dilakukan.
    Kali ini pengujian sedikit lebih sulit meskipun masih merupakan error yang umum terjadi pada website WordPress.
    Saya sengaja membuat critical error 500. Kemudian mengirimkan tiket untuk dibantu memperbaiki error yang terjadi.
    {/* Image Removed: warnahost-chat-4.webp */}
    Sama seperti pada pengujian kedua, mereka meminta izin untuk akses ke cPanel klien untuk melihat log error.
    Tidak butuh waktu lama sampai mereka menemukan error dan memperbaikinya. Total waktu yang dibutuhkan dari awal saya mengirimkan tiket hingga error selesai diperbaiki adalah 18 menit.
    Termasuk cepat, meski bukan yang tercepat. 
    Saya pikir sampai review ini saya update, layanan support mereka masih bagus dan bisa diandalkan. 
  </AccordionItem>
  <AccordionItem title="Pengujian Keempat - November 2022">
    Kami di PenasihatHosting melakukan pengujian ulang satu kali dalam setahun. Tujuannya adalah untuk mengetahui jika ada peningkatan dalam kualiats atau penurunan.
    Setelah pengujian ketiga dilakukan, kami melakukan pengujian kembali. Masih sama, yaitu pengujian teknis dan masih tergolong mudah untuk diperbaiki meskipun agak _tricky._ Pengujian kali ini berhubungan dengan gambar yang broken di WordPress.
    Saya mengirimkan ticket meminta bantuan tanggal 9 November 2022, pukul 15:50 dan mengeluhkan permasalahan gambar yang broken. Kemudian, tiket mendapatkan balasan sekaligus berita perbaikan masalah pukul 16:59. Total waktu yang dibutuhkan adalah 1 jam dan 9 menit.
    {/* Image Removed: pengujian-september-2022-WarnaHost-support.png */}
    Dari pengujian saya, terlihat penurunan kecepatan response pada WarnaHost. Penjelasan mereka adalah karena antrian tiket sedang banyak. Betul alasannya bisa diterima, tapi kita sedang membandingkan dan mereview layanan mereka, jadi saya pikir adil untuk mengatakan bahwa support mereka mengalami penurunan kecepatan response dibandingkan pengujian tahun-tahun sebelumnya.
    Mengingat jumlah customer yang mengalami peningkatan dari tahun ke tahun, saya pikir mungkin sudah seharusnya mereka menambah amunisi baru dalam jajaran customer service mereka. Owner WarnaHost, Kang Ivan pernah mengatakan sulit untuk mencari SDM di Palu, Sulawesi Tengah dan memiliki rencana pindah ke Jogja.. Mungkin saja ini alasan dibaliknya.
  </AccordionItem>
  <AccordionItem title="Pengujian Kelima - 2023">
    Kami berkomitmen untuk menyajikan hasil pengujian yang benar-benar mencerminkan pengalaman yang diterima oleh seluruh pelanggan. Namun, jika penyedia hosting mengetahui kami sedang menguji layanan mereka, mereka mungkin akan memberikan perhatian khusus pada kasus kami, yang bisa mengubah hasil pengujian.
    Oleh karena itu, kami telah memutuskan untuk menunda pengujian ulang hingga tahun 2024, di mana kami akan melakukan penelitian secara private, bukan secara publik seperti saat ini.
  </AccordionItem>
  <AccordionItem title="Pengujian Keenam - Juli 2024 (terbaru)">
    Setelah absen di tahun 2023, saya akhirnya kembali menguji layanan support dari berbagai provider hosting, termasuk WarnaHost. Pengujian ini dilakukan pada Juli 2024, dan seperti biasa, saya fokus pada aspek teknis.
    Kali ini, saya mengulang skenario "error establishing data connection" yang pernah saya gunakan pada pengujian 2020. Caranya? Simpel, saya "merusak" file wp-config.php sehingga koneksi database terputus dan website menjadi tidak bisa diakses. Klasik, tapi efektif untuk menguji respons support.
    Berikut timeline pengujian:
    - 15:05 - Tiket dikirim
    - 15:23 (18 menit kemudian) - Support merespons, mengatakan akan melakukan pengecekan
    - 15:38 (15 menit setelah respons pertama) - Website kembali normal
    {/* Image Removed: pengujian-support-warnahost-1-1.png */}
    Total waktu dari pengiriman tiket hingga masalah teratasi: 33 menit.
    Pertanyaannya: Apakah 33 menit ini tergolong cepat? Well, jika dibandingkan dengan pengujian 4 tahun lalu dimana mereka bisa menyelesaikan masalah dalam 11 menit, jawabannya jelas tidak.
    Tapi ada kabar baiknya: Dibandingkan pengujian terakhir di tahun 2022, kinerja mereka kali ini jauh lebih baik. Ada perbaikan yang cukup signifikan.
    Jadi, meskipun belum kembali ke performa terbaiknya, saya rasa peningkatan ini layak masuk dalam poin kelebihan WarnaHost pada update review kali ini. Setidaknya, mereka menunjukkan tren positif.
    {/* Image Removed: pengujian-support-warnahost-2024-2.png */}
  </AccordionItem>
</Accordion>

### 2. Harga Hosting yang Murah + Penawaran Jumlah Sumber Daya yang Memadai

WarnaHost menawarkan paket hosting murah mulai dari Rp 198.000/tahun (naik dari tahun sebelumnya Rp 180.000/tahun, tapi tetap murah, meskipun bukan yang termurah).

Saya yakin Anda tidak akan menemukan yang lebih murah dan lebih banyak jumlah resources nya dibandingkan paket hosting murah WarnaHost dimanapun Anda cari.

Sebentar.. pada saat review ini saya update, sebenarnya ada tiga provider lainnya yang juga saya review di Penasihat Hosting, yang dapat menyaingi mereka dari aspek harga, yaitu:

- DomaiNesia Starter: Rp 168.000/tahun
- IdCloudHost Starter Pro: Rp 180.000/tahun
- Jagoan Hosting IDOL: Rp 180.000/tahun

Paket DomaiNesia Starter sayangnya tidak menggunakan LiteSpeed Enterprise, melainkan Nginx. Adapun Paket IdCloudHost starter saat ini sudah 'sold out'.

Dengan paekt Hemat Cloud WarnaHost seharga Rp 15.000/bulan, Anda akan mendapatkan:

- 1 GB Cloud NVMe
- 2 Total Domains
- Unlimited Bandwidth
- 0.5 CPU Core
- 512 MB RAM
- Gratis Domain
- 5 Email Account
- 5 Mysql Database
- cPanel Control Panel
- 45 hari garansi uang kembali

Luar biasa, bukan? Bahkan mereka mengklaim sudah menggunakan NVMe, bukan lagi SSD, apalagi HDD.

### 3. Sudah Menggunakan JetBackup untuk Backup Otomatis Website Harian

Tidak banyak paket hosting murah yang menyediakan fitur JetBackup atau backup otomatis harian untuk website Anda. Setelah melakukan peninjauan terhadap beberapa provider, saya menemukan bahwa [Jagoan Hosting](https://penasihathosting.com/review-jagoan-hosting/) dan [IdCloudHost](https://penasihathosting.com/review-idcloudhost/) adalah yang terbaik dalam hal ini, dan setelah itu WarnaHost.

Dengan menggunakan layanan ini, website Anda akan otomatis dibackup setiap hari dengan masa penyimpanan selama 2 hari. Selain itu, tersedia juga backup mingguan dan bulanan dari satu bulan sebelumnya.

{/* Image Removed: jetbackup-5-di-cpanel-warnahost.png */}

### 4. Garansi Hosting 45 Hari

Sebelumnya, WarnaHost hanya memberikan garansi selama 30 hari untuk pelanggan barunya, tetapi, per update review bulan Agustus 2023 lalu, saya menemukan ada perubahan yang sangat positif dari WarnaHost, dimana masa garansi diperpanjang hingga 45 hari.

Dan setelah saya baca kembali ketentuan garansinya, tidak ada aturan atau syarat garansi yang akan sulit, hanya garansi ini berlaku untuk minimal sewa hosting 3 bulan dan untuk akun pertama atau akun baru saja.

Saya acungi dua jempol buat WarnaHost.

### 5. Gratis Domain dan Gratis Migrasi Hosting

Bagi Anda yang bukan orang 'technical', Anda tidak perlu khawatir ketika ingin memindahkan file website Anda dari hosting lama ke WarnaHost, karena mereka akan bantu memindahkan nya untuk Anda secara gratis alias tanpa biaya.

Selain itu, mereka juga menawarkan gratis [domain](https://hostingpedia.id/kategori/domain/) untuk apapun paket hosting yang Anda ambil. Ketentuannya sebagai berikut:

Jika Anda sewa paket hosting murah "Hemat":

{/* Image Removed: gratis-domain-paket-hemat.png */}

Dan jika Anda menyewa minimal paket "Starter":

{/* Image Removed: gratis-domain-min-paket-starter.png */}

### 6. Ada Banyak Perbaikan Dilakukan WarnaHost Sejak Review Update Sebelumnya

Saya mencatat ada enam perbaikan yang WarnaHost lakukan guna meningkatkan kualitas layanannya. Apa saja?

1. Semua paket hosting sudah menggunakan cPanel
2. Web server pada paket cPanel telah menggunakan LiteSpeed Enterprise. Memang ada kenaikan harga, tapi saya yakin harga mereka masih termasuk termurah.
3. Sudah menggunakan fitur JetBackup
4. Ada 3 file backup yang tersedia, yaitu backup-an setiap 2 hari sekali dan setiap 1 bulan sekali. Sebelumnya hanya 1x sebulan.
5. Sudah ada opsi berlangganan hosting secara bulanan, walaupun harganya tidak adil, dimana harga bulanan 2x lipat dari harga tahunan
6. Client area yang saya pikir sudah lebih cepat dari pengujian sebelumnya, yang mana merupakan perbaikan yang baik dari aspek user experience.

Keenam poin diatas pada review sebelumnya ada dibagian kekurangan WarnaHost, namun saya senang sekali Kang Ivan selaku founder sangat positif menyambut kekurangan yang telah saya tulis sebelumnya.

Sangat jarang founder seperti Kang Ivan.

## Kekurangan menggunakan WarnaHost

Setidaknya, ada 4 kekurangan WarnaHost dalam catatan saya.

### 1. Rata-rata waktu response server yang lambat pada pengujian _load testing_

Apa itu _load testing_?

> Bayangkan Anda punya restoran. Load testing itu seperti tiba-tiba mendatangkan 50 orang sekaligus ke restoran Anda dan melihat apakah para koki dan pelayan bisa menanganinya tanpa membuat pelanggan menunggu terlalu lama. Dalam dunia hosting, "koki dan pelayan" ini adalah server mereka.

Masalah umum yang sering terjadi adalah server hosting sering mengalami _overload_ atau bahkan _downtime_ ketika menerima banyak pengunjung secara bersamaan.

Untuk mengatasi masalah ini, pengujian Load Testing sangat penting dilakukan. Ini juga dapat membuktikan kualitas server yang digunakan provider hosting.

Jadi, untuk mengetahuinya saya melakukan pengujian dengan mengirimkan 10 virtual user secara bersamaan untuk menavigasi website test WarnaHost. Tujuannya untuk melihat apakah server shared hosting WarnaHost mampu menangani semua permintaan dan apakah waktu respon tetap stabil atau malah melambat.

Alat yang saya gunakan dalam pengujian ini adalah Locust.io (open source).

Ohya, dalam pengujian saya tidak mengaplikasikan cache pada website/server dan juga ekstensi php seperti opcache dan redis untuk murni menguji performa server.

Hasil pengujian load testing nya dapat dilihat pada gambar dibawah ini:

{/* Image Removed: warnahost-load-testing-2024.svg */}

Dalam grafik hasil pengujian, ada empat garis yang perlu kita perhatikan:

1. Hijau: Waktu respons server
2. Merah: Jumlah request per detik
3. Biru: Jumlah pengunjung virtual
4. Kuning: Menunjukkan error (jika ada) selama pengujian

Idealnya, garis hijau (waktu respons) seharusnya tetap stabil meskipun garis biru (jumlah pengunjung) meningkat. Namun, pada hasil pengujian WarnaHost, kita melihat garis hijau ikut naik seiring bertambahnya pengunjung virtual. Ini bukan pertanda bagus.

Apa artinya? Sederhana: semakin banyak orang mengakses website Anda secara bersamaan, semakin lambat website tersebut akan berjalan.

Selama 2,5 menit pengujian, rata-rata waktu respons WarnaHost adalah 3703,52 ms. Dibandingkan dengan enam provider lain yang saya uji di tahun 2024, angka ini termasuk yang paling lambat.

### 2. Tingkat Stabilitas Uptime yang Buruk (99,381%)

Secara keseluruhan, dari hasil monitoring sejak Januari 2021, saya bisa bilang bahwa sebenarnya rata-rata uptime WarnaHost cukup bagus. Lebih banyak stabilnya daripada tidaknya.

Tapi, tidak cukup hanya lebih banyak stabilnya daripada tidaknya, bukan?

Coba perhatikan data rata-rata WarnaHost dalam rentang Oktober 2021 - Mei 2023 berikut ini:

{/* Original table shortcode: [table id=87 responsive="scroll" /] */}

Dan bandingkan dengan data terbaru di tahun 20 Februari 2024 - 30 Juni 2024:

{/* Image Removed: rata-rata-uptime-warnahost-di-2024.svg */}
{/* Original table shortcode: [table id=100 /] */}

Seperti yang saya katakan sebelumnya, tidak cukup dengan uptime yang lebih banyak stabilnya saja.

Apabila hosting Anda mengalami downtime dengan frekuensi sering dan untuk waktu yang lama dalam sebulan, tentu akan menimbulkan kerugian yang besar bagi Anda. Itu sebabnya, uptime selalu menjadi faktor pertama yang perlu menjadi pertimbangan Anda dalam memilih hosting.

### 3. Semurah Rp 16.500/bulan (1 tahun) atau Rp 30.000 (per bulan)

Anda akan mendapatkan harga yang lebih murah hanya jika langsung berlangganan 1 tahun atau 2 tahun.. Strategi seperti ini sejatinya sudah umum di industri [web hosting](https://hostingpedia.id/kategori/web-hosting/), tidak hanya pasar Indonesia, namun internasional. Sebagai pengguna yang terbiasa membayar apapun secara bulanan, saya tidak menyukainya. Menurut saya tidak adil saja dan seakan-akan memaksa konsumen untuk membayar lebih mahal (dari sisi konsumen), meskipun saya cukup bisa mengerti alasan dibaliknya.

Namun, ada beberapa provider yang harga hosting per bulannya semurah harga per tahunnya, seperti [IdCloudHost](https://penasihathosting.com/review-idcloudhost/), [Kenceng Solusindo](https://penasihathosting.com/review-kenceng-solusindo/), [Jagoan Hosting](https://penasihathosting.com/review-jagoan-hosting/) dan [Dracoola Multimedia](https://hostingpedia.id/hosting/dracoola-multimedia/).

### 4. Backup nya Masih Memiliki Keterbatasan

Betul WarnaHost sudah menggunakan JetBackup untuk aktifitas backup dan restore backup, tidak lupa juga Anda bisa menggunakan backup, restore dan staging di Softaculous, hanya saja, untuk backup otomatis via JetBackup, file yang tersedia hanya ada 3:

- Backup yang setiap 2 atau 3 hari sekali sebanyak dua file backup
- Backup yang setiap 1 bulan sekali sebanyak 1 file backup

## Rangkuman Paket Hosting WarnaHost dan Informasi Lainnya

### Rangkuman penawaran paket hosting di WarnaHost:

WarnaHost menawarkan berbagai produk hosting yang lengkap, mulai dari [hosting murah](https://penasihathosting.com/hosting-murah/), [WordPress hosting](https://hostingpedia.id/kategori/wordpress-hosting/), unlimited hosting, [Cloud VPS unmanaged](https://hostingpedia.id/kategori/unmanaged-vps/) dan [managed VPS](https://hostingpedia.id/kategori/manage-vps/) hingga [dedicated server](https://hostingpedia.id/kategori/dedicated-server/). Berikut adalah detail harga paket hosting murah WarnaHost.

{/* Image Removed: paket-hosting-murah-warnahost-juli-2024.png */}

### Informasi Lainnya yang Perlu Anda Ketahui

WarnaHost menawarkan 9 pilihan paket hosting mulai dari paket hosting murah, hosting bisnis, hosting khusus WordPress hingga Dedicated Server.

- **Pilihan server:** Jakarta (IIX) dan Singapore (SG). Dapat dipilih sesuai kebutuhan pada saat memesan hosting.
- **Aktifasi acount:** instant activation
- **Control Panel yang digunakan:** cPanel dengan LiteSpeed Enterprise
- **Gratis domain:** Apapun paket yang Anda pilih. Terkhusus ekstensi .com, hanya gratis untuk paket tertentu saja
- **Instalasi aplikasi (WordPress, Joomla, Drupal, dll):** Dapat di instal dengan sangat mudah dengan tool Softaculous yang ada di semua control panel.

## Kesimpulan: Apakah Saya Merekomendasikan WarnaHost?

WarnaHost bisa menjadi salah satu pilihan hosting yang Anda pertimbangkan, terutama dengan beberapa perbaikan yang telah mereka lakukan, seperti:

- Paket hosting murah mereka sudah menggunakan cPanel + LiteSpeed Enterprise
- Layanan support nya menunjukan tren yang positif
- Fitur backup otomatis harian, mingguan dan bulanan
- Garansi yang lebih panjang dari rata-rata industri, yaitu 45 hari

Namun, mereka masih memiliki dua kekurangan penting: rata-rata waktu respons yang lambat dan tingkat kestabilan uptime server yang masih rendah.

Dengan uptime yang buruk, sangat sulit untuk merekomendasikan provider ini.

<TLDRHighlight
  ratingValue={3.5}
  visitLinkHref="/go/warnahost"
  visitLinkText="Kunjungi WarnaHost"
  visitLinkRel="nofollow external"
/> 