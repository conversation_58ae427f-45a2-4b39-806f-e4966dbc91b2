---
title: "Antarmuka cPanel: Mengenal Tam<PERSON>lan dan Fungsi <PERSON>"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/antarmuka-cpanel.webp
excerpt: "Mengenal tampilan dan fungsi-fungsi dasar pada antarmuka cPanel. Panduan lengkap untuk memahami layout dan navigasi cPanel."
metadata:
  title: "Antarmuka cPanel: Mengenal Tampilan dan Fungsi <PERSON>ar (Edisi 2025)"
  description: "Pelajari tampilan dan fungsi-fungsi dasar pada antarmuka cPanel. Panduan lengkap untuk memahami layout dan navigasi cPanel dengan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 2
  chapterTitle: "Antarmuka cPanel"
---

# Antarmuka cPanel: Mengenal Tampilan dan Fungsi Dasar

Setelah Anda ber<PERSON> [login ke cPanel](/apa-itu-cpanel/), langkah selanjutnya adalah memahami tampilan dan fungsi-fungsi dasar pada antarmuka cPanel. Hal ini akan memudahkan Anda dalam mengelola website Anda.

## Tampilan Utama cPanel

Ketika Anda sudah log in ke cPanel, maka Anda akan melihat tampilan cPanel seperti dibawah ini.

![Tampilan antarmuka utama cPanel dengan tema Jupiter](https://img.penasihathosting.com/2025/cpanel/Tampilan-cPanel.webp "tampilan cpanel")

Secara default, di tahun 2025 ini, jika provider hosting Anda rutin melakukan update pada cPanel, seharusnya tampilan cPanel Anda sudah menggunakan theme "Jupiter" seperti diatas. Yang membedakan harusnya adalah warna nya saja, biasanya sesuai dengan warna brand provider hosting.

### Apabila tampilan cPanel Anda berbeda, jangan panik!

Jangan panik jika tampilan cPanel pada provider hosting Anda berbeda, karena hanya tampilan atau theme saja yang berganti, adapun pengaturan nya tidak.

## Mengenal Fungsi-fungsi Dasar Pada cPanel

Antarmuka cPanel terdiri dari beberapa bagian utama yang perlu Anda ketahui:

1. **Search Bar dan Navigation Bar**
2. **Left Sidebar**
3. **Right Sidebar**
4. **Main Content Area**

Mari kita bahas satu per satu.

### Search Bar dan Navigation Bar

Dibagian paling atas sebelah kanan cPanel, Anda akan menemukan kolom pencarian, icon lonceng (notifications), dan user icon pengguna (user preferences).

![Lokasi search bar, notifikasi, dan user preferences di cPanel](https://img.penasihathosting.com/2025/cpanel/Navigation-bar-1024x565.webp)

- **Search Features:** digunakan untuk mencari fungsi apapun di cPanel, misanya mencari "File Manager", "Disk Usage", "Errors", dan lain-lain. Fitur pencarian ini sangat berguna terutama bagi pemula yang belum familiar dengan lokasi fitur-fitur di cPanel.

- **User preferences:** dimana Anda akan dengan mudah menemukan pengaturan utama, seperti mengganti password cPanel, mengganti bahasa, mengganti theme/tampilan cPanel, melihat informasi kontak Anda (email yang digunakan pada cPanel) dan juga pengaturan untuk mereset seluruh pengaturan dengan hanya satu kali klik saja.

- **Notifications:** sama seperti notifications pada social media, gunanya adalah untuk memberitahu Anda apabila terdapat update tertentu pada cPanel dan informasi lainnya yang berkaitan dengan cPanel.

- **Logout:** ini sangat penting, maksud saya apabila Anda sudah selesai menggunakan cPanel, demi alasan keamanan sebaiknya Anda langsung me-logout cPanel Anda dengan mengklik logout.

### Left Sidebar

Pada tampilan cPanel terbaru, secara default, harusnya hanya ada menu, yaitu Tools dan WordPress Manager by Softaculous.

![Menu Tools dan WordPress Manager di sidebar kiri cPanel](https://img.penasihathosting.com/2025/cpanel/Sidebar-cPanel-1024x694.webp)

- **Tools:** Anda bisa mengklik menu Tools untuk kembali ke halaman awal cPanel. Anggap ini seperti menu "Home" pada website.

- **WordPress Manager:** Well, lebih dari 60% website di dunia dibangun menggunakan WordPress, jadi tidak heran cPanel membuat dan menempatkan menu WordPress Manager di sidebar sehingga Anda dengan mudah menemukannya. Saya akan bahas lebih detail WordPress Manager di bagian tersendiri nantinya.

### Right Sidebar

Sedangkan dibagian sebelah kanan tampilan cPanel, Anda akan menemukan dua informasi penting yang berkaitan dengan account dan spesifikasi hosting Anda, yaitu general information dan statistics.

![Informasi General Information dan Statistics di sidebar kanan cPanel](https://img.penasihathosting.com/2025/cpanel/Sidebar-kanan-1024x758.webp)

- **General Information:** dimana Anda dapat melihat informasi umum pada cPanel Anda, seperti user yang sedang Anda gunakan, domain utama pada cPanel, alamat IP, informasi last login, juga terdapat link yang mengarah ke informasi server hosting Anda.

- **Statistics:** dimana Anda akan menemukan informasi terkait resources yang sudah Anda gunakan, seperti jumlah bandwidth yang terpakai, jumlah penggunaan disk/storage, penggunaan CPU, RAM, dll.

### Main Content Area

Area utama cPanel berisi berbagai kategori fitur yang dikelompokkan berdasarkan fungsinya. Beberapa kategori utama meliputi:

1. **Files** - Untuk mengelola file website, backup, dan disk space
2. **Domains** - Untuk mengelola domain, subdomain, dan redirects
3. **Email** - Untuk mengelola email account, forwarders, dan spam filters
4. **Databases** - Untuk mengelola database MySQL/MariaDB
5. **Software** - Untuk menginstal aplikasi seperti WordPress
6. **Security** - Untuk mengatur SSL, IP blocking, dan keamanan lainnya
7. **Advanced** - Untuk pengaturan lanjutan seperti cron jobs dan error logs

Setiap kategori berisi ikon-ikon yang mewakili fitur-fitur spesifik yang dapat Anda akses.

## Melakukan Organisir pada cPanel

Anda juga dapat mengorganisir semua bagian pada cPanel Anda dengan membuka atau menutup bagian-bagian tertentu. Caranya mudah, tinggal klik yang saya beri panah pada gambar dibawah ini:

![Tombol untuk mengorganisir bagian-bagian di cPanel](https://img.penasihathosting.com/2025/cpanel/Organisir-cPanel-1024x486.webp)

Anda juga dapat memindahkan (drag and drop) bagian-bagian tertentu keatas dan kebawah sesuai dengan keinginan Anda.

![Ilustrasi drag and drop untuk memindahkan bagian di cPanel](https://img.penasihathosting.com/2025/cpanel/Drag-and-drop-cPanel-1024x621.webp)

## Mengubah Tampilan cPanel

cPanel menawarkan beberapa opsi tampilan yang dapat Anda sesuaikan dengan preferensi Anda:

1. **Paper Lantern** - Tampilan klasik yang lebih sederhana
2. **Jupiter** - Tampilan modern dengan desain yang lebih bersih (default di 2025)
3. **Basic Mode** - Tampilan yang lebih sederhana dengan ikon yang lebih besar

Untuk mengubah tampilan, klik ikon user preferences di pojok kanan atas, lalu pilih "Change Style".

## Tips Navigasi cPanel

Berikut beberapa tips untuk memudahkan navigasi di cPanel:

1. **Gunakan Search Bar** - Cara tercepat untuk menemukan fitur yang Anda butuhkan
2. **Bookmark Halaman** - Bookmark halaman cPanel yang sering Anda gunakan
3. **Gunakan Keyboard Shortcuts** - Beberapa keyboard shortcuts tersedia di cPanel
4. **Atur Favorit** - Tempatkan fitur yang sering digunakan di bagian atas
5. **Gunakan Breadcrumbs** - Perhatikan breadcrumbs di bagian atas untuk navigasi

## Kesimpulan

Memahami antarmuka cPanel adalah langkah penting dalam mengelola website Anda dengan efektif. Dengan mengenal tampilan dan fungsi-fungsi dasar pada cPanel, Anda akan lebih mudah menavigasi dan menggunakan fitur-fitur yang tersedia.

Pada [bab berikutnya](/preferences-cpanel/), kita akan mempelajari cara mengupdate preferences di cPanel, termasuk cara mengganti password dan mengelola user.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips Penting</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Luangkan waktu untuk menjelajahi antarmuka cPanel dan membiasakan diri dengan lokasi fitur-fitur yang berbeda. Ini akan menghemat waktu Anda di masa depan saat perlu mengakses fitur tertentu dengan cepat.
  </p>
</div>
