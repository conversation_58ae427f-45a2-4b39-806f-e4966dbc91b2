---
title: "Tutorial atau Cara Install WordPress di Cloudways"
publishDate: 2023-05-26
updateDate: 2024-11-21
category: "Tutorial"
image: "https://img.penasihathosting.com/2025/May/install-wordpress-di-cloudways.webp"
excerpt: "Panduan lengkap cara menginstall WordPress di Cloudways dengan mudah. Pelajari langkah-langkah instalasi WordPress, konfigurasi domain, dan pengaturan SSL."
metadata:
  title: "Tutorial Install WordPress di Cloudways | Panduan Lengkap"
  description: "Panduan lengkap cara menginstall WordPress di Cloudways dengan mudah. Pelajari langkah-langkah instalasi WordPress, konfigurasi domain, dan pengaturan SSL."
---

import PromoBanner from '~/components/ui/PromoBanner.astro';

Artikel ini merupakan kelanjutan dari rangkaian tulisan saya mengenai [review Cloudways](https://penasihathosting.com/review-cloudways/) yang telah saya publikasikan sebelumnya.

Saya percaya akan sangat membantu jika saya menyusun tutorial atau panduan langkah demi langkah tentang cara menginstall WordPress di Cloudways.

Oh ya, jika Anda mencari tutorial bagaimana cara migrasi website ke Cloudways (bukan install WordPress baru), silahkan kunjungi [tutorial yang ini.](https://penasihathosting.com/install-wordpress-cloudways/)

Sebenarnya caranya tidak sulit, tapi untuk pemula proses ini mungkin terasa agak rumit, terutama jika Anda baru saja berpindah dari cPanel dan merasa sedikit kaget dengan user interface Cloudways yang sangat berbeda.

Pertama sekali, pastikan Anda telah memiliki akun di Cloudways. Jika belum, saya sarankan Anda untuk mendaftar terlebih dahulu. Oh ya, Anda juga bisa mencoba layanan Cloudways (trial) terlebih dahulu selama 3 hari.

<PromoBanner
  title="Cloudways Hosting"
  description="Dapatkan diskon 30% selama 2 bulan dengan kode promo di bawah ini. Gratis trial 3 hari tanpa perlu kartu kredit!"
  ctaText="Coba Cloudways Sekarang"
  ctaLink="https://penasihathosting.com/go/cloudways"
  promoCode="PHCW30"
  imageSrc="https://img.penasihathosting.com/2025/May/kode-promo-Cloudways.webp"
  imageAlt="Kode Promo Cloudways untuk diskon 30%"
  disclaimer="Support Anda membantu website ini terus berjalan! Saya menggunakan link affiliasi, dimana saya akan menerima komisi $65 dari pembelian hosting Cloudways Anda. Anda tidak akan dikenakan biaya tambahan."
/>

### 1\. Tambahkan Aplikasi Baru

Setelah Anda berhasil _log in_ ke Cloudways, mungkin secara otomatis tampilan dashboard Cloudways Anda akan menggunakan user interface yang telah diperbarui, seperti tampak pada gambar dibawah ini.

Langkah pertama yang perlu Anda lakukan adalah mengklik tombol "Add Application".

![Tampilan dashboard Cloudways dengan tombol Add Application](https://img.penasihathosting.com/2025/May/klik-tombol-add-application.webp "Tampilan dashboard Cloudways dengan tombol Add Application")

Jika ternyata tampilan _user interface_ Anda masih menggunakan yang lama, saya menyarankan untuk menggantinya ke yang baru, karena saya yakin mereka tidak akan menawarkan opsi untuk mengganti ke tampilan yang lama (classic) dalam waktu yang panjang.

![Tombol Try Now untuk beralih ke UI baru Cloudways](https://img.penasihathosting.com/2025/May/klik-tombol-try-now.webp "Tombol Try Now untuk beralih ke UI baru Cloudways")

Kemudian, isi detail aplikasi Anda:

**PILIH DAN ISI APLIKASI**

Pilih aplikasi yang akan Anda install. Jika Anda ingin menginstall WordPress yang telah dioptimasi secara otomatis oleh Cloudways, maka pilih "Version 6.1.1" (versi yang tersedia pada saat review ini saya tulis).

Atau jika Anda ingin mengoptimasi Cloudways Anda sendiri, maka pilih yang "Clean" (tidak disarankan untuk pemula).

Jika Anda tidak yakin, saran saya pilih yang "Version 6.1.1".

![Pilih aplikasi WordPress di Cloudways](https://img.penasihathosting.com/2025/May/pilih-apliaksi.webp "Pilih aplikasi WordPress di Cloudways")

Selanjutnya, Anda akan diminta untuk mengisi detail aplikasi Anda. Mencakup nama aplikasi, nama server, dan nama project. Anda bebas mengisi bagian ini sesuai keinginan, karena isinya tidak akan mempengaruhi apapun dan dapat diubah kapan saja nantinya.

![Form pengisian detail aplikasi WordPress](https://img.penasihathosting.com/2025/May/isi-detail-aplikasi.webp "Form pengisian detail aplikasi WordPress")

**PILIH SERVER**

- Pilih server Anda. Saran saya jika Anda pemula atau baru memulai atau tidak yakin, maka pilih **Digital Ocean.**

- Pilih server size: dengan cara menggesernya ke kanan atau ke kiri. Anda bisa selalu mulai dengan 1 GB terlebih dahulu dan jika memang kurang ingat bahwa _server size_ Anda bisa di upgrade nantinya.

- Pilih location: jika website Anda berbahasa Indonesia, maka pilih Singapore. Jika berbahasa inggris, saran saya pilih salah satu kota di United States.

- Cek kembali detail aplikasi dan server Anda, jika sudah benar, maka klik tombol "Launch Now"

![Form pemilihan server, ukuran, dan lokasi di Cloudways](https://img.penasihathosting.com/2025/May/Pilih-server-Anda.webp "Form pemilihan server, ukuran, dan lokasi di Cloudways")

Setelah Anda klik tombol "Launch Now", maka Cloudways akan melakukan set up terhadap server Anda dan biasanya prosesnya sekitar 7 menit.

![Proses pembuatan server Cloudways](https://img.penasihathosting.com/2025/May/proses-launch-server-1024x357.webp "Proses pembuatan server Cloudways")

### 2\. Menambahkan Domain

Selanjutnya, Anda perlu menambahkan domain agar terhubung dengan server.

Oia, sebelum masuk ke tahap menambahkan domain, saya kira Anda perlu mengetahui bahwa di Cloudways pengaturan server dan aplikasi itu berbeda dan memang sudah seharusnya berbeda, hanya saja, untuk pemula mungkin akan kebingungan.

Pada gambar dibawah ini (setelah anda berhasil menambahkan server pada langkah sebelumnya), ada 2 tab yang perlu Anda perhatikan, yaitu tab "Servers" dan tab "Applications".

![Perbedaan tab Servers dan Applications di Cloudways](https://img.penasihathosting.com/2025/May/Beda-server-dan-aplikasi.webp "Perbedaan tab Servers dan Applications di Cloudways")

Mudahnya begini:

- "Servers" adalah bagian di mana Anda mengelola dan mengatur semua konfigurasi fisik server Anda, seperti memantau penggunaan sumber daya dan mengelola layanan server.

- Sedangkan "Applications" adalah tempat Anda mengelola aplikasi yang berjalan di atas server tersebut, termasuk menginstall aplikasi baru, mengatur domain dan sertifikat SSL, serta mengelola database dan file aplikasi.

Dengan pemahaman ini, sekarang kita bisa lanjutkan ke tahap menambahkan domain melalui tab "Applications" dan klik nama aplikasi yang telah Anda buat sebelumnya.

Anda akan digiring ke halaman berikut:

![Halaman aplikasi WordPress di Cloudways](https://img.penasihathosting.com/2025/May/halaman-aplikasi-1.webp "Halaman aplikasi WordPress di Cloudways")

Pada tahap ini, sebenarnya aplikasi WordPress Anda sudah berhasil terinstall, tetapi masih menggunakan nama domain sementara. Biasanya nama domain sementara nya seperti ini: wordpress-362278-1127190.cloudways.com (perhatikan yang saya kotaki warna merah pada _screenshoot_ diatas.)

Oh ya, Anda juga bisa langsung mencoba log in ke "WP ADMIN" WordPress baru Anda menggunakan domain sementara tersebut dengan memasukkan username dan password yang disediakan (perhatikan pada bagian 'Admin Panel'.)

Tapi, tentu saja Anda tidak akan menggunakan domain sementara itu bukan?

Maka Anda perlu menambahkan domain Anda sendiri dan menghubungkan nya ke server.

**BUKA MENU 'DOMAIN MANAGEMENT'**

Buka menu "Domain Management" yang ada di sidebar sebelah kiri (1), klik tombol "+ Add Domain" (2).

![Menu Domain Management di Cloudways](https://img.penasihathosting.com/2025/May/Buka-tab-domain-management-1024x645.webp "Menu Domain Management di Cloudways")

Tambahkan nama domain Anda (abaikan pilihan untuk mencentang 'add as wildcard')

![Form penambahan domain di Cloudways](https://img.penasihathosting.com/2025/May/tambahkan-nama-domain.webp "Form penambahan domain di Cloudways")

Kemudian, jadikan domain yang baru Anda tambahkan tersebut sebagai 'Make Primary'

![Tombol Make Primary domain di Cloudways](https://img.penasihathosting.com/2025/May/make-primary.webp "Tombol Make Primary domain di Cloudways")

**MENAMBAHKAN A RECORD DI DNS MANAGEMENT**

Tergantung dari provider tempat Anda membeli domain, Anda perlu menambahkan A Record pada DNS Management domain Anda.

- Type = A Record
- Host = @ (kecuali Anda ingin nama domain Anda menggunakan "www", maka isi dengan "www")
- Value = Isi dengan IP server Anda (lihat gambar di bawah 'please note')
- TTL = Biarkan saja apa adanya atau set Automatic

<figure>

![Form pengisian A Record di Cloudflare](https://img.penasihathosting.com/2025/May/cara-mengisi-A-record-di-Cloudflare.webp "Form pengisian A Record di Cloudflare")

<figcaption>

_Cara menambahkan A record di DNS management Cloudflare_

</figcaption>

</figure>

Pastikan juga Anda menambahkan A record untuk versi 'www' website Anda dan diarahkan ke IP yang sama. Nantinya, versi 'www' ini akan otomatis ter-redirect ke website Anda yang versi 'non www'.

**Bagaimana mengetahui 'Value' atau IP Server Anda?**

- Klik "Access Details" (1)
- Klik tab "SSH / SFTP"
- Klik untuk copy public IP Anda

![IP Address server Cloudways](https://img.penasihathosting.com/2025/May/IP-Address.webp "IP Address server Cloudways")

**Please Note:**

Secara default, Cloudways tidak menggunakan NS, jadi yang Anda perlu tambahkan/ganti **hanyalah A Record saja.**

Dan ganti NS domain Anda menjadi NS bawaan registar domain Anda. Misalnya, Anda menggunakan NS dari DomaiNesia, maka ganti NS nya menjadi NS bawaan DomaiNesia. Begitupun misal Anda menggunakan NS bawaan NameCheap/GoDaddy, maka ganti menjadi NS bawaan NameCheap/GoDaddy.

Sampai disini, Anda hanya perlu menunggu. Jika Anda sudah benar mengisi A Record, harusnya kurang dari 15 menit atau paling lama 24 jam domain Anda sudah terhubung dengan server.

### 3\. INSTALL SLL GRATIS UNTUK KEAMANAN

Sampai disini, Anda hanya perlu menunggu. Jika Anda telah memasukkan A Record dengan benar, domain Anda seharusnya akan terhubung ke server dalam waktu kurang dari 15 menit, atau paling lama 24 jam (dan sepengalaman saya sangat jarang hingga 24 jam).

Langkah terakhir adalah meng-install SSL untuk keamanan website Anda.

Langkah ini penting dan jangan sampai Anda lupa melakukannya, karena jika tidak, website Anda akan tidak bisa diakses / not secure seperti dibawah ini:

<figure>

![Tampilan website tanpa SSL](https://img.penasihathosting.com/2025/May/ssl-belum-terinstall.webp "Tampilan website tanpa SSL")

<figcaption>

_yang akan terjadi jika Anda lupa menginstall SSL_

</figcaption>

</figure>

**BUKA MENU 'SSL CERTIFICATE'**

Caranya:

- Buka menu "SSL Certificate" (1)
- Pilih Lets Encrypt (2)
- Masukkan email Anda (3) - Tidak perlu harus sama dengan email Cloudways
- Masukkan nama domain (4)
- Klik tombol "INSTALL CERTIFICATE" (5)

![Install SSL](https://img.penasihathosting.com/2025/May/install-ssl.webp "Install SSL")

Apabila Anda mengalami kegagalan dalam penginstalan SSL, kemungkinan besar penyebabnya adalah A Record domain Anda belum sepenuhnya terhubung ke server. Jadi, Anda hanya perlu menunggu lebih lama lagi.

**CEK STATUS HTTP CODE DAN REDIRECT DOMAIN**

Setelah Anda berhasil menginstall SSL di domain Anda, Anda bisa mengecek status HTTP code dan arah redirect canonical domain Anda menggunakan tool: [https://httpstatus.io/](https://httpstatus.io/).

Masukkan nama domain Anda (1), centang 'canonical domain check' (2) dan klik tombol "Check status"

![Form pengecekan status HTTP](https://img.penasihathosting.com/2025/May/Cek-status-http-.webp "Form pengecekan status HTTP")

Jika kode status domain Anda tidak sesuai dengan contoh pada _screenshoot_ dbawah (ini berlaku untuk penggunaan domain non 'www'), maka ada kemungkinan terjadi kesalahan saat Anda memasukkan A Record ke DNS Management domain Anda.

![Hasil test redirect map di httpstatus.io](https://img.penasihathosting.com/2025/May/cek-status--1024x312.webp "Hasil test redirect map di httpstatus.io")

Selesai.

Semoga tutorial cara install WordPress di Cloudways ini membantu. Jika Anda menemui kesulitan atau tidak yakin dengan bagian manapun dalam tutorial ini, jangan ragu untuk bertanya melalui kolom komentar yang ada dibawah.
