---
title: "Cara Membuat Website Sendiri: Panduan Lengkap"
publishDate: 2023-07-18
updateDate: 2024-05-21
category: "Tutorial"
tags:
  - "Panduan Lengkap"
  - "wordpress"
  - "tutorial"
  - "pemula"
  - "panduan-lengkap"
image: https://img.penasihathosting.com/2025/cara-membuat-website/cara-membuat-website.webp
excerpt: "Panduan lengkap cara membuat website sendiri dari nol untuk pemula. Mulai dari memilih platform, domain, hosting, hingga membangun website dengan WordPress dan Elementor."
metadata:
  title: "Cara Membuat Website Sendiri: Panduan Lengkap untuk Pemula"
  description: "Tutorial lengkap cara membuat website sendiri dari nol untuk pemula. Panduan step-by-step dengan WordPress dan Elementor, cocok untuk bisnis dan personal."
  layout: "guide-page" 
---

<PERSON><PERSON> <PERSON><PERSON> panduan cara membuat website ini untuk membantu pemula yang ingin belajar membuat website sendiri tanpa perlu menguasai coding atau mengeluarkan biaya untuk menyewa jasa web designer dan developer.

Saya yakin sebagian besar dari Anda akan berhasil.

Apa yang dibutuhkan?

- Anda hanya perlu menginvestasikan beberapa jam dari waktu Anda
- dan mengikuti panduan yang bagus.

Beberapa jam? Yes, kenyataannya, tidak ada yang bisa membuat website dalam waktu singkat. Tidak realistis bagi pemula untuk dapat membuat website dalam 30 atau 60 menit seperti yang sering dijanjikan oleh beberapa sumber.

Dan panduan bagus? Saya percaya Anda sudah berada di tempat yang tepat saat ini.

Ayo mulai dari bab 1.

## Bab 1: Memilih Platform Terbaik

Ada lebih dari 100+ platform tersedia sampai saat ini. Memilih satu diantaranya adalah keputusan pertama yang perlu Anda ambil. Saya akan pandu Anda menemukan yang terbaik: mudah dipelajari, gratis dan yang sudah memiliki komunitas yang besar.

### **Pertama-tama, apa itu platform?**

Dahulu, sekitar tahun 2004 atau 2005, [membuat website](https://penasihathosting.com/cara-membuat-website/) itu sulit. Anda perlu setidaknya belajar CSS dan HTML untuk membuat website.

Belum lagi belajar bagaimana cara men-design websitenya sebelum meng-coding nya.

Tidak mudah, membutuhkan waktu dan mungkin Anda perlu pergi kesalah satu tempat terpencil agar bisa fokus dalam mempelajarinya.

Di tahun 2024 ini, faktanya Anda bahkan tidak perlu mempelajari itu semua untuk membuat website yang dapat menambah penghasilan/penjualan Anda di dunia online, karena sebagian orang telah membuat sebuah platform yang dapat mempermudah Anda dalam membuat website.

Dengan adanya platform, Anda dapat membuat website sendiri tanpa perlu ikut pelatihan kursus.

### Apa Platform Terbaik untuk Membuat Website?

Ada banyak pilihan platform yang dapat Anda pilih untuk membuat website. Jumlahnya lebih dari seratus ketika saya mengeceknya di situs [W3Tech](https://w3techs.com/technologies/history_overview/content_management).

Menurut statistik, berikut adalah platform yang paling banyak digunakan:

![Statistik platform website terpopuler](https://img.penasihathosting.com/2025/cara-membuat-website/cms-market-share-may-2025.webp "Statistik platform website terpopuler")

Seperti yang Anda lihat dalam statistik diatas, WordPress adalah platform yang paling populer di dunia dengan jumlah market share mencapai 43%.

**Pertanyaannya:** Mengapa WordPress begitu populer? Karena WordPress...

- Mudah digunakan / user-friendly
- GRATIS dan tidak ada iklan yang akan disematkan ke website Anda
- Memiliki komunitas yang besar
- Website yang dibangun dengannya akan tampak responsive di semua perangkat termasuk handphone dan tablet

**Bagaimana dengan platform lainnya, seperti website builder: WIX, Squarespace dan Weebly?**

Mereka adalah pilihan platform yang juga bagus. Hanya saja memiliki beberapa keterbatasan, seperti:

- Tidak gratis
- Perlu memilih paket minimal yang seharga $8.5/bulan (WIX) untuk menghapus iklan
- Bandwidth dan storage yang terbatas pada paket termurahnya (WIX)
- Website yang Anda tempatkan dalam platform mereka bukan 100% milik Anda

Untuk Anda yang baru memulai, saya menyarankan menggunakan WordPress.

Saya pribadi juga menggunakan WordPress di semua website yang saya miliki (termasuk [Penasihat Hosting](https://penasihathosting.com/)) dan saya percaya WordPress juga akan cocok buat Anda.

Dan kabar baiknya: Anda akan belajar bagaimana cara membuat website sendiri menggunakan WordPress dalam panduan ini.


## Bab 2: Memilih Domain dan Hosting

Website itu dimulai dari sebuah nama domain - bukan design. Anda membutuhkan sebuah nama sebelum memulai membangun website Anda.

### Memilih Nama Domain

Website itu dimulai dari sebuah nama domain. Bukan design.

[Domain](https://hostingpedia.id/kategori/domain/) adalah nama website Anda. Sebuah alamat yang orang akan orang gunakan ketika mencoba mengunjungi website Anda melalui browser.

Misalnya, nama domain website ini adalah [penasihathosting.com](https://penasihathosting.com/).

Memiliki [nama domain yang bagus](https://penasihathosting.com/tips-memilih-nama-domain/) adalah suatu langkah awal yang sangat penting.

Apa alasannya?

Pertama karena nama domain dapat membantu Anda membangun merek yang akan menempel di benak pelanggan atau pengunjung Anda.

Kedua, nama domain dapat membantu menentukan target pasar Anda. Contohnya domain website saya: [penasihathosting.com](https://penasihathosting.com/).

Jadi, langkah ke-2 dalam [cara membuat website](https://penasihathosting.com/cara-membuat-website/) setelah memilih platform (WordPress) adalah menentukan **nama domain.**

Jika Anda masih bingung menentukan nama domain, berikut saya berikan beberapa tips yang semoga dapat mempermudah Anda mencari nama domain yang tepat:

- Jika Anda berencana membuat website untuk diri Anda sendiri, maka Anda bisa menggunakan nama Anda sebagai domainnya: **NamaAnda.com.**
- Jika membuat website untuk perusahaan, maka Anda juga bisa menggunakan nama perusahaan: **NamaPerusahaanAnda.com.**
- Jika untuk organisasi, maka Anda bisa juga menggunakan nama organisasi, misalnya **GenerasiBebasRiba.com**

Dan gunakan ekstensi domain yang populer agar website Anda terlihat lebih professional, seperti:

- .com
- .net
- .org (biasanya digunakan untuk organisasi)
- .sch.id (cocok digunakan untuk website sekolah)
- .ponpes.id (cocok digunakan untuk pesantren)
- .co.id (untuk perusahaan yang sudah memiliki izin usaha)
- .id (cocok untuk personal, tapi sekarang penggunannya sudah umum dan tidak hanya cocok untuk website personal saja)

Tips lainnya:

- Hindari trademarks atau merk dagang, misalnya nama domain: reviewhpSAMSUNG.com
- Hindari menggunakan tanda penghubung atau nomor
- Dan _keep it simple_

Oia, harga domain tidak terlalu mahal, yaitu mulai dari Rp145.000 per tahun nya.

Note: Saya akan kasih tau Anda cara mendapatkan domain gratis selama 1 tahun pada tahap selanjutnya.

### Memilih Web Hosting

[Web hosting](https://hostingpedia.id/kategori/web-hosting/) adalah sebuah server untuk menyimpan data-data website secara online. Fungsinya adalah untuk menghubungkan website Anda dengan internet.

Mungkin Anda bertanya-tanya: Apa bedanya web hosting dengan domain?

![Bedanya hosting dengan domain](https://img.penasihathosting.com/2025/May/Bedanya-hosting-dengan-domain-1-1.webp "Bedanya-hosting-dengan-domain-1")

Domain hanyalah sebuah alamat, seperti alamat rumah. Sedangkan hosting adalah rumahnya.

Tanpa adanya alamat (domain) dan rumah (hosting), orang tidak akan bisa masuk ke website Anda.

Itu sebabnya Anda membutuhkan keduanya agar website Anda bisa diakses oleh semua orang di internet, dari seluruh dunia.

Anda sudah belajar bagaimana cara memilih domain. Lalu, bagaimana [caranya memilih hosting](https://penasihathosting.com/web-host-bagus/)?

Ada lebih dari 100 provider hosting di Indonesia. Memilih satu diantara ratusan bukan pekerjaan yang mudah.

Memilih hosting adalah _milestone_ terpenting pertama yang perlu Anda lalui, karena:

- Salah-salah memilih hosting, website Anda bisa kena masalah dikemudian hari, seperti data hilang, website sering error/tidak bisa diakses, dan semacamnya
- Salah-salah memilih hosting, website Anda bisa mudah kena malware, mudah dimasuki hacker, dan semacamnya

Pada intinya, salah memilih hosting, justru tidak akan membantu Anda dalam menghasilkan penjualan, malah _cost_ yang akan bertambah karena Anda harus pindah-pindah provider hosting.

Masalah hosting ini cukup kompleks.

Memilih satu diantara banyaknya pilihan itu mudah-mudah sulit. Mudah menemukannya, karena memang ada banyak pilihan yang tersedia, tapi sulit untuk menemukan yang terbaik.

Untungnya, kami di Harun Studio telah melakukan penelitian selama lebih dari 30 bulan terhadap 22 provider hosting paling populer di Indonesia. ([baca hasil penelitian](https://penasihathosting.com/hasil-penelitian/))

Tujuannya? Salah satunya, agar orang yang baru belajar cara membuat website seperti Anda tidak salah dalam memilih hosting.

Dan rekomendasi [hosting terbaik](/) untuk pemula berdasarkan hasil penelitian kami adalah [DomaiNesia](/go/domainesia) setidaknya karena 5 faktor kuat berikut:

- Hosting atau server mereka stabil
- Kecepatannya cukup bisa diandalkan
- Supportnya bagus dan dapat diandalkan
- Fitur yang cukup banyak
- Plus penawaran harga yang cukup bersaing

### Dapatkan Hosting dan Domain di DomaiNesia

**Diskon spesial:**

- Masukkan kode promo: **BUATWEBSITE** ketika _checkout_ untuk mendapatkan diskon 5%.

**Garansi hosting:** DomaiNesia menawarkan garansi hosting selama 30 hari. Jika Anda kecewa dengan layanan mereka, Anda bisa meminta refund dan uang Anda akan kembali (dikurangi biaya domain).

**Disclosure:** Support Anda membantu website ini terus berjalan. Kami menggunakan link affiliasi, dimana kami akan menerima komisi sebesar 20 - 50% dari pembelian hosting Anda tanpa ada biaya tambahan dibebankan keapda Anda. [Baca lebih lengkap.](/advertiser-disclosure/)

#### Cara membeli hosting dan domain murah di DomaiNesia:

##### **1\. Kunjungi** [www.DomaiNesia.com](/go/kedomainesia) (link referral/affiliate)

![Kunjungi DomaiNesia](https://img.penasihathosting.com/2025/cara-membuat-website/Kunjungi-DomaiNesia-scaled-1.webp "Kunjungi DomaiNesia")

(1) Masukkan nama domain yang Anda inginkan pada form nama domain "A better web awaits.."
(2) Jika domain yang Anda pilih tersedia, klik tombol "Pesan paket hemat".

Jika tidak tersedia, silahkan mencari nama domain lain nya atau pilih ekstensi yang berbeda (.com, .id, .net, .org, dll).

##### 2\. Isi detail pembelian paket hosting

(1) Pilih paket hosting Anda. Saran saya adalah paket Super (2GB) dan Anda akan mendapatkan gratis domain selama satu tahun.
(2) Lokasi server boleh Anda pilih Singapore atau Jakarta. Keduanya bagus, tapi saya prefer Singapore.
(3) Pilih siklus pembayaran hosting. Jika Anda memilih 1 tahun, maka Anda akan mendapatkan gratis 1 domain.
(4) Pastikan bahwa nama domain Anda sudah benar. Perhatikan setiap karakternya, karena jika salah, nama domain tidak bisa di ganti jika sudah aktif
(5) Ceklis Addons: DNS Zone Manager (Gratis)
(6) Masukkan alamat email Anda untuk pembuatan account DomaiNesia
(7) Klik tombol warna hijau "Masukkan ke Troli"

![Order hosting di DomaiNesia](https://img.penasihathosting.com/2025/cara-membuat-website/Order-hosting-di-DomaiNesia.webp "Order hosting di DomaiNesia")

##### 3\. Masukkan kode promo untuk mendapatkan diskon lebih besar

Kode promo: **BUATWEBSITE**  (Diskon 35% dan berlaku untuk perpanjangan.)

Harga total yang harus Anda bayarkan jika sebelumnya Anda memilih paket "Super" harusnya adalah Rp 368.940 setelah mengaplikasikan kode promo kami.

Jika sudah benar, klik tombol "DAFTAR".

![masukkan kupon BUATWEBSITE](https://img.penasihathosting.com/2025/cara-membuat-website/masukkan-kupon-BUATWEBSITE-1024x408.webp "masukkan kupon BUATWEBSITE")

##### 4\. Isi Data Diri

Isi nama depan dan belakang, institusi, email, dan password.

![Isi detail informasi tambahan](https://img.penasihathosting.com/2025/cara-membuat-website/Isi-detail-informasi-tambahan.jpg-1024x378.webp "Isi detail informasi tambahan")

Dilanjutkan dengan mengisi alamat, nomor telepon.

Dan sebelum mengklik tombol warna hijau "Sign Up", pastikan sekali lagi bahwa semua informasi yang Anda masukkan sudah benar.

Jangan asal-asalan mengisi data diri Anda.

![Isi alamat](https://img.penasihathosting.com/2025/cara-membuat-website/Isi-alamat-.jpg-1024x899.webp "Isi alamat")

Jika sudah benar, akan muncul pemberitahuan bahwa pendaftaran telah berhasil.

Klik tombol "Selesaikan & Bayar".

![Pendaftaran berhasil](https://img.penasihathosting.com/2025/cara-membuat-website/Pendaftaran-berhasil-1024x265.webp "Pendaftaran berhasil")

## Bab 3: Menginstall WordPress di cPanel

Install WordPress di cPanel hosting itu mudah. Prosesnya engga ribet dan hanya membutuhkan waktu kurang dari 5 menit saja.

Anda sudah memiliki 2 amunisi dasar untuk membuat website, yaitu [domain](https://hostingpedia.id/kategori/domain/) dan [hosting](https://hostingpedia.id/kategori/web-hosting/).

Sekarang adalah saatnya untuk membuat website Anda online agar bisa diakses oleh semua orang di internet.

Hal pertama yang harus Anda lakukan adalah menginstal platform WordPress di hosting Anda.

Ada dua cara menginstall WordPress:

1. Cara otomatis (one click installation)
2. Cara manual

Kita akan gunakan cara yang paling mudah, yaitu menggunakan fitur _One Click Installation_ (install dengan 1x klik) di cPanel hosting.

### Menginstall WordPress Menggunakan Fitur _One-Click Installation_

Hampir semua penyedia hosting menyediakan fitur _One-Click Installation_ untuk menginstal WordPress di dalam [cPanel hosting](https://hostingpedia.id/kategori/cpanel-hosting/) atau [DirectAdmin Hosting](https://hostingpedia.id/kategori/directadmin-hosting/) atau [Plesk hosting](https://hostingpedia.id/kategori/plesk-hosting/).

Jika Anda [memilih hosting yang bagus](/web-host-bagus/), Anda akan menemukan fitur ini dalam [cPanel](/panduan-cpanel/) hosting Anda.

Pada contoh ini, saya menggunakan provider hosting [DomaiNesia](/go/domainesia).

Bagaimana jika Anda menggunakan provider hosting yang berbeda? Jangan panik, karena pengaturan nya tidak akan jauh berbeda.

#### **1\. Log in ke akun DomaiNesia Anda**

Atau klik link login berikut: [https://my.domainesia.com/signin/](https://my.domainesia.com/signin/)

Masukkan _username_ dan _password_ yang sudah Anda buat pada saat mengisi _form_ pendaftaran akun DomaiNesia sebelumnya.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Kemudian, klik bagian yang saya kotaki warna merah atau klik bagian _products_.</p>
</div>

![Klik bagian products](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-bagian-products-scaled-1.webp "Klik-bagian-products-scaled")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Lalu, klik pada tab "Access".</p>
</div>

![Klik pada tab Access](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-pada-tab-22Access22.webp "Klik-pada-tab-22Access22")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p>Dan klik login to cPanel.</p>
</div>

![Klik log in to cPanel](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-log-in-to-cPanel.webp "Klik log in to cPanel")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">4</div>
  <p>Anda sudah masuk ke cPanel. Beginilah tampilan cPanel hosting DomaiNesia Anda.</p>
</div>

![Tampilan cPanel DomaiNesia](https://img.penasihathosting.com/2025/cara-membuat-website/Tampilan-cPanel-DomaiNesia.webp "Tampilan cPanel DomaiNesia")

#### 2\. Pastikan Domain Anda Sudah Menggunakan SSL

Sebelum masuk ke langkah menginstall WordPress, ada satu hal penting yang harus Anda lakukan agar website Anda terlihat professional, yaitu menginstall SSL.

Jika menggunakan SSL, maka koneksi ke website Anda akan aman, seperti ini:

![Menggunakan SSL](https://img.penasihathosting.com/2025/cara-membuat-website/Menggunakan-SSL.webp "Menggunakan-SSL")

Jika tidak menggunakan SSL = tidak aman. Di browser Chrome, website Anda akan di labeli sebagai "Not Secure".

![tidak menggunakan SSL](https://img.penasihathosting.com/2025/cara-membuat-website/tidak-menggunakan-SSL.webp "tidak menggunakan SSL")

Anda tidak mau website Anda terlihat tidak aman seperti diatas, bukan?

Lalu, bagaimana cara nya menginstall SSL pada website baru Anda? Caranya mudah.

Perhatikan di bagian sebelah kanan atas dari cPanel Anda, apakah primary domain Anda sudah "DV Certificate" berwarna hijau?

![Perhatikan DV Ceartificate](https://img.penasihathosting.com/2025/cara-membuat-website/Perhatikan-DV-Certificate.webp "Perhatikan DV Certificate")

Pada dasarnya, di DomaiNesia SSL sudah aktif secara otomatis. Jika belum, primary domain Anda akan diberi keterangan "No Valid Certificate".

Cara memperbaiki nya:

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>klik tombol icon obeng seperti yang saya tunjukkan di bawah ini:</p>
</div>

![klik tombol icon obeng](https://img.penasihathosting.com/2025/cara-membuat-website/klik-tombol-icon-obeng.webp "klik-tombol-icon-obeng")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Kemudian, ceklis pada bagian nomor (1), dan klik tombol 'Run AutoSSL" pada bagian nomor (2).</p>
</div>

![Ikuti 2 langkah berikut](https://img.penasihathosting.com/2025/cara-membuat-website/ikuti-2-langkah-berikut.webp "Ikuti 2 langkah berikut")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p>Cek kembali di home cPanel, konfirmasi apakah primary domain Anda sudah "DV Certificate" atau belum.</p>
</div>

Jika sudah, silahkan lanjut ke langkah ketiga.

![Perhatikan DV Certificate](https://img.penasihathosting.com/2025/cara-membuat-website/Perhatikan-DV-Certificate-1024x419.webp "Perhatikan DV Certificate")

#### 3\. Redirect HTTP ke https (Untuk SEO)

Agar domain selalu menggunakan https (secure dan bagus untuk SEO), maka Anda harus melakukan redirect dari http ke https, jika tidak, maka orang lain dapat mengakses dua versi dari website Anda.

Satu yang http (not secure)Satu lagi yang https (secure/karena sudah terinstall SSL)

Karena itu, Anda perlu melakukan redirect http ke https.

Di cPanel, Anda dapat dengan mudah melakukan redirect ini dengan 1x klik saja.

Ada beberapa cara, tergantung provider hosting dan control panel yang Anda gunakan:

**1\. Jika menggunakan DomaiNesia**

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Klik Nginx pada cPanel</p>
</div>

![Klik pada bagian Nginx](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-pada-bagian-Nginx.jpg-1024x498.webp "Klik pada bagian Nginx")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Kemudian klik tab "Force https" dan klik tombol "Enable" seperti yang saya contohkan pada gambar dibawah.</p>
</div>

![redirect http ke https](https://img.penasihathosting.com/2025/cara-membuat-website/redirect-http-ke-https-1024x632.webp "redirect http ke https")

**2\. Jika menggunakan provider lain (selain DomaiNesia)**

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Klik pada bagian Domains.</p>
</div>

![Klik Domains](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-Domains.jpg-1024x287.webp "Klik Domains")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Lalu klik klik tombol yang saya arahkan pada gambar dibawah.</p>
</div>

![Redirect http to https](https://img.penasihathosting.com/2025/cara-membuat-website/Redirect-http-to-https.jpg-1024x432.webp "Redirect http to https")

#### 4\. Kembali Ke Tutorial Cara Menginstall WordPress

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Cari dan klik logo WordPress atau bila tidak ada, pilih Softaculous Apps Installer</p>
</div>

![Pilih scripts WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/Pilih-scripts-WordPress-1024x852.webp "Pilih scripts WordPress")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Klik tombol biru "Install Now"</p>
</div>

![Klik tombol Biru Install Now](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-tombol-Biru-Install-Now.png-1024x537.webp "klik-tombol-biru-install-now")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p>Kemudian, Isi informasi yang dibutuhkan. Ikuti petunjuk pada gambar dibawah ini:</p>
</div>

![Install WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/install-WordPress-di-cPanel-Hosting-1162x1536-1.webp "install-WordPress-di-cPanel-Hosting-1162x1536-1")

Jangan lupa mengisi admin email dengan email aktif Anda. Ini bisa digunakan juga untuk melakukan reset password jika Anda lupa suatu saat nanti.

Tunggu sekitar 1-3 menit hingga WordPress terinstall dengan sempurna.

Jika sudah terinstall:

![Website WordPress sudah berhasil diinstall](https://img.penasihathosting.com/2025/cara-membuat-website/Website-WordPress-sudah-berhasil-diinstall.webp "Website WordPress sudah berhasil diinstall")

### Melakukan Penyesuaian Pada Website Baru

Oke, sekarang Anda sudah berhasil menginstal WordPress. Website Anda sudah bisa diakses oleh semua orang di internet.

Anda bisa coba buka website Anda dengan mengetik nama domain Anda di browser.

Misalnya: penasihathosting.com di address bar Google Chrome.

Tetapi, saat ini website Anda masih terlihat baru dan kosong.

![Website masih kosong](https://img.penasihathosting.com/2025/cara-membuat-website/masih-kosong.png.webp "masih-kosong.webp")

Sebelum mengisi nya dengan konten dan lanjut ke bab ke-4 dari [tutorial cara membuat website](https://penasihathosting.com/cara-membuat-website/) ini, Anda perlu mengurus beberapa hal penting terlebih dahulu untuk membuat website terlihat siap.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Log in ke Dashboard WordPress Anda</strong></p>
</div>

Apabila Anda tidak yakin bagaimana caranya, ketik: http://domainanda.com/wp-admin/ (ganti "domainanda.com" dengan nama domain Anda).

![Log in ke WordPress Anda](https://img.penasihathosting.com/2025/cara-membuat-website/log-in-ke-WordPress-Anda.jpg.webp "log-in-ke-WordPress-Anda.webp")

Masukkan username atau alamat email dan password (yang Anda buat pada saat menginstal WordPress). Ketika Anda sudah log in, beginilah tampilan dashboard WordPress Anda:

![Dashboard WP](https://img.penasihathosting.com/2025/cara-membuat-website/dashboard-wp.png-1.webp "Dashboard WP")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Ganti Judul dan Tagline Website Anda</strong></p>
</div>

Judul dan tagline adalah bagian pertama yang perlu Anda sesuaikan.

Buatlah judul dan tagline yang mencerminkan isi website Anda.

Sebagai contoh, judul website ini adalah "Penasihat Hosting" dan taglinenya adalah "Review Penyedia Layanan Web Hosting".

Bagaimana caranya mengganti judul dan tagline?Pada dasboard WordPress Anda, klik **Settings** \-> **General**.

![Mengganti judul dan tagline](https://img.penasihathosting.com/2025/cara-membuat-website/Mengganti-judul-dan-tagline.jpg.webp "Mengganti judul dan tagline")

**Bingung menentukan judul dan tagline?**

Jika Anda tidak yakin mau menulis apa, berikut saya berikan 4 tips jitu yang dapat membantu Anda:

- Jika Anda bikin website untuk tujuan branding bisnis/usaha, Anda dapat menggunakan judul yang sama atau identik dengan nama domain Anda. Sebagai contoh, judul halaman Google adalah Google dan Facebook adalah Facebook.
- Gunakan deskripsi yang menggambarkan website Anda secara keseluruhan dan gabungkan dengan nama domain Anda. Sebagai contoh, judul halaman website Tokopedia adalah "Jual Beli Online Aman dan Nyaman - Tokopedia".
- Tanyakan pada diri Ada: "Apa tujuan website Anda?" Apa yang ingin Anda capai melalui website Anda?Anda dapat menggunakan tujuan atau yang ingin Anda capai sebagai judul dan tagline website Anda.
- Jika Anda membuat blog personal, Anda dapat menggunakan nama Anda sendiri sebagai judul blog dan profesi Anda sebagai tagline.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p><strong>Pastikan Website Anda Search Engine Friendly (SEO)</strong></p>
</div>

Ada tiga poin penting yang perlu Anda pertimbangkan ketika menyiapkan website Anda agar search engine friendly (bagus SEO nya).

Untungnya, ketiga poin ini sangat mudah dilakukan di WordPress.

**1\. Jangan gunakan format yang panjang dan tidak penting pada struktur URL Anda.**

Yang dimaksud struktur URL itu adalah yang seperti ini:

_http://domainanda.com/2017/02/01/category/cara-membuat-blog_

Sebenarnya, engga salah menggunakan struktur URL tersebut, tapi mesin pencari tidak menyukai struktur yang panjang seperti contoh diatas.

Sebaiknya gunakan struktur URL yang pendek dan hindari penggunaan tanggal.

_http://domainanda.com/cara-membuat-website_

Bagaimana caranya agar struktur URL website Anda dapat seperti contoh yang benar diatas?

Klik **Settings** > **Permalinks**, lalu pilih 'post name' dan simpan pengaturan Anda.

![pilih permalinks post name](https://img.penasihathosting.com/2025/cara-membuat-website/pilih-permalinks-post-name.png.webp "pilih-permalinks-post-name.webp")

**2\. Jangan blok mesin pencari untuk membaca dan me-crawl website Anda.**

Klik **Settings** > **Reading**.

_Scrool_ mouse Anda kebagian paling bawah.

Pastikan bahwa Anda TIDAK mencentang search engine visibility, jika Anda mencentangnya, maka website Anda sama sekali tidak akan terindex oleh mesin pencarian Google, Yahoo, Bing dan lainnya.

![jangan centang bagian ini](https://img.penasihathosting.com/2025/cara-membuat-website/jangan-centang-bagian-ini.png.webp "jangan-centang-bagian-ini.webp")

**3\. Jangan biarkan komentar spam mudah masuk ke website Anda**

Faktanya, ada jutaan orang di internet yang gemar meninggalkan komentar spam untuk mengiklankan produk atau jasa mereka.

Klik **Settings** \> **Discussion** dan pastikan Anda mencentang kedua pilihan dibawah ini:

![moderasi komentar](https://img.penasihathosting.com/2025/cara-membuat-website/moderasi-komentar.png.webp "moderasi-komentar.webp")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">4</div>
  <p><strong>Hapus konten default WordPress \[Seperti: Hello World, dan Sample Page\]</strong></p>
</div>

Konten-konten yang perlu Anda hapus:

- Hapus "Hello World" di bagian "Posts"
- Hapus "Sample Page" di bagian "Pages"

Arahkan kursor Anda ke judul artikel agar pilihan Edit | Quick Edit | **Trash** | View muncul.

![hapus post default](https://img.penasihathosting.com/2025/cara-membuat-website/hapus-posts.png.webp "hapus-posts.webp")

## Bab 4: Memilih Tema WordPress

Buat informasi saja, WordPress memiliki 6.000+ tema gratis yang telah dirancang professional dalam database mereka. Anda bisa menjelajahinya seperti seorang anak kecil di dalam toko permen.

Website Anda masih kosong, Anda perlu membuat beberapa halaman, seperti halaman depan (home), tentang, kontak, dsb. Juga, Anda perlu memilih tema website. Di langkah 4 ini, saya juga akan pandu Anda memilih tema WordPres terbaik untuk apapun kebutuhan Anda.

### Bagaimana Caranya Memilih Tema?

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Akses tema gratis WordPress</strong></p>
</div>

Klik **Appearance** \> **Themes** dan klik tombol "Add New" untuk mengakses tema gratis yang ada di WordPress.

![Cari-tema-gratis-di-WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/Cari-tema-gratis-di-WordPress.png.webp "Cari-tema-gratis-di-WordPress.webp")

Dan boom! Anda memiliki akses ke lebih dari 6.000+ tema gratis WordPress!

Sekarang Anda bisa menelusuri berbagai pilihan tema seperti seorang anak kecil di toko permen.

![pilih tema WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/pilih-tema-WordPress.png.webp "pilih-tema-WordPress.webp")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Pilih tema WordPress</strong></p>
</div>

Sampai disini, saya perlu memberitahu Anda bahwa sebelum merampungkan panduan cara membuat website WordPress untuk pemula ini, saya sudah mencoba berbagai tema WordPress populer yang menurut saya mudah untuk digunakan bagi siapapun dan apapun tujuannya.

Tema-tema populer seperti:

- Astra (1 juta+ telah terinstall)
- OceanWP (700rb+)
- Kadence (100rb+)
- GeneratePress (400rb+)
- Blocksy (50rb+)
- Dan Neve (300rb+)

Setelah mencoba semuanya, saya sampai kepada kesimpulan bahwa, yang:

- Gratis
- Mudah digunakan untuk pemula
- Mudah dikustomisasi
- Cepat dan SEO friendly

Adalah **Astra**.

**_Note:_**

- Anda bisa memilih tema lainnya jika Anda mau. Tapi, rekomendasi saya tidak akan jauh-jauh dari 6 tema yang telah saya sebutkan diatas.
- Semua tema yang saya sebutkan diatas bentuknya adalah freemium. Artinya, Anda bisa menggunakannya secara free/gratis, tapi jika Anda ingin fitur yang lebih banyak, Anda harus membayar biaya untuk upgrade ke premiumnya.
- Astra dalam pengujian saya adalah tema gratis yang _powerfull_. Anda bahkan tidak perlu upgrade ke premium untuk membuat website yang bagus.

Anda bisa menambahkan tema Astra langsung dari dashboard WordPress Anda.

Ketik **Astra** pada kolom pencarian disebelah kanan atas.

![install tema Astra](https://img.penasihathosting.com/2025/cara-membuat-website/install-tema-Astra-1024x498.png.webp "install-tema-Astra-1024x498.webp")

Kemudian arahkan kursor Anda ke thumbnail Astra dan tombol install akan muncul.

Klik tombol "install" lalu "Activate".

Dan begitu Anda klik tombol "Activate", maka tema sudah aktif.

_Great job_!

Jika sudah Anda install, maka sekarang saatnya membuat koten halaman di website Anda. Konten di halaman-halaman seperti:

- Home
- Tentang Kami
- Layanan Kami
- Kontak Kami
- Blog
- Testimonial

Atau apapun halaman yang Anda butuhkan.

### Bagaimana membuat halaman baru dan menambahkan artikel?

Faktanya, menulis konten di WordPress itu mudah.

Tapi, ada yang perlu Anda ketahui sebelumnya, yaitu WordPress punya 2 tempat yang bisa Anda gunakan untuk menambahkan konten.

Ada Posts dan ada Pages.

**Anda mungkin bertanya-bertanya:**

"Mengapa ada 2 tempat? Lalu apa bedanya antara Posts dan Pages? (Penting untuk diketahui sebelum Anda lanjut ke tahap selanjutnya, jadi saya sarankan untuk tidak meloncati bagian ini).

![post dan pages di WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/post-dan-pages-di-WordPress.png.webp "post-dan-pages-di-WordPress.webp")

Post dan Pages memang terlihat mirip, namun memiliki fungsi yang berbeda.

- **Post** digunakan untuk menerbitkan artikel yang sifatnya rutin di website Anda.

    Sebagai contoh, jika Anda ingin menerbitkan artikel seminggu sekali atau seminggu dua kali, maka sebaiknya Anda menulisnya dalam bentuk Post. Selain itu, Post juga dapat dibagi berdasarkan kategori.

    Katakanlah Anda memiliki website tentang berita, dan Anda mempunyai artikel-artikel tentang berita teknologi, berita politik, berita olahraga memanah, berita pendidikan, dan lain-lain.

    Maka Anda dapat memasukkannya kedalam kategori agar lebih mudah dicari oleh pengunjung. Sebagai contoh, saya memasukkan artikel cara membuat website menggunakan WordPress ini dalam bentuk Post.

- Sedangkan **Pages** sifatnya adalah tetap atau statis.

    Halaman-halaman yang dibuat dalam bentu Pages tidak dapat dimasukkan dalam kategori. Contoh halaman yang biasa dibuat menggunakan Pages adalah halaman home, tentang kami, kontak kami, testimonial, produk kami, visi/misi, dsb.

#### Cara menambahkan Pages

Jika Anda ingin membuat halaman 'Tentang Kami', 'Kontak Kami', 'Visi/Misi', dsb maka Anda harus menulisnya dalam bentuk **Pages**.

Caranya sebagai berikut:

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Di sidebar sebelah kiri pada layar Anda, klik <strong>Pages</strong> \-> <strong>Add New</strong>.</p>
</div>

![klik-pages-dan-add-new-untuk-menambahkan-pages-baru](https://img.penasihathosting.com/2025/cara-membuat-website/klik-pages-dan-add-new-untuk-menambahkan-pages-baru.png.webp "klik-pages-dan-add-new-untuk-menambahkan-pages-baru")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Dan ini adalah tampilan WordPress pages editor.</p>
</div>

![WordPress Editor](https://img.penasihathosting.com/2025/cara-membuat-website/WordPress-editor-1024x558.png.webp "WordPress-editor-1024x558.webp")

Sampai disini, Anda hanya perlu mengisi judul halaman saja kemudian langsung klik tombol "Publish".

(Spoiler: Karena saya akan ajak Anda untuk mendesain halaman website menggunakan plugin Elementor(?) nanti pada bab selanjutnya).

Jadi, biarkan saja apa adanya dulu kosong seperti ini.

![Buat halaman kosong terlebih dahulu](https://img.penasihathosting.com/2025/cara-membuat-website/Buat-halaman-kosong-terlebih-dahulu-1024x547.webp "Buat halaman kosong terlebih dahulu")

Jika sudah klik publish, Anda bisa mengklik tombol logo WordPress seperti gambar dibawah ini untuk kembali ke halaman dashboard WordPress.

![Klik untuk kembali ke halaman dashboard WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-untuk-kembali-ke-halaman-dashboard-WordPress.webp "Klik untuk kembali ke halaman dashboard WordPress")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p>Jika Anda ingin membuat beberapa halaman.</p>
</div>

Katakanlah: Halaman depan (home), halaman tentang kami, kontak kami, blog (halaman yang berisi artikel-artikel yang dibuat dalam bentuk post), dsb.. maka buatlah halaman tersebut dan biarkan kosong apa adanya.

![halaman-di-website-WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/halaman-di-website-WordPress.png.webp "halaman-di-website-WordPress")

#### Dan Bagaimana Cara Menambahkan Pages Kedalam Menu?

Sebelum kita mendesain halaman-halaman yang sudah dibuat barusan, sebaiknya Anda tambahkan halaman-halaman tersebut ke dalam menu website.

Perhatikan menu pada gambar website dibawah ini:

![menambahkan menu website](https://img.penasihathosting.com/2025/cara-membuat-website/Menambahkan-menu-website.png.webp "Menambahkan-menu-website.webp")

Caranya juga mudah.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Klik <strong>Appearance</strong> > <strong>Menu</strong></p>
</div>

![Menu di WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/menu.webp "menu")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Ikuti langkah demi langkah nya dibawah ini untuk membuat menu website Anda.</p>
</div>

![membuat menu navigasi](https://img.penasihathosting.com/2025/cara-membuat-website/membuat-menu-navigasi.png.webp "membuat-menu-navigasi.webp")

1. Buat nama menu website Anda. Boleh apa saja, karena tidak akan mempengaruhi apapun
2. Klik tombol "Save Menu"
3. Tambahkan halaman yang sudah Anda buat sebelum nya ke dalam struktur menu dengan men-centang/ceklis nya. Misalnya, Anda ingin menambahkan halaman Home, Tentang dan Kontak. Anda juga bisa menambahkan menu di dalam _posts_ atau membuat _custom links._
4. Kemudian klik tombol 'Add to Menu'. Maka semua halaman yang Anda centang akan muncul di struktur menu.
5. Atur menu sesuai keinginan dengan cara _drag & drop_. Ingat bahwa Anda juga bisa membuat sub menu seperti contoh saya diatas.
6. Centang 'Header Menu 1"
7. Terakhir, klik tombol 'Save Menu' kembali (untuk kedua kalinya)

Sampai pada tahap ini, Anda bisa cek apakah menu website yang baru Anda buat sudah tampil di website Anda.

## Bab 5: Melakukan Penyesuaian pada Tema

Astra merupakan tema yang mudah dikustomisasi dan salah satu dari tiga tema favorit saya selain GeneratePress dan Blocksy.

Sebelum lanjut ke langkah terakhir dari panduan [cara membuat website](https://penasihathosting.com/cara-membuat-website/), yaitu mendesain halaman kosong (home, tentang, kontak, dsb) yang sudah Anda buat sebelumnya, Anda perlu melakukan sedikit penyesuaian pada tema Astra terlebih dahulu, agar bisa sesuai seperti yang Anda inginkan.

### 1. Membuat Halaman Statis di Homepage

Halaman statis adalah halaman yang tidak pernah berubah.

Jadi, "statis" akan menampilkan konten atau artikel yang sama setiap kali seseorang datang ke website Anda.

Contohnya adalah halaman depan ([harunstudio.com](https://harunstudio.com/)), kapanpun Anda mengunjunginya dan seberapa seringpun saya menulis artikel baru, halaman statis ini tidak akan pernah berubah.

![Homepage Harun Studio](https://img.penasihathosting.com/2025/cara-membuat-website/Homepage-Harun-Studio.webp "Homepage-Harun-Studio")

**Bagaimana cara membuat halaman statis?**

Buka **appearance** \> **Customize.**

Klik "Homepage Settings" pada bagian sebelah kiri Customize.

Lalu atur sebagai "A Static page", kemudian pilih Homepage = Home (Halaman depan kosong yang sudah Anda buat sebelumnya).

dan Posts page = Blog.

![cara membuat halaman statis di WordPress](https://img.penasihathosting.com/2025/cara-membuat-website/Cara-membuat-halaman-statis-di-WordPress.png-1024x751.webp "Cara-membuat-halaman-statis-di-WordPress")

Post Page ini sifatnya opsional, namun jika Anda ingin menulis artikel yang sifatnya rutin maka Anda perlu membuat halaman Post page yang menampilkan artikel-artikel yang sudah Anda terbitkan di website.

Jika Anda belum membuatnya, Anda bisa kembali untuk membuat halaman kosong baru dan beri judul sebagai **Blog**.

### 2. Kustomisasi Global Website

Ini termasuk bagian yang paling terpenting dari semua rangkaian proses cara membuat website, yaitu menentukan dari awal global website Anda.

Mulai dari apa font yang Anda gunakan, warna hingga layout website Anda.

Kita akan mulai dari menentukan typography.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Menentukan Typography</strong></p>
</div>

Buka **Global** > **Typography**

Secara default, font tema Astra yang Anda gunakan menggunakan system font. Secara performance, system font adalah font yang paling cepat.

Tapi, jika Anda ingin menggantinya yang lebih fancy, Anda bisa memilih font lainnya.

(1) Anda bisa memilih satu diantara enam font yang telah direkomendasikan oleh Astra pada bagian "Presets"
(2) Atau memilih font sendiri dari daftar font yang ada di bagian "Body Font Family"
(3) Tentukan size body font Anda. Saran saya jangan gunakan font lebih kecil dari 16.

![Menentukan Typography](https://img.penasihathosting.com/2025/cara-membuat-website/Typography-1024x912.png.webp "Typography-1024x912.webp")

Untuk pengaturan yang lain, saya rasa tidak perlu diubah-ubah dahulu.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Menentukan Warna Website</strong></p>
</div>

Buka **Global** > **Colors**

Salah satu ciri website yang bagus adalah konsisten terhadap warna. Saya menyarankan untuk tidak menggunakan kombinasi warna yang banyak, misalnya lebih dari 6 warna.

Lebih sedikit lebih baik.

Misalnya, Facebook identik dengan warna biru. Tokopedia identik dengan hijau. Youtube identik dengan warna merah.

Secara default, Astra menggunakan kombinasi warna biru, hitam dan putih. Anda bisa memilih warna lainnya.

Jika Anda bingung memilih kombinasi warna, Anda bisa mengunjungi situs berikut untuk mencari inspirasi warna:

- [Coolors.co](https://coolors.co/palettes/trending)
- [Canva](https://www.canva.com/colors/color-palette-generator/)

![Menentukan Warna Website](https://img.penasihathosting.com/2025/cara-membuat-website/Global-Palette-1024x978.png.webp "Global-Palette-1024x978.webp")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p><strong>Menentukan Container</strong></p>
</div>

Buka **Global** > **Container**

(1) Container width adalah lebar website Anda. Semakin tinggi nilainya maka semakin lebar website Anda. Saran saya biarkan default saja (1200px)
(2) Ini adalah model layout secara default. Jika Anda memilih _contain boxed_, maka secara default semua layout, baik page dan blog akan memiliki layout yang _contained boxed._
(3) Biasanya halaman Pages layoutnya full width, tapi saya serahkan kepada Anda bentuk layout nya sesuai dengan keinginan Anda.

![Menentukan Container](https://img.penasihathosting.com/2025/cara-membuat-website/Menentukan-container-1024x817.png.webp "Menentukan-container-1024x817.webp")

### 3. Kustomisasi Header

Jika Anda tidak yakin mana yang disebut header, perhatikan gambar dibawah ini:

![Kustomisasi Header](https://img.penasihathosting.com/2025/cara-membuat-website/Header-website.png-1024x460.webp)

Sampai disini, Anda mungkin ingin mengganti nama website Anda menjadi gambar logo website Anda atau mengganti nya dengan nama lain.

Anda mungkin juga mau mengganti warna _background header_ dari putih menjadi hitam, atau ingin merubah posisinya berada di sebelah kanan, kiri, atau tengah, dsb.

Kita akan mencoba satu per satu fungsi **Header Builder** mulai dari sini.

Anda bisa berkreasi sebebas yang Anda mau.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Menambahkan Logo Website</strong></p>
</div>

Ini sifatnya masih optional.

Jika Anda sudah memiliki logo website, Anda bisa mengganti logo text dari logo berbentuk text menjadi logo bergambar.

Caranya buka **Header Builder** > dan klik **Site Title & Logo** seperti yang saya tunjukkan dalam gambar dibawah ini:

![mengganti site title dan logo](https://img.penasihathosting.com/2025/cara-membuat-website/Mengganti-site-title-dan-logo.png.webp "Mengganti-site-title-dan-logo.webp")

Kemudian ikuti langkah-langkah berikut:

![kustomisasi logo web](https://img.penasihathosting.com/2025/cara-membuat-website/Kustomisasi-logo-website-768x1451.png-542x1024.webp "Kustomisasi logo website")

(1) Klik 'Select Logo' untuk upload logo Anda
(2) Atur lebar logo Anda. Misalnya 150, 200, dll..
(3) Anda mungkin ingin menyembunyikan judul text website Anda
(4) Jika Anda ingin menggunakan logo text, Anda bisa merubah text nya dengan nama lain, misal My Blog
(5) Jika Anda ingin mensejajarkan logo gambar dengan logo text Anda
(6) Jika Anda ingin menggunakan tagline

Jika sudah selesai klik tombol "Publish" berwarna biru yang terletak di bagian kiri atas layar Anda.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Merubah warna background navigation</strong></p>
</div>

Secara default, warna background header tema Astra adalah putih.

Mungkin Anda ingin merubahnya menjadi warna merah, hijau, biru atau apapun warna yang Anda inginkan.

Buka **Header >** Arahkan kursor Anda ke bagian logo seperti gambar dibawah dan klik **Main Row**

![merubah design logo](https://img.penasihathosting.com/2025/cara-membuat-website/merubah-warna-background-header-1024x537.png.webp "merubah-warna-background-header-1024x537.webp")

Klik tab "Design" (yang saya lingkari warna merah).

Kemudian klik icon background dan pilih warna background Anda.

![merubah warna background](https://img.penasihathosting.com/2025/cara-membuat-website/Mengganti-warna-background-1024x964.png.webp "Mengganti-warna-background-1024x964.webp")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p><strong>Merubah posisi logo dan menambahkan element di dalam header</strong></p>
</div>

Untuk merubah-rubah posisi suatu element, baik logo, menu, search dan lain-lain itu sangat mudah dilakukan di tema Astra.

Anda bisa menambahkan apapun di dalam header Anda, seperti button, kolom pencarian (search), secondary menu, dan lain-lain.

Sampai disini, Anda bebas meng-explore bagian-bagian lainnya dari tema Astra Anda.

Namun, pastikan sebelum lanjut ke bab terakhir, Anda mengklik tombol "Publish" yang ada di sebelah kiri atas agar semua perubahan yang Anda lakukan tersimpan.

## Bab 6: Membangun Halaman Website dengan Elementor

Langkah 6 merupakan inti dari [panduan cara membuat website](https://penasihathosting.com/cara-membuat-website/) dengan WordPress ini. Saya ucapkan selamat karena Anda telah sampai disini. Great job! Yang bisa saya katakan adalah langkah terakhi ini adalah yang paling menyenangkan diantara semuanya.

Masih ingat dengan halaman-halaman kosong yang Anda buat pada langkah sebelumnya?

Sekarang saatnya Anda membangun halaman-halaman kosong tersebut.

### Membangun Halaman Dengan Plugin Page Builder

Ada dua cara untuk membangun halaman di WordPress.

**Pertama**, cara yang membutuhkan pengetahuan coding: CSS, HTML, PHP dan JavaScript (cara tersulit).

**Kedua**, menggunakan cara yang mudah (drag & drop) -> cara yang akan Anda gunakan dalam panduan ini.

Masih ingat bagaimana Anda menggunakan fitur drag & drop ketika melakukan kustomisasi pada header website Anda?

Mudah bukan? Kira-kira seperti itulah cara bagaimana Anda akan membangun website membangun plugin Page Builder.

Atau jika sebelumnya Anda telah mencoba platform website builder seperti WIX dan Squarespace, maka Anda juga tidak akan merasa asing ketika mencoba plugin page builder di WordPress.

Faktanya, saya pun dalam membangun panduan cara membuat website dari nol ini juga menggunakan plugin page builder (drag & drop).

Saya bahkan tidak punya latar belakang IT dan saya percaya Anda juga pasti bisa melakukannya.

### Memilih Page Builder yang Newbie-Friendly

Sama seperti ketika memilih tema Astra, sebelum memilih plugin Page Builder, saya juga sudah mencoba berbagai pilihan populer, seperti Elementor, Beaver, dan Brizy. Tidak lupa saya juga mencoba Gutenberg Editor seperti Kadence Block.

Setelah menghabiskan berjam-jam mencoba berbagai page builder, saya sampai pada kesimpulan dimana plugin Page Builder Elementor adalah yang paling tepat untuk pemula, karena dua alasan:

- Mudah digunakan untuk pemula sekalipun (User-Friendly)
- Versi gratisnya sudah cukup _powerfull_ dan dilengkapi denagn fitur-fitur yang banyak
- Ada banyak plugin pendukung gratis yang bisa Anda manfaatkan untuk menambah fungsionalitas

![Elementor Page Builder](https://img.penasihathosting.com/2025/cara-membuat-website/Elementor-Page-Builder.png-1024x334.webp "Elementor Page Builder")

Saya percaya Anda akan menyukai bagaimana membangun halaman-halaman website Anda menggunakan Elementor.

**Faktanya, Elementor Page Builder adalah plugin yang sangat populer.**

Saat ini sudah di install lebih dari 5 juta kali dan mendapatkan lebih dari 5.000 rating 5. Anda menggunakan plugin yang tepat.

### Untuk memulainya, silahkan ikuti dua langkah beirkut ini:

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Install plugin Elementor</strong></p>
</div>

Buka **Plugins** > **Add New** > Cari "Elementor" dalam kotak pencarian.

Kemudian klik tombol "Install Now" dan "Activate" hingga muncul pesan "Plugin Activated".

![Install-plugin-Elementor](https://img.penasihathosting.com/2025/cara-membuat-website/Install-plugin-Elementor.png-1024x509.webp "Install-plugin-Elementor")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Install plugin pendukung</strong></p>
</div>

Anda membutuhkan plugin pendukung untuk Elementor. Gunanya adalah untuk menambah fungsi pada plguin Elementor yang Anda gunakan menjadi lebih banyak, sehingga memudahkan Anda dalam membuat website.

Dari hasil pengujian beberapa plugin pendukung gratis terbaik untuk Elementor, saya sampai pada kesimpulan bahwa plugin **Starter Templates dari Brainstorm Force** adalah yang paling bagus.

Plugin Starter Templates ini adalah plugin yang dibuat oleh tim yang sama dengan yang membuat tema Astra.

Jadi, kombinasi antara tema **Astra** + plugin page builder **Elementor** + plugin pendukung **Starter Templates** adalah pilihan yang tepat dan merupakan yang terbaik berdasarkan pengujian yang saya lakukan.

Buka bagian **Plugins** kembali > **Add New** > Cari "Starter Templates" dalam kotak pencarian.

Kemudian klik tombol "Install Now" dan "Activate" hingga muncul pesan "Plugin Activated".

![plugin starter templates](https://img.penasihathosting.com/2025/cara-membuat-website/plugin-starter-templates.png.webp "plugin-starter-templates.webp")

### Membangun Halaman Depan (Home)

#### 1. Persiapan

Buka **Pages**.

Lalu Edit halaman depan Anda, dalam contoh ini saya memberikan judul halaman depan sebagai Home.

![edit-halaman-depan](https://img.penasihathosting.com/2025/cara-membuat-website/edit-halaman-depan.png.webp "edit-halaman-depan")

Kemudian, ikuti langkah-langkah pengaturan berikut untuk mempersiapkan halaman Anda sebelum meng-edit nya dengan plugin Elementor.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Klik icon Astra yang terletak di sebelah atas kanan dari layar Anda</p>
</div>

![klik icon Astra](https://img.penasihathosting.com/2025/cara-membuat-website/klik-icon-Astra.png.webp "klik icon Astra")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Atur pengaturan halamannya sebagai berikut:</p>
</div>

(2) Ubah Sidebar menjadi **No Sidebar**
(3) Ubah Content Layoutnya menjadi **Full Width / Stretched**
(4) Dan pilih **Disable Title**

![pengaturan halamannya](https://img.penasihathosting.com/2025/cara-membuat-website/Pengaturan-halaman-nya.png-807x1024.webp "Pengaturan halaman nya")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p>(5) Klik tombol Update<br />(6) Kemudian, klik tombol warna biru "Edit with Elementor"</p>
</div>

![Edit with Elementor](https://img.penasihathosting.com/2025/cara-membuat-website/edit-with-Elementor-1024x556.png.webp "edit with Elementor")

Anda akan digiring ke halaman Elementor Page Builder.

Disinilah tempat Anda membangun halaman-halaman pada website WordPress Anda.

![Membangun halaman dengan Elementor](https://img.penasihathosting.com/2025/cara-membuat-website/Membangun-halaman-dengan-Elementor-1024x730.png.webp "Membangun-halaman-dengan-Elementor-1024x730.webp")

#### 2. Mengenal User Interface Elementor

Seperti yang Anda lihat pada gambar dibawah, terdapat 2 bagian: kiri (bagian berwarna hijau) dan kanan (bagian berwarna merah) yang agak-agak mirip seperti fitur **Customizer** yang sudah Anda gunakan sebelumnya.

![Mengenal User Interface Elementor](https://img.penasihathosting.com/2025/cara-membuat-website/mengenal-user-interface-Elementort.png.webp "mengenal-user-interface-Elementort.webp")

Di bagian kiri terdapat widget-widget yang bisa Anda gunakan, seperti:

- Widget heading untuk membuat Judul halaman
- Widget image untuk menambahkan gambar
- Widget button untuk membuat tombol
- Widget icon untuk menambahkan icon, dan lain-lain

Sedangkan di bagian kanan adalah halaman yang akan Anda bangun. Cara menambahkan widget ke dalam halaman adalah dengan cara _drag & drop_. Sama seperti ketika Anda menggunakan header dan footer builder dari tema Astra sebelumnya.

#### 3. Membangun Halaman menggunakan Elementor

Mulai dari sini, Anda mungkin akan menghabiskan lebih banyak waktu membangun halaman menggunakan Elementor Page Builder.

Bagian ini menurut saya adalah yang paling menyenangkan, karena caranya cukup mudah, dan ada cukup banyak template dan block gratis yang disediakan oleh Elementor untuk membantu Anda dalam membangun halaman.

Ohiya, ada dua cara membangun halaman:

**Pertama** dan cara termudah adalah menggunakan template jadi, Anda cukup melakukan modifikasi saja.

**Kedua**, mendesain halaman dari nol. Ini bukanlah cara yang bagus buat pemula.

Jadi, di dalam panduan ini saya akan pandu Anda menggunakan cara pertama, yaitu menggunakan template yang sudah ada dan memodifikasinya.

##### 3.1 Menggunakan template yang Sudah Ada

Ikuti langkah-langkah berikut:

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p>Klik icon icon seperti yang ditunjukkan dalam screenshoot dibawah ini.</p>
</div>

![klik icon starter templates](https://img.penasihathosting.com/2025/cara-membuat-website/klik-icon-starter-templates.png.webp "klik-icon-starter-templates.webp")

Kemudian akan muncul.. Kita akan sebut ini sebagai **Template Library**, yaitu sebuah tempat dimana Anda bisa mencari template, baik itu template pages (sebuah template jadi) dan blocks (template per bagian).

![Template Library](https://img.penasihathosting.com/2025/cara-membuat-website/template-library-1536x833.png-1024x555.webp "template-library-1536x833.png-1024x555.webp")

Template library ini disediakan oleh plugin **Starter Templates** yang telah Anda instal sebelumnya.

Mungkin muncul pertanyaan di benak Anda:

Mengapa tidak menggunakan template yang telah disediakan langsung oleh Elementor saja?

Karena tidak banyak pilihan yang gratis. Saya sudah mengeceknya dan mereka seperti memaksa Anda untuk upgrade ke versi PRO untuk menggunakan semua template yang mereka buat.

Itulah alasan terbesar saya mengapa memilih untuk menggunakan plugin pendukung seperti Starter Templates, yaitu untuk memudahkan Anda ketika sedang membangun halaman website.

Template library dari Starter Templates memiliki dua buah template, yaitu yang bentuknya **Pages** dan yang bentuknya **Blocks**.

Apa bedanya template pages dan blocks?

Pages adalah template jadi. Lengkap. Biasanya terdiri dari kumpulan dari bagian:

- Hero
- Feature
- Price
- Testimonials
- Team

Sementara blocks adalah bagian-bagian dari template jadi diatas.

Misalnya, Hero adalah block. Feature adalah block. Testimonials adalah block.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p>Kembali ke template library tadi. Cari template yang Anda sukai atau yang cocok dengan kebutuhan website Anda.</p>
</div>

Pertama-tama, Anda bisa filter pencarian dengan hanya menampilkan template yang free alias gratis saja.

![Filter template](https://img.penasihathosting.com/2025/cara-membuat-website/filter-template-768x618.png.webp "Filter template")

> Per 5 Januari 2022 ketika saya sedang meng-update panduan cara membuat website dengan Elementor ini, saya hitung ada sekitar 99 template pages gratis yang tersedia.

Atau Anda juga bisa mencari dengan memasukkan keyword tertentu di kotak pencarian, seperti: business, organic, clean, cv, dan lain-lain.

![Cari menggunakan keyword](https://img.penasihathosting.com/2025/cara-membuat-website/Cari-menggunakan-keyword.png.webp "Cari menggunakan keyword")

Misalnya, dalam contoh panduan cara membuat website ini, saya menggunakan template "Love Nature".

_**Note:** Anda bisa memilih template yang lainnya, tidak perlu sama dengan ini. Carilah template yang sekiranya cocok dengan kebutuhan website Anda._

Kemudian, klik gambarnya.

![Memilih template yang cocok](https://img.penasihathosting.com/2025/cara-membuat-website/Memilih-template-yang-cocok-1024x708.png.webp "Memilih template yang cocok")

Akan muncul beberapa pilihan dari template "Love Nature". Karena saat ini Anda sedang membangun halaman home, maka klik template yang "Home".

![preview templates](https://img.penasihathosting.com/2025/cara-membuat-website/preview-templates-1024x478.png.webp "preview templates")

Sebelum template dimasukkan ke dalam halaman home, Anda bisa melihat terlebih dahulu layout penuh dari template home yang Anda pilih tadi.

![preview template sebelum dimasukkan ke halaman](https://img.penasihathosting.com/2025/cara-membuat-website/preview-template-sebelum-dimasukkan-ke-halaman-1024x565.png.webp "preview template sebelum dimasukkan ke halaman")

#### 4. Memodifikasi Template

Nah, sekarang Anda sudah memiliki template dan Anda mungkin sudah siap untuk melakukan modifikasi dari template yang Anda pilih.

Saya akan pandu Anda untuk memodifikasi beberapa elemen penting yang menurut saya paling Anda butuhkan:

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">1</div>
  <p><strong>Membuat layout section menjadi full width</strong></p>
</div>

Ketika Anda memasukkan template ke halaman, Anda mungkin menyadari bahwa section bagian atas (istilahnya dalam web design adalah "Hero") atau section-section lainnya memiliki lebar yang tidak full seperti yang tampil dalam screenshoot dibawah:

![Menggunakan template Love Nature dari Starter Templates](https://img.penasihathosting.com/2025/cara-membuat-website/Menggunakan-template-Love-Nature-dari-Starter-Templates-1024x663.png.webp "Menggunakan template Love Nature dari Starter Templates")

Anda bisa membuat lebarnya menjadi full atau full width dengan mengaturnya menjadi full width.

Caranya mudah. Arahkan kursor Anda ke section yang ingin Anda jadikan full width. Perhatikan akan muncul garis kotak berwarna biru. Pada bagian atasnya, ada tiga buah tombol icon yang berdampingan satu sama lain.

![tiga buah icon](https://img.penasihathosting.com/2025/cara-membuat-website/tiga-buah-icon.png.webp "tiga buah icon")

- Icon bertanda plus berfungsi untuk menambah section baru
- icon bertanda enam buah titik untuk melakukan editing pada section
- Dan icon bertanda "X" berfungsi untuk menghapus section

Nah, klik pada icon bertanda enam buah titik yang ada di tengah dan akan muncul pengaturan pada section tersebut disebelah kiri layar Anda.

Kemudian, aktfkan atau klik **full width section** pada _toggle_ seperti yang saya tunjukkan dibawah ini:

![membuat section menjadi full width](https://img.penasihathosting.com/2025/cara-membuat-website/membuat-section-menjadi-full-width-1024x664.png.webp "membuat section menjadi full width")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">2</div>
  <p><strong>Merubah Header Menjadi Transparan</strong></p>
</div>

Ini sifatnya optional dan tergantung dari selera atau kebutuhan design Anda. Tapi, jika Anda ingin membuat header Anda menjadi transparan seperti dibawah ini, maka ini juga mudah dilakukan.

![Membuat header menjadi transparan](https://img.penasihathosting.com/2025/cara-membuat-website/Membuat-header-menjadi-transparan-1024x588.png.webp "Membuat header menjadi transparan")

Pengaturannya ada pada tema Astra, jadi Anda mungkin perlu membuka tab baru pada browser Anda dan pada dashboard WordPress Anda, buka **Appearance** > **Customize**.

Buka **Header Builder** > Lalu klik **Transparent Header**

![Pengaturan-transparan-header](https://img.penasihathosting.com/2025/cara-membuat-website/Pengaturan-transparan-header.png.webp "Pengaturan-transparan-header")

Lalu, aktifkan **Enable on Complete Website** seperti yang saya arahkan dibawah ini untuk membuat header menajdi transparan.

![Klik enable on complete website](https://img.penasihathosting.com/2025/cara-membuat-website/Klik-enable-on-complete-website.png.webp "Klik enable on complete website")

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">3</div>
  <p><strong>Memodifikasi Text dan Button</strong></p>
</div>

Untuk merubah suatu text atau button, Anda cukup mengklik pada element yang Anda ingin ganti.

![merubah text dan button](https://img.penasihathosting.com/2025/cara-membuat-website/merubah-text-dan-button-1024x687.png.webp "merubah text dan button")

Kemudian perhatikan panel di sebelah kiri Anda, panel tersebut langsung berubah menjadi editor untuk element yang Anda klik.

Anda bisa merubah text langsung dari element, atau dari panel di sebelah kiri.

Bagaimana cara merubah style, seperti warna, size font dan lain-lainya?

Klik pada tab "Style" untuk mengatur warna text, ukuran, shadow, dan lain-lainnya.

![merubah style text](https://img.penasihathosting.com/2025/cara-membuat-website/merubah-style-text-1024x790.png.webp "merubah style text")

Bagaimana dengan tombol atau button?

Sama caranya seperti ketika Anda merubah text. Anda klik terlebih dahulu button yang ingin Anda modifikasi, kemudian panel editor button akan muncul di sebelah kiri layar Anda.

![Memodifikasi button di Elementor](https://img.penasihathosting.com/2025/cara-membuat-website/Memodifikasi-button-di-Elementor-1024x889.png.webp "Memodifikasi button di Elementor")

Perhatikan yang saya kotaki warna merah diatas. Akan muncul semua warna palette yang Anda atur sebelumnya di tema Astra Anda.

Saya menyarankan untuk menggunakan warna yang konsisten, karena itu pilihlah warna berdasarkan warna palette yang tersedia saja.

Jika Anda ingin merubah kumpulan warna palette tema Anda, kembali pada pengaturan colors di fitur customize tema Astra Anda.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">4</div>
  <p><strong>Mengganti Gambar</strong></p>
</div>

Ada cara yang berbeda dalam mengganti gambar tergantung dari widget atau element yang digunakan.

Jika gambar yang ingin Anda ganti adalah gambar background, maka caranya adalah arahkan kursor Anda ke gambar background yang ingin diganti, dan klik icon edit section (icon bertanda enam buah titik putih).

Kemudian klik gambar untuk menggantinya dengan gambar yang lain.

![Mengganti gambar background](https://img.penasihathosting.com/2025/cara-membuat-website/Mengganti-gambar-background-1024x830.png.webp "Mengganti gambar background")

Ngomong-ngomong, plugin **Starter Templates** memiliki fitur untuk mengambil gambar langsung dari situs Pixabay. Anda bisa mencari ribuan gambar gratis langsung dari **Media Library.**

Pastikan Anda klik tab "Free Images" untuk mencari gambar yang Anda inginkan.

![Mencari gambar gratis dari Pixabay](https://img.penasihathosting.com/2025/cara-membuat-website/Mencari-gambar-gratis-dari-Pixabay-1024x501.png.webp "Mencari gambar gratis dari Pixabay")

Benar-benar plugin yang bagus, bukan?

Bagaimana jika Anda ingin mengganti gambar yang bukan gambar background, seperti dibawah ini?

Cukup klik gambar yang ingin Anda ganti dan klik "Choose Image" pada panel sebelah kiri Anda.

<div className="flex items-center mb-4">
  <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-2">5</div>
  <p><strong>Menambahkan section baru</strong></p>
</div>

Saya percaya kemungkinannya besar Anda akan menambahkan section baru pada design halaman Anda.

Mungkin Anda ingin menambahkan informasi lain seperti fitur, testimonials, FAQ dan lain-lain.

Ada tiga cara:

1. Bangun section dari awal (tidak menggunakan template)
2. Menggunakan template block yang telah disediakan Stater Templates
3. Mengambil section dari template pages yang lain

Cara kedua, memilih block dari Template Library.

Ada lebih dari 100+ block. Dan tidak seperti template pages, semuanya bisa Anda gunakan secara gratis. Wo-ho!

![menambahkan block baru](https://img.penasihathosting.com/2025/cara-membuat-website/Menambahkan-block-baru-1024x652.png.webp "menambahkan block baru")

Cara ketiga, mengambil section dari template pages lainnya. Cara ini agak ribet, tapi tidak ada cari yang lebih mudah daripada Anda membuatnya dari nol.

![save as template](https://img.penasihathosting.com/2025/cara-membuat-website/Save-as-template-1024x727.png.webp "save as template")

#### 5. Selesaikan Desain Halaman Home Anda

Elementor cukup mudah untuk dipahami.

Mungkin Anda akan menemui kesulitan pada awal mulai membangun halaman website Anda, tapi itu wajar, Anda perlu mencoba dan meng-eksplore bagian-bagian lainnya pada Elementor agar terbiasa menggunakannya.

Sampai disini Anda bebas berkreasi seperti mengganti warna background, menambah section dan widget, membuat tombol, dan lain-lain.

Lalu, bagaimana dengan halaman lainnya, seperti halaman about us, services, atau apapaun halaman selain homepage yang Anda buat?

Caranya sama dengan membuat halaman home, Anda hanya perlu mengulang prosesnya dari awal.

## Launching Website Anda

Jika Anda benar-benar mengikuti panduan cara membuat website dari awal hingga akhir, saya percaya website Anda telah siap untuk di _launching._

## Apa Selanjutnya?

Sejujurnya, masih ada banyak hal yang perlu Anda pelajari. Panduan ini hanya awalnya saja.

Saya mungkin akan membuat panduan lainnya (sedang _struggle_ mencari waktu), seperti panduan:

- Membuat contact form dan mengintegrasikannya dengan webmail dan 3rd party email
- [Memilih plugin terbaik untuk SEO, Performance, Security, dan lain-lain](https://penasihathosting.com/alat/)
- [Cara maintenance website WordPress](https://harunstudio.com/blog/panduan-maintenance-website/)
- Tips SEO On Page
- Dan lain-lain..

Terakhir, jika Anda memiliki pertanyaan, jangan sungkan-sungkan untuk mengirimkannya melalui kolom komentar yang ada dibawah.