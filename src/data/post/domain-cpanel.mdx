---
title: "Mengelola Domain di cPanel: Panduan Lengkap"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "domain"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/domain-management-cpanel.webp
excerpt: "Cara menambahkan dan mengelola domain serta mengatur redirects di cPanel. Panduan lengkap untuk mengoptimalkan pengaturan domain Anda."
metadata:
  title: "Mengelola Domain di cPanel: Panduan Lengkap (Edisi 2025)"
  description: "Pelajari cara menambahkan domain baru, mengatur subdomain, dan mengonfigurasi redirects di cPanel dengan panduan lengkap dan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 4
  chapterTitle: "Mengelola Domain"
---

# Mengelola Domain di cPanel: Panduan Lengkap

Setelah Anda memahami cara [mengupdate preferences di cPanel](/preferences-cpanel/), langkah selanjutnya adalah mempelajari cara mengelola domain. Bagian ini sangat penting terutama jika Anda memiliki beberapa domain atau ingin membuat subdomain untuk website Anda.

## Pengenalan Manajemen Domain di cPanel

Bagian "Domains" pada cPanel memungkinkan Anda untuk mengelola segala hal yang berhubungan dengan [domain](https://hostingpedia.id/kategori/domain/), seperti menambahkan domain yang baru Anda beli dari account hosting Anda, menambahkan subdomains, atau redirects domain yang Anda miliki ke domain lainnya.

![Bagian Domains di cPanel](https://img.penasihathosting.com/2025/cpanel/Domains-1024x483.webp)

Di bagian Domains, Anda akan menemukan beberapa fitur utama:

1. **Domains** - Untuk menambahkan dan mengelola domain
2. **Subdomains** - Untuk membuat dan mengelola subdomain
3. **Addon Domains** - Untuk menambahkan domain tambahan ke akun hosting Anda
4. **Redirects** - Untuk mengatur pengalihan dari satu URL ke URL lainnya
5. **Zone Editor** - Untuk mengelola catatan DNS domain Anda

Mari kita bahas masing-masing fitur secara detail.

## Menambahkan Domain 

Untuk menambahkan domain ke cPanel, Anda hanya perlu mengklik "Domains" di dalam bagian "Domains" seperti yang terlihat pada screen shoot diatas.

Ngomong-ngomong, Anda hanya dapat menambahkan domain pada cPanel Anda hanya apabila di dalam paket hosting yang Anda pilih Anda di izinkan untuk menambahkan domain tambahan.

![Halaman untuk menambahkan domain baru di cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-domain-baru-1024x911.webp)

Ketika Anda sudah berada pada halaman "Domains" seperti diatas, Anda akan diminta untuk memasukkan Domain dan document root. Sebagai informasi, Anda bisa memasukkan domain baru atau sub domain dari domain utama Anda.

- **New domain name** adalah nama domain yang ingin Anda daftarkan (masukkan nama domain tanpa menggunakan "www", sebagai contoh penasihathosting.com).

- **Subdomain** menciptakan subdomain untuk domain utama pada account cPanel Anda - subdomain tercipta secara otomatis setelah Anda mengetik new domain name dan pengunjung Anda tidak akan pernah tahu bahwa mereka didirect dari subdomain.domainutama.com ke domain baru Anda.

- **Document root** adalah lokasi/tempat pada server dimana domain baru Anda diletakkan.

Ketika Anda sudah selesai mengetik nama domain, cPanel secara otomatis akan menyediakan subdomain dan document root. Saya menyarankan Anda tidak mengubah pengaturan otomatis yang dibuat cPanel, baik pada subdomain ataupun document root.

Klik "Add Domain" bila sudah selesai.

### Mengatasi Error Saat Menambahkan Domain

Jika muncul error seperti ini:

![Contoh pesan error saat menambahkan domain baru karena batasan paket hosting](https://img.penasihathosting.com/2025/cpanel/Error-adding-domain.webp "error adding domain")

Itu artinya, paket hosting yang Anda gunakan tidak bisa menambahkan domain baru. Anda bisa menambahkan sub domain, tapi tidak domain yang berbeda dengan domain utama Anda.

Solusinya adalah:

1. **Upgrade paket hosting** Anda ke paket yang mendukung multiple domains
2. **Gunakan subdomain** sebagai alternatif jika memungkinkan
3. **Beli paket hosting baru** untuk domain tambahan Anda

## Mengelola Subdomain

Subdomain adalah bagian dari domain utama Anda yang dapat digunakan untuk membuat bagian terpisah dari website Anda. Misalnya, jika domain utama Anda adalah example.com, Anda dapat membuat subdomain seperti blog.example.com atau shop.example.com.

Untuk membuat subdomain:

1. Klik "Subdomains" di bagian Domains
2. Masukkan nama subdomain yang diinginkan
3. Pilih domain induk dari dropdown
4. Tentukan document root (lokasi file subdomain)
5. Klik "Create"

Subdomain sangat berguna untuk:

- Membuat blog terpisah dari website utama
- Membuat toko online terpisah
- Membuat area khusus member
- Membuat lingkungan pengujian (staging)

## Mengelola Addon Domains

Addon domain memungkinkan Anda untuk menambahkan domain tambahan ke akun hosting Anda. Ini berbeda dengan subdomain karena addon domain adalah domain lengkap yang terpisah.

Untuk menambahkan addon domain:

1. Klik "Addon Domains" di bagian Domains
2. Masukkan nama domain baru
3. cPanel akan otomatis mengisi subdomain dan document root
4. Buat password untuk FTP account (opsional)
5. Klik "Add Domain"

Pastikan domain yang Anda tambahkan sudah terdaftar dan nameserver-nya sudah diarahkan ke server hosting Anda.

## Redirects

Redirects adalah proses mengarahkan pengunjung dari satu URL ke URL lainnya.

Hal ini dilakukan dengan mengirimkan kode status HTTP kepada browser pengunjung, yang memberitahu browser untuk mengarahkan pengunjung ke URL tujuan yang baru. Redirects umumnya digunakan untuk beberapa alasan, seperti:

1. Redirects dapat digunakan untuk mengalihkan pengunjung dari versi non-www ke versi www (atau sebaliknya) dari sebuah website, **tetapi tidak direkomendasikan**. Mengapa? Karena dua alasan berikut:
    - Seharusnya redirect untuk tujuan ini dilakukan pada level [DNS](https://penasihathosting.com/nameserver-dan-dns/), sehingga pengunjung akan diarahkan sebelum permintaan sampai ke server web. Hal ini dapat mengurangi beban server web Anda.
    
    - Redirect yang diatur melalui DNS manager akan berlaku untuk seluruh domain dan subdomain yang terkait. Anda tidak perlu mengatur redirect secara individu untuk setiap halaman atau subdomain.

3. Selain itu, redirects juga dapat digunakan untuk mengalihkan pengunjung dari satu halaman atau direktori ke halaman atau direktori lainnya.

5. Redirects juga berguna ketika Anda ingin mengubah URL dari sebuah halaman atau direktori tanpa kehilangan pengunjung yang sudah terbiasa dengan URL lama.

Bagusnya, cPanel memiliki fitur Redirects yang bisa Anda manfaatkan.

**Mungkin sebagian dari Anda bertanya-tanya:**

Bukankah di WordPress juga ada plugin Redirect yang dapat melakukan tugas ini?

Anda benar, tetapi ketika Anda mengatur redirect melalui cPanel, redirect dilakukan di tingkat server sebelum permintaan mencapai WordPress. Ini berarti bahwa server dapat langsung mengarahkan pengunjung ke URL tujuan yang baru, menghindari pemrosesan tambahan yang mungkin diperlukan oleh WordPress.

### Cara Menambahkan Redirect di cPanel

Bagaimana cara menambahkan redirect di cPanel? Masih pada bagian Domains, Anda akan menemukan fitur Redirects.

![Menu Redirects di cPanel](https://img.penasihathosting.com/2025/cpanel/Redirects-.webp "redirects")

- Umumnya, redirect menggunakan tipe Permanent (301), kecuali yang sifat nya sementara, maka Anda bisa pilih tipe 302.

- Kemudian pilih dari domain mana Anda akan melakukan redirect, misalnya "penasihathosting.com"

- Pada kolom " / ", isi dengan alamat URL nya. Misalnya jika Anda ingin redirect URL "penasihathosting.com/url-lama", maka Anda isi dengan "url-lama"

- Kemudian, isi alamat URL baru lengkap pada kolom Redirects to

- Lalu pilih Anda mau redirect menggunakan www atau tidak atau keduanya dan klik tombol "Add".

### Jenis-jenis Redirect

Ada beberapa jenis redirect yang perlu Anda ketahui:

1. **301 Redirect (Permanent)** - Memberitahu mesin pencari bahwa halaman telah dipindahkan secara permanen. Ini adalah jenis redirect yang paling umum digunakan dan direkomendasikan untuk SEO.

2. **302 Redirect (Temporary)** - Memberitahu mesin pencari bahwa halaman telah dipindahkan sementara. Gunakan ini jika Anda berencana untuk mengembalikan URL asli di masa depan.

3. **307 Redirect (Temporary)** - Serupa dengan 302, tetapi lebih ketat dalam mempertahankan metode HTTP.

4. **308 Redirect (Permanent)** - Serupa dengan 301, tetapi lebih ketat dalam mempertahankan metode HTTP.

## Zone Editor

Zone Editor adalah alat yang memungkinkan Anda untuk mengelola catatan DNS (Domain Name System) untuk domain Anda. Ini adalah fitur lanjutan yang berguna jika Anda perlu mengonfigurasi:

- Catatan MX untuk email
- Catatan A untuk mengarahkan domain ke IP tertentu
- Catatan CNAME untuk alias domain
- Catatan TXT untuk verifikasi kepemilikan domain
- Catatan SRV untuk layanan khusus

Untuk mengakses Zone Editor:

1. Klik "Zone Editor" di bagian Domains
2. Pilih domain yang ingin Anda kelola
3. Tambah, edit, atau hapus catatan DNS sesuai kebutuhan

## Praktik Terbaik Pengelolaan Domain

Berikut beberapa praktik terbaik saat mengelola domain di cPanel:

1. **Gunakan HTTPS** - Aktifkan SSL untuk semua domain dan subdomain Anda
2. **Konsisten dengan www/non-www** - Pilih satu format dan redirect yang lainnya
3. **Kelola DNS dengan hati-hati** - Perubahan DNS dapat mempengaruhi email dan layanan lainnya
4. **Dokumentasikan perubahan** - Catat semua perubahan yang Anda buat pada domain
5. **Periksa propagasi DNS** - Perubahan DNS membutuhkan waktu untuk menyebar (biasanya 24-48 jam)

## Kesimpulan

Mengelola domain di cPanel adalah keterampilan penting untuk webmaster. Dengan memahami cara menambahkan domain, membuat subdomain, mengatur redirect, dan mengelola DNS, Anda dapat mengoptimalkan pengaturan domain Anda untuk performa dan SEO yang lebih baik.

Pada [bab berikutnya](/email-cpanel/), kita akan mempelajari cara mengatur email account di cPanel, termasuk cara membuat email dengan domain Anda sendiri.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips SEO</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Saat mengubah struktur URL website Anda, selalu gunakan redirect 301 untuk URL lama ke URL baru. Ini membantu mempertahankan peringkat SEO dan memastikan pengunjung yang menggunakan bookmark lama tetap dapat mengakses konten Anda.
  </p>
</div>
