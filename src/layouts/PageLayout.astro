---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';
import BackToTopButton from '~/components/common/BackToTopButton.astro';
import SponsorBanner from '~/components/widgets/SponsorBanner.astro';

import { headerData, footerData } from '~/navigation';

import type { MetaData } from '~/types';

export interface Props {
  metadata?: MetaData;
}

const { metadata } = Astro.props;
---

<Layout metadata={metadata}>
  <slot name="header">
    <Header {...headerData} position="right" isSticky showToggleTheme />
  </slot>
  <SponsorBanner />
  <main>
    <slot />
  </main>
  <slot name="footer">
    <Footer {...footerData} />
  </slot>
  <BackToTopButton />
</Layout>
