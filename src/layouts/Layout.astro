---
import '~/assets/styles/tailwind.css';

import { I18N } from 'astrowind:config';
import CommonMeta from '~/components/common/CommonMeta.astro';
import Favicons from '~/components/Favicons.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import ApplyColorMode from '~/components/common/ApplyColorMode.astro';
import Metadata from '~/components/common/Metadata.astro';
import UmamiAnalytics from '~/components/common/UmamiAnalytics.astro';
import BasicScripts from '~/components/common/BasicScripts.astro';
import { ClientRouter } from 'astro:transitions';
import type { MetaData as MetaDataType } from '~/types';

export interface Props {
  metadata?: MetaDataType;
}

const { metadata = {} } = Astro.props;
const { language, textDirection } = I18N;
---

<!doctype html>
<html lang={language} dir={textDirection} class="2xl:text-[18px]">
  <head>
    <CommonMeta />
    <Favicons />
    <CustomStyles />
    <ApplyColorMode />
    <Metadata {...metadata} />
    <UmamiAnalytics />
    <ClientRouter fallback="swap" />
  </head>

  <body class="antialiased text-default bg-neutral-50 dark:bg-page tracking-tight">
    <div class="relative z-10">
      <slot />
    </div>
    <BasicScripts />
  </body>
</html>
