---
import Layout from '~/layouts/PageLayout.astro';
import { Icon } from 'astro-icon/components';
import SocialShare from '~/components/common/SocialShare.astro';
import TableOfContents from '~/components/blog/TableOfContents.astro';
import BackToTopButton from '~/components/common/BackToTopButton.astro';
import FAQs from '~/components/widgets/FAQs.astro';
import Image from '~/components/common/Image.astro';
import { getFormattedDate } from '~/utils/utils';
import type { CollectionEntry } from 'astro:content';

export interface Props {
  entry: CollectionEntry<'wikihosting'>;
  headings?: Array<{ depth: number; slug: string; text: string }>;
}

const { entry, headings = [] } = Astro.props;
const { data: frontmatter } = entry;

// Generate metadata for SEO
const metadata = {
  title: frontmatter.title,
  description: frontmatter.description,
  canonical: `https://penasihathosting.com/wikihosting/${entry.id}/`,
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    url: `https://penasihathosting.com/wikihosting/${entry.id}/`,
    siteName: 'PenasihatHosting',
    images: frontmatter.image ? [
      {
        url: frontmatter.image,
        width: 1200,
        height: 628,
      },
    ] : [
      {
        url: 'https://img.penasihathosting.com/2025/July/wikihosting-og-image.webp',
        width: 1200,
        height: 628,
      },
    ],
    type: 'article',
  },
  ...frontmatter.metadata,
};


// Calculate reading time if not provided
const readingTime = frontmatter.readingTime || Math.ceil((frontmatter.wordCount || 1500) / 200);

// Get difficulty badge styling
const getDifficultyClass = (difficulty: string) => {
  switch (difficulty) {
    case 'Pemula':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'Menengah':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'Advanced':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

// Author info
const authorDescription = "Hosting Reviewer di PenasihatHosting.com";
---

<Layout metadata={metadata}>
  <!-- Schema.org structured data -->
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": frontmatter.title,
    "description": frontmatter.description,
    "image": frontmatter.image || 'https://img.penasihathosting.com/2025/July/wikihosting-og-image.webp',
    "author": {
      "@type": "Person",
      "name": frontmatter.author || "PenasihatHosting Team"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PenasihatHosting",
      "logo": {
        "@type": "ImageObject",
        "url": "https://penasihathosting.com/logo.png"
      }
    },
    "datePublished": frontmatter.publishDate || new Date().toISOString(),
    "dateModified": frontmatter.updateDate || frontmatter.publishDate || new Date().toISOString(),
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://penasihathosting.com/wikihosting/${entry.id}`
    }
  })} />

  <section class="mx-auto">
    <article>
      <!-- Content Section - Two Columns -->
      <div class="lg:grid lg:grid-cols-3 lg:gap-20 max-w-global mx-auto px-4 sm:px-6">
        <!-- Main Content - 2/3 Width -->
        <div class="lg:col-span-2 pb-8">
          <!-- Hero Section - Moved inside grid -->
          <header class="mb-2 relative">
            <!-- Title Section - Consistent with blog posts -->
            <div class="mb-8 mt-8 relative">
              <div class="max-w-full lg:max-w-[65ch]">
                <!-- Breadcrumb/Category Navigation -->
                <div class="text-[14px] font-medium text-default uppercase flex items-center gap-2 mb-4">
                  <a href="/" class="flex items-center gap-1 text-default hover:text-default hover:underline">
                    <Icon name="tabler:home" class="w-4 h-4" />
                    Home
                  </a>
                  <Icon name="tabler:chevron-right" class="w-4 h-4 text-default" />
                  <a href="/kamus-hosting/" class="text-default hover:text-default hover:underline">Kamus Hosting</a>
                </div>

                <!-- Title with thick border - Consistent with blog posts -->
                <div class="border-t-8 border-primary dark:border-primary pt-2 relative mx-auto">
                  <h1 class="text-4xl md:text-5xl font-extrabold leading-tight tracking-tight font-heading mb-3 text-gray-900 dark:text-white">
                    {frontmatter.title}
                  </h1>
                  
                  <p class="text-base text-gray-600 dark:text-gray-300 mb-4">
                    {frontmatter.description}
                  </p>

                  <!-- Badges and Meta Info -->
                  <div class="flex flex-wrap gap-3 mb-4">
                    {frontmatter.category && (
                      <span class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                        {frontmatter.category}
                      </span>
                    )}
                    {frontmatter.difficulty && (
                      <span class={`px-3 py-1 text-xs font-medium rounded-full ${getDifficultyClass(frontmatter.difficulty)}`}>
                        {frontmatter.difficulty}
                      </span>
                    )}
                    <span class="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 rounded-full">
                      {readingTime} min read
                    </span>
                  </div>
                  
                  <!-- Updated Date -->
                  <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <span>Updated <span class="font-medium">{getFormattedDate(new Date(frontmatter.updateDate || frontmatter.publishDate || new Date()))}</span></span>
                  </div>
                  
                  <!-- Social Share -->
                  <SocialShare url={`https://penasihathosting.com/wikihosting/${entry.id}`} text={frontmatter.title} class="flex gap-2" />
                </div>
              </div>
            </div>
          </header>

          <!-- Featured Image - Added below header -->
          {frontmatter.image ? (
            <div class="mt-8 mb-2">
              <Image
                src={frontmatter.image}
                class="w-full sm:rounded-md shadow-lg object-cover object-center"
                widths={[400, 900, 1075]}
                sizes="100vw"
                alt={frontmatter.excerpt || frontmatter.title || ''}
                layout="cover"
                loading="lazy"
                decoding="async"
              />
            </div>
          ) : null}

          <!-- Author Card -->
          <div class="py-4">
            {frontmatter.author && (
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-secondary dark:from-primary dark:to-secondary flex items-center justify-center text-white font-bold text-lg mr-4">
                  {frontmatter.author.charAt(0).toUpperCase()}
                </div>
                <div>
                  <span class="block text-base font-medium text-gray-900 dark:text-white">{frontmatter.author}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{authorDescription}</span>
                </div>
              </div>
            )}
          </div>

          <!-- Main Content -->
          <div class="prose prose-base dark:prose-invert dark:prose-headings:text-neutral-200 prose-headings:font-heading prose-headings:leading-tight prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-a:text-[var(--aw-color-accent)] prose-a:hover:text-[var(--aw-color-accent-hover)] dark:prose-a:text-[var(--aw-color-accent)] dark:prose-a:hover:text-[var(--aw-color-accent-hover)] prose-img:rounded-md prose-headings:scroll-mt-[80px] max-w-none leading-normal">
            <slot />
          </div>

          <!-- FAQ Section -->
          {frontmatter.faq && frontmatter.faq.length > 0 && (
            <div class="mt-12">
              <FAQs 
                title="Pertanyaan yang Sering Diajukan" 
                items={frontmatter.faq} 
                columns={1}
                classes={{
                  container: 'max-w-none'
                }}
              />
            </div>
          )}
        </div>
        
        <!-- Sidebar - 1/3 Width -->
        <div class="lg:col-span-1">
          <div class="mt-8 lg:mt-24 mb-10">
            <!-- Table of Contents -->
            <div class="bg-neutral-100 dark:bg-slate-800 p-6 rounded-md mb-6">
              <TableOfContents headings={headings} />
            </div>
          </div>

          <!-- Sticky content placeholder for future widgets -->
          <div style="position: -webkit-sticky; position: sticky; top: 6rem; z-index: 10;">
            <!-- Future widgets can be added here -->
          </div>
        </div>
      </div>
    </article>
  </section>

  <!-- Back to Top Button -->
  <BackToTopButton />
</Layout>