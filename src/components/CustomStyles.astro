---
---

<style is:inline>
  @import url('https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700&display=swap');

  :root {
    --aw-font-sans: 'Figtree', serif;
    --aw-font-serif: 'Figtree', serif;
    --aw-font-heading: 'Figtree', serif;

    --aw-color-primary: oklch(14.5% 0.01 270);
    --aw-color-secondary: oklch(55.6% 0 0);
    --aw-color-brand: rgb(1 97 239);
    --aw-color-accent: oklch(50.5% 0.213 27.518);
    --aw-color-accent-hover: oklch(50.5% 0.213 27.518 / 0.8);

    --aw-color-text-heading: rgb(0 0 0);
    --aw-color-text-default: oklch(0.252 0 0);
    --aw-color-text-muted: rgb(16 16 16 / 76%);
    --aw-color-bg-page: #f9f9f9;
    --aw-color-bg-section: rgb(255 255 255);
    --aw-color-bg-card: rgb(255 255 255);
    --aw-color-bg-form: rgb(255 255 255);
    --aw-color-bg-input: rgb(255 255 255);
    --aw-color-bg-muted: #F2EEE9;
    --aw-color-bg-page-dark: rgb(3 6 32);

    ::selection {
      background-color: lavender;
    }
  }

  .dark {
    --aw-font-sans: 'Figtree', serif;
    --aw-font-serif: 'Figtree', serif;
    --aw-font-heading: 'Figtree', serif;

    --aw-color-primary: rgb(1 97 239);
    --aw-color-secondary: rgb(1 84 207);
    --aw-color-accent: oklch(50.5% 0.213 27.518);
    --aw-color-accent-hover: oklch(50.5% 0.213 27.518 / 0.8);

    --aw-color-text-heading: rgb(247, 248, 248);
    --aw-color-text-default: rgb(229 236 246);
    --aw-color-text-muted: rgb(229 236 246 / 66%);
    --aw-color-bg-page: rgb(3 6 32);
    --aw-color-bg-section: rgb(15 23 42);
    --aw-color-bg-card: rgb(30 41 59);
    --aw-color-bg-form: rgb(30 41 59);
    --aw-color-bg-input: rgb(30 41 59);
    --aw-color-bg-muted: rgb(31 41 55);

    ::selection {
      background-color: black;
      color: snow;
    }
  }
</style>

<style is:global>
  /* Default text colors for semantic HTML elements */
  h1, h2, h3, h4, h5, h6 {
    color: var(--aw-color-text-heading);
  }

  p, span, div {
    color: var(--aw-color-text-muted);
  }

  .text-muted, .text-sm {
    color: var(--aw-color-text-muted);
  }

  /* Default link styling */
  a {
    color: var(--aw-color-accent);
    text-decoration: none;
  }

  a:hover {
    color: var(--aw-color-accent-hover);
    text-decoration: underline;
  }

  .btn-primary {
    background-color: var(--aw-color-primary);
    color: white !important; /* Ensure text is white */
    text-decoration: none !important;
    padding: 0.5rem 1rem; /* Basic padding */
    border-radius: 0.375rem; /* rounded-md consistency */
    text-align: center;
    transition: background-color 0.2s ease-in-out;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover {
    /* Custom: dark hover for primary button */
    background-color: #222222;
    text-decoration: none !important;
  }

  /* You might want to define .btn-secondary here too if it's not elsewhere */
  .btn-secondary {
    background-color: var(--aw-color-secondary);
    color: white; /* Default text color, might need adjustment based on secondary color's lightness */
    padding: 0.5rem 1rem;
    border-radius: 0.375rem; /* rounded-md consistency */
    text-align: center;
    border: 1px solid var(--aw-color-secondary); /* Example: border for secondary */
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none !important;
    transition: all 0.2s ease-in-out;
  }

  .btn-secondary:hover {
    background-color: var(--aw-color-text-muted); /* Example hover */
    text-decoration: none !important;
  }

  .prose-base, .prose {
    font-size: 17px;
  }

  @media (max-width: 540px) {
    .prose-base, .prose {
      font-size: 16px;
    }
  }
  .prose a {
    color: var(--aw-color-accent); /* Default color (light mode) */
    font-weight: 700;
    /* Set dark mode text color using theme primary color */
    @apply dark:text-primary;
  }

  @keyframes border-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Direktori Hosting Specific Styles */
  .direktori-badge-recommended {
    background: linear-gradient(135deg, var(--aw-color-primary), var(--aw-color-brand));
    color: white;
    font-weight: 600;
  }

  .direktori-badge-verified {
    background: linear-gradient(135deg, #3B82F6, #1D4ED8);
    color: white;
    font-weight: 600;
  }

  .direktori-badge-promoted {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    font-weight: 600;
  }

  .direktori-provider-card {
    transition: all 0.2s ease-in-out;
  }

  .direktori-provider-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .direktori-category-card {
    transition: all 0.2s ease-in-out;
  }

  .direktori-category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }

  .direktori-search-input {
    border: 1px solid #D1D5DB;
    background: var(--aw-color-bg-input);
    color: var(--aw-color-text-default);
  }

  .direktori-search-input:focus {
    border-color: var(--aw-color-primary);
    box-shadow: 0 0 0 3px rgba(var(--aw-color-primary), 0.1);
    outline: none;
  }

  .direktori-featured-glow {
    animation: featured-pulse 4s ease-in-out infinite alternate;
  }

  @keyframes featured-pulse {
    0% {
      box-shadow: 0 0 5px rgba(var(--aw-color-primary), 0.2);
    }
    100% {
      box-shadow: 0 0 15px rgba(var(--aw-color-primary), 0.4), 0 0 25px rgba(var(--aw-color-primary), 0.2);
    }
  }

  /* Kategori badge hover custom background */
  .kategori-badge:hover {
    background: #E8E1D8 !important;
  }
</style>
