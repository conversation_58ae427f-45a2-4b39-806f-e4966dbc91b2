---
import type { HostingType } from '~/types';

interface Props {
  activeTab: HostingType;
}

const { activeTab } = Astro.props;

const tabs = [
  { id: 'shared', label: 'Shared Hosting', enabled: true },
  { id: 'vps', label: 'VPS Hosting', enabled: true },
  { id: 'cloudpanel', label: 'Cloud Panel', enabled: true },
  { id: 'wordpress', label: 'WordPress Hosting (coming soon)', enabled: false },
] as const;
---

<div class="mb-8">
  <!-- Desktop View -->
  <div class="hidden md:flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-t-lg p-1">
    {tabs.map((tab) => (
      <button 
        class={`px-6 py-3 font-medium text-sm rounded-md transition-all duration-200 ${
          activeTab === tab.id 
            ? 'bg-white dark:bg-gray-700 text-primary shadow-sm border border-gray-200 dark:border-gray-600' 
            : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        } ${!tab.enabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        data-tab={tab.id}
        disabled={!tab.enabled}
      >
        {tab.enabled ? (
          tab.label
        ) : (
          <span class="text-gray-400">{tab.label}</span>
        )}
      </button>
    ))}
  </div>

  <!-- Mobile View with Horizontal Scroll -->
  <div class="md:hidden bg-gray-50 dark:bg-gray-800 rounded-t-lg p-1 border-b border-gray-200 dark:border-gray-700">
    <div class="flex overflow-x-auto scrollbar-hide space-x-2 pb-1">
      {tabs.map((tab) => (
        <button 
          class={`flex-shrink-0 px-4 py-2.5 font-medium text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
            activeTab === tab.id 
              ? 'bg-white dark:bg-gray-700 text-primary shadow-sm border border-gray-200 dark:border-gray-600' 
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
          } ${!tab.enabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          data-tab={tab.id}
          disabled={!tab.enabled}
        >
          {tab.enabled ? (
            tab.label
          ) : (
            <span class="text-gray-400">{tab.label}</span>
          )}
        </button>
      ))}
    </div>
  </div>
</div>

<script>
  // Tab switching functionality with View Transitions support
  function initTabNavigation() {
    const tabButtons = document.querySelectorAll('[data-tab]');
    const tabContents = {
      'shared': document.getElementById('shared-hosting-table'),
      'vps': document.getElementById('vps-hosting-table'),
      'cloudpanel': document.getElementById('cloudpanel-hosting-table'),
      'wordpress': document.getElementById('wordpress-hosting-table')
    };

    tabButtons.forEach(button => {
      button.addEventListener('click', function() {
        if (this.disabled) return;
        
        const targetTab = this.getAttribute('data-tab');
        
        // Update button states for both desktop and mobile
        tabButtons.forEach(btn => {
          btn.classList.remove('bg-white', 'dark:bg-gray-700', 'text-primary', 'shadow-sm', 'border', 'border-gray-200', 'dark:border-gray-600');
          btn.classList.add('text-gray-500', 'dark:text-gray-400');
        });
        
        this.classList.remove('text-gray-500', 'dark:text-gray-400');
        this.classList.add('bg-white', 'dark:bg-gray-700', 'text-primary', 'shadow-sm', 'border', 'border-gray-200', 'dark:border-gray-600');
        
        // Scroll active tab into view on mobile
        if (window.innerWidth < 768) {
          this.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'nearest', 
            inline: 'center' 
          });
        }
        
        // Update content visibility
        Object.keys(tabContents).forEach(key => {
          if (tabContents[key]) {
            tabContents[key].classList.add('hidden');
          }
        });
        
        if (tabContents[targetTab]) {
          tabContents[targetTab].classList.remove('hidden');
        }
      });
    });
  }

  // Initialize on DOM ready
  document.addEventListener('DOMContentLoaded', initTabNavigation);
  
  // Re-initialize on Astro view transitions
  document.addEventListener('astro:page-load', initTabNavigation);
</script>

<style>
  /* Hide scrollbar for mobile tabs */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
  
  /* Ensure smooth scrolling */
  .scrollbar-hide {
    scroll-behavior: smooth;
  }
</style>