---
import CloudPanelTableHeader from './CloudPanelTableHeader.astro';
import CloudPanelTableRow from './CloudPanelTableRow.astro';
import { cloudPanelData } from '../hostingProviders';
import type { CloudPanelProvider } from '../../../types';

interface Props {
  providers?: CloudPanelProvider[];
  title: string;
  subtitle: string;
}

const { providers = cloudPanelData, title, subtitle } = Astro.props;
---

<div>
  <div class="mb-6">
    <div class="flex items-center mb-2">
      <span class="text-green-600 mr-2">✓</span>
      <h2 class="text-xl font-bold">{title}</h2>
    </div>
    <p class="text-gray-600 dark:text-gray-400 text-sm">{subtitle}</p>
  </div>

  <!-- Table -->
  <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg">
    <table id="cloudpanel-hosting-table" class="w-full min-w-[1000px]">
      <CloudPanelTableHeader />
      <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
        {providers.map((provider) => (
          <CloudPanelTableRow provider={provider} />
        ))}
      </tbody>
    </table>
  </div>

  <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
    <div class="text-sm text-gray-700 dark:text-gray-300">
      <p class="mb-2">
        💡 <strong>CATATAN:</strong> Harga dapat berubah sewaktu-waktu. Data terakhir diupdate Agustus 2025.
      </p>
      <p class="mb-2">
        🆓 <strong>FREE PLANS:</strong> Beberapa provider menawarkan plan gratis atau trial untuk testing.
      </p>
    </div>
  </div>
</div>
