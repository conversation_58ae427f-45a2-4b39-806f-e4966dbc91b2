---
import Button from '../../ui/Button.astro';
import type { CloudPanelProvider } from '../../../types';
import { getBadgeColor } from '../../../utils/hostingTableUtils';

interface Props {
  provider: CloudPanelProvider;
}

const { provider } = Astro.props;

// Check if provider is sponsored (for now, check by name - could be enhanced with dedicated field)
const isSponsored = provider.name.toLowerCase().includes('xcloud');
---

<tr class={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
  isSponsored 
    ? 'bg-gradient-to-br from-purple-50 to-blue-50 border-l-4 border-l-amber-400 ring-1 ring-purple-100' 
    : ''
}`}>
  <!-- Logo -->
  <td class="px-4 py-4 text-center">
    {provider.logo ? (
      <img 
        src={provider.logo} 
        alt={`${provider.name} logo`}
        class="w-10 h-10 object-contain mx-auto"
        loading="lazy"
      />
    ) : (
      <div class="w-10 h-10 mx-auto bg-gray-100 dark:bg-gray-700 rounded-md flex items-center justify-center">
        <span class="text-gray-400 text-xs">No Logo</span>
      </div>
    )}
  </td>
  
  <!-- Provider -->
  <td class="px-4 py-4">
    <div class="flex flex-col gap-2">
      <a
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        class="font-semibold text-gray-900 dark:text-white hover:text-primary transition-colors"
      >
        {provider.name}
      </a>
      <div class="flex flex-col gap-1">
        {provider.badge && (
          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border w-fit ${getBadgeColor(provider.badgeColor || 'gray')}`}>
            {provider.badge}
          </span>
        )}
        {isSponsored && (
          <div class="text-xs text-amber-700 font-medium flex items-center gap-1">
            ⭐ Featured Partner
          </div>
        )}
      </div>
    </div>
  </td>
  
  <!-- Harga/Bulan -->
  <td class="px-4 py-4">
    <div class={`font-semibold text-lg ${
      isSponsored 
        ? 'text-purple-700 font-bold' 
        : 'text-primary'
    }`}>
      {provider.price}
    </div>
  </td>
  
  <!-- Alasan & Keunggulan -->
  <td class="px-4 py-4">
    <div class="text-sm text-gray-600 dark:text-gray-400 max-w-md leading-relaxed">
      {provider.uptime}
    </div>
  </td>
  
  <!-- Cocok Untuk -->
  <td class="px-4 py-4">
    <div class="text-sm text-gray-700 dark:text-gray-300 max-w-sm leading-relaxed font-medium">
      {provider.support}
    </div>
  </td>
  
  <!-- Free Plan -->
  <td class="px-4 py-4">
    <div class="text-sm">
      {provider.freePlan ? (
        <span class={`inline-flex items-center px-2 py-1 rounded-md font-medium border ${
          isSponsored 
            ? 'bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border-blue-300 shadow-sm' 
            : 'bg-blue-100 text-blue-800 border-blue-200'
        }`}>
          🆓 {provider.freePlan}
        </span>
      ) : (
        <span class="text-gray-500">Tidak tersedia</span>
      )}
    </div>
  </td>
  
  <!-- Actions -->
  <td class="px-4 py-4 whitespace-nowrap">
    <div class="space-y-2">
      <a
        href={provider.reviewLink}
        class={`block text-center px-3 py-1 text-xs font-medium rounded transition-colors ${
          isSponsored 
            ? 'text-purple-700 bg-purple-50 hover:no-underline hover:text-purple-800 hover:bg-purple-100 border border-purple-200' 
            : 'text-gray-700 bg-gray-100 hover:no-underline hover:text-primary hover:bg-gray-200'
        }`}
      >
        Baca Review
      </a>
      <Button
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        variant="primary"
        class={`text-xs px-3 py-1 w-full ${
          isSponsored 
            ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg transform hover:scale-105' 
            : ''
        }`}
        text={isSponsored ? '🚀 Pilih Provider' : 'Pilih Provider'}
      />
    </div>
  </td>
</tr>
