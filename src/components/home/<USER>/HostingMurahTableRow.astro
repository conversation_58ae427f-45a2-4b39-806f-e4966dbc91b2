---
import Button from '../../ui/Button.astro';
import type { HostingMurahProvider } from '../../../types';
import { getBadgeColor } from '../../../utils/hostingTableUtils';

interface Props {
  provider: HostingMurahProvider;
}

const { provider } = Astro.props;

// Get price status color
const getPriceStatusColor = (status: string) => {
  switch(status) {
    case 'lowest': return 'text-green-500 font-bold';
    case 'budget': return 'text-blue-500 font-semibold';
    case 'fair': return 'text-gray-700 font-medium';
    case 'higher': return 'text-red-500 font-medium';
    default: return 'text-gray-600';
  }
};

// Get uptime status color
const getUptimeStatusColor = (status: string) => {
  switch(status) {
    case 'excellent': return 'text-green-500';
    case 'good': return 'text-blue-500';
    case 'ok': return 'text-blue-500';
    case 'poor': return 'text-red-500';
    default: return 'text-gray-600';
  }
};

// Get load time status color
const getLoadTimeStatusColor = (status: string) => {
  switch(status) {
    case 'excellent': return 'text-green-500';
    case 'fast': return 'text-green-500';
    case 'faster': return 'text-green-500';
    case 'good': return 'text-blue-500';
    case 'slower': return 'text-red-500';
    case 'slowest': return 'text-red-500';
    default: return 'text-gray-600';
  }
};

// Get support status color
const getSupportStatusColor = (status: string) => {
  switch(status) {
    case 'best': return 'text-green-500';
    case 'fast': return 'text-green-500';
    case 'good': return 'text-blue-500';
    case 'slow': return 'text-red-500';
    case 'slowest': return 'text-red-500';
    default: return 'text-gray-600';
  }
};
---

<tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
  <!-- Provider -->
  <td class="px-4 py-4">
    <div class="flex flex-col gap-2">
      <a
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        class="flex items-center gap-3 hover:opacity-80 transition-opacity"
      >
        {provider.logo ? (
          <img 
            src={provider.logo} 
            alt={`${provider.name} logo`}
            class="w-24 h-8 object-contain"
            loading="lazy"
          />
        ) : (
          <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-md flex items-center justify-center">
            <span class="text-gray-400 text-xs font-medium">{provider.name.substring(0, 2)}</span>
          </div>
        )}
      </a>
      <div class="flex flex-col gap-1">
        {provider.badge && (
          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border w-fit ${getBadgeColor(provider.badgeColor || 'gray')}`}>
            {provider.badge}
          </span>
        )}
      </div>
    </div>
  </td>
  
  <!-- Harga Termurah -->
  <td class="px-4 py-4">
    <div class={`font-semibold text-lg ${getPriceStatusColor(provider.priceStatus)}`}>
      {provider.price}
    </div>
    <div class="text-xs text-gray-500 mt-1">
      {provider.priceNote}
    </div>
  </td>
  
  <!-- Paket & Resource -->
  <td class="px-4 py-4">
    <div class="space-y-2">
      <div class="font-medium text-sm text-gray-900 dark:text-white">
        Paket {provider.packageName}
      </div>
      <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <div class="flex justify-between">
          <span>Storage:</span>
          <span class="font-medium">{provider.resources.storage}</span>
        </div>
        <div class="flex justify-between">
          <span>Bandwidth:</span>
          <span class="font-medium">{provider.resources.bandwidth}</span>
        </div>
        <div class="flex justify-between">
          <span>CPU/RAM:</span>
          <span class="font-medium">{provider.resources.cpu} / {provider.resources.ram}</span>
        </div>
        <div class="flex justify-between">
          <span>Domain:</span>
          <span class="font-medium">{provider.resources.domains}</span>
        </div>
        <div class="flex justify-between">
          <span>Panel:</span>
          <span class="font-medium">{provider.resources.controlPanel}</span>
        </div>
      </div>
    </div>
  </td>
  
  <!-- Performa -->
  <td class="px-4 py-4">
    <div class="space-y-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-600">Uptime:</span>
        <span class={`font-medium ${getUptimeStatusColor(provider.performance.uptimeStatus)}`}>
          {provider.performance.uptime}
        </span>
      </div>
      <div class="flex items-center justify-between">
        <span class="text-gray-600">Load Time:</span>
        <span class={`font-medium ${getLoadTimeStatusColor(provider.performance.loadTimeStatus)}`}>
          {provider.performance.loadTime}
        </span>
      </div>
      <div class="flex items-center justify-between">
        <span class="text-gray-600">Support:</span>
        <span class={`font-medium ${getSupportStatusColor(provider.performance.supportStatus)}`}>
          {provider.performance.support}
        </span>
      </div>
    </div>
  </td>
  
  <!-- Garansi & Promo -->
  <td class="px-4 py-4">
    <div class="space-y-2 text-sm">
      <div>
        <span class="text-gray-600">Garansi:</span>
        <div class="font-medium text-gray-700">{provider.guarantee}</div>
      </div>
      {provider.discount && (
        <div class="text-xs">
          <span class="inline-flex items-center px-2 py-1 rounded-md font-medium bg-green-100 text-green-800 border border-green-200">
            🎁 {provider.discount}
          </span>
        </div>
      )}
      {provider.specialOffer && (
        <div class="text-xs text-blue-500 font-medium">
          ✨ {provider.specialOffer}
        </div>
      )}
    </div>
  </td>
  
  <!-- Actions -->
  <td class="px-4 py-4 whitespace-nowrap">
    <div class="space-y-2">
      <a
        href={provider.reviewLink}
        class="block text-center px-3 py-1 text-xs font-medium rounded transition-colors text-gray-700 bg-gray-100 hover:no-underline hover:text-primary hover:bg-gray-200"
      >
        Baca Review
      </a>
      <Button
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        variant="primary"
        class="text-xs px-3 py-1 w-full"
        text="Pilih Hosting"
      />
    </div>
  </td>
</tr>
