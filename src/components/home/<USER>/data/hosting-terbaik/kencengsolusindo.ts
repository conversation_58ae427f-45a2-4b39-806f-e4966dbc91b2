import type { HostingReview } from "../../types";

export const kencengsolusindo: HostingReview = {
  providerName: "Kenceng Solusindo",
  displayName: "Kenceng Solusindo - Harga seimbang dengan kualitas",
  website: "www.KencengSolusindo.co.id",
  reviewUrl: "/review-kenceng-solusindo/", // Placeholder, please update if you have a specific review URL
  pricing: "Rp 16.000/bulan",
  highlights: [
    "Kecepatan hosting nya mengalami peningkatan yang sangat signifikan (tercepat ke #2)",
    "Rata-rata uptime nya pun cukup stabil secara keseluruhan",
    "Layanan support yang responsif dan cepat dalam menangani error website pelanggan",
    "Harga hosting dengan struktur yang adil",
  ],
  description: [
    "Kenceng Solusindo lahir di tahun 2016 dan saat ini, mereka mengklaim telah memiliki ribuan pelanggan dan berkantor pusat di Mojokerto, Jawa Timur.",
    "Saya suka dengan nama brand provider hosting yang satu ini karena memang menarik perhatian. Namun, ketika Anda menggunakan nama seperti \"kenceng\", tentu Anda harus memenuhi ekspektasi pelanggan. Jika tidak, nama keren ini malah bisa menjadi petaka bagi reputasi mereka.",
    "Apa yang saya pikirkan pertama kali? Sebagai hosting reviewer, tentu adalah spesifikasi server yang mereka punya.",
    "Jika dilihat dari spesifikasi server hosting pada paket hosting yang saya sewa, mereka menggunakan: CPU Dual Intel(R) Xeon(R) CPU E5-2658 v2, RAM 128GB DDR3, SSD SATA untuk Data Storage, dan SSD NVMe untuk Database.",
    "Jujur saja, dari sisi spesifikasi, server mereka tampak kurang bertenaga. Idealnya untuk semua penyedia layanan shared hosting saat ini sudah menggunakan CPU dengan frekuensi lebih dari 3.0 GHz dan menggunakan RAM fisik DDR4 minimal 2666 MHz ECC.",
    "Lantas, bagaimanakah performa sebenarnya dari hosting mereka?",
    "Saya cukup terkejut melihat <a href='/hasil-penelitian/'>hasil pengujian</a> performa Kenceng Solusindo tahun 2025 ini. Mengapa? Karena rata-rata waktu kecepatan response server nya adalah yang tercepat ke #2 (hanya kalah dari IdCloudHost).",
    "Saya tidak tahu pasti apa yang mereka lakukan. Saya tidak melihat adanya peningkatan pada perangkat keras fisik seperti yang dilakukan DomaiNesia. Oleh karena itu, jawabannya kemungkinan besar terletak pada perbaikan yang mereka lakukan di tingkat konfigurasi server. Ini tentu sangat menarik untuk diteliti lebih lanjut.",
    "Tidak hanya itu, rata-rata uptime server mereka juga sudah terlihat stabil. Selama tahun 2024 lalu, uptime server mereka hanya gagal 1x, selebihnya ada diatas 99,96%, menjadikannya pilihan provider terstabil ke #2 setelah DomaiNesia.",
    "Bagaimana dengan layanan support nya? Juga menunjukan tren yang positif dimana berdasarkan pengujian terakhir, mereka berhasil menangani error dengan catatan waktu hanya 5 menit atau tercepat diantara semua provider.",
  ],
  affiliateLink: "https://penasihathosting.com/go/kencengsolusindo",
  pros: [
    "Rata-rata waktu respons server yang tercepat ke #2 pada pengujian load testing.",
    "Layanan support tercepat dalam hal penanganan error website pelanggan.",
    "Harga hosting dengan struktur harga yang adil.",
    "Menyediakan jumlah sumber daya hosting yang lebih dari cukup pada paket termurahnya.",
    "Rata-rata uptime yang secara keseluruhan stabil sepanjang tahun 2024.",
  ],
  cons: [
    "Tidak ada garansi pengembalian dana",
    "Fitur backup memiliki keterbatasan.",
    "Terlalu banyak pilihan pada paket hosting bisnis.",
  ],
  image: 'https://img.penasihathosting.com/2025/May/review-kenceng-solusindo.webp'
};