import type { HostingReview } from "../../types";

export const jagoanhosting: HostingReview = {
  providerName: "Jagoan Hosting",
  website: "www.JagoanHosting.com",
  reviewUrl: "/review-jagoan-hosting/", // Placeholder, please update if you have a specific review URL
  pricing: "Rp 25.000/bulan",
  highlights: [
    "Rata-rata kecepatan waktu response server yang terbilang 'OK' dalam pengujian load testing.",
    "Harga hosting yang murah dengan struktur harga yang adil (harga bulanan sama dengan harga tahunan)",
    "Fitur backup otomatis dengan Acronis Backup dengan retensi file backup hingga 14 hari (terbanyak diantara semua provider)",
  ],
  description: [
    "Jagoan Hosting cukup dikenal di kalangan blogger. Didirikan pada tahun 2007, mereka memposisikan diri untuk melayani pelanggan pemula yang menginginkan kualitas hosting terbaik dengan harga terjangkau.",
    "Bagaimana evaluasi kami terhadap penyedia <a href='/hosting-murah/'>hosting murah</a> ini? Yang saya maksud adalah dari tiga faktor yang selalu menjadi patokan kami dalam menilai provider hosting: uptime, kecepatan server, dan layanan support.",
    "Berdasarkan pengujian terakhir di Februari 2025, rata-rata kecepatan response server shared hosting Jagoan Hosting ada di rank #3 dari 6 provider.",
    "Sayangnya, dalam monitoring sepanjang tahun 2024, rata-rata uptime mereka menunjukkan ketidakstabilan yang cukup serius. Skor uptime mereka hanya mencapai 98,833%, yang merupakan yang terburuk di antara semua penyedia yang kami monitoring.",
    "Terkait layanan support, perlu diingat bahwa dalam update penelitian Mei 2023 lalu, Jagoan Hosting telah melakukan beberapa peningkatan layanan, yaitu:",
    "Bantuan layanan via WhatsApp full human",
    "Certified WordPress support",
    "Program bantuan optimasi kecepatan website",
    "Meskipun demikian, dalam pengujian terbaru kami, layanan support mereka masih tergolong lambat. Mereka memang berhasil memperbaiki kesalahan yang sengaja kami buat selama pengujian support, namun waktu penanganan mereka adalah yang terlama, yaitu 110 menit. Ini jauh di atas rata-rata semua penyedia yang kami uji, yang hanya 34 menit.",
  ],
  pros: [
    "Rata-rata kecepatan waktu response server yang terbilang 'OK' dalam pengujian load testing.",
    "Memiliki fitur backup otomatis menggunakan Acronis Backup dengan banyak keunggulan.",
    "Garansi pembelian hosting selama 30 hari.",
    "Gratis migrasi hosting dan gratis domain untuk paket tertentu.",
    "Paket termurahnya menawarkan jumlah resources yang lebih dari cukup untuk membuat website baru.",
    "Memiliki halaman knowledge base yang lengkap dan kamus hosting.",
  ],
  affiliateLink: "https://penasihathosting.com/go/jagoanhosting",
  cons: [
    "Rata-rata uptime yang buruk dengan stabilitas yang sangat fluktuatif.",
    "Layanan support yang lambat dalam menangani permasalahan.",
    "Harga hosting mengalami kenaikan Rp 10.000/bulan",
    "Ada denda untuk biaya restore data jika telat membayar tagihan perpanjangan hosting.",
  ],
  image: 'https://img.penasihathosting.com/2025/May/review-jagoan-hosting.webp'
}; 