---
import SocialShare from '~/components/common/SocialShare.astro';
import Image from '~/components/common/Image.astro';

interface Props {
  title: string;
  description: string;
  author: string;
  date: string;
  url: string;
  image?: string;
}

const { title, description, author, date, url, image } = Astro.props;
---

<section class="bg-bg-page dark:bg-bg-section pt-8">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <div class="text-xs mb-6">
      Disclosure: Kami mereview semua provider hosting secara independen tanpa campur tangan pihak manapun. Ketika Anda membeli hosting melalui link kami, kami mendapatkan komisi (10-50%), tanpa ada biaya tambahan untuk Anda.
    </div>
    <div class="max-w-full lg:max-w-[65ch]">
      <div class="border-t-8 border-primary dark:border-primary pt-2 mb-4">
        <h1 class="text-4xl md:text-5xl font-extrabold mb-3">
          {title}
        </h1>
        <p class="text-lg mb-4">
          {description}
        </p>
        <div class="flex items-center text-sm mb-4">
          <span class="font-medium">{author}</span>
          <span class="mx-2">•</span>
          <span>Updated: {date}</span>
        </div>
        <SocialShare url={url} text={title} />
      </div>
    </div>
    {image && (
      <div class="mt-8">
        <Image
          src={image}
          alt={title}
          width={1200}
          height={600}
          class="w-full object-cover rounded-lg"
          aspectRatio="16/9"
        />
      </div>
    )}
  </div>
</section>
