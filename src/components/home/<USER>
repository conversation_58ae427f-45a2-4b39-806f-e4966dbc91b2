---
// Data models only, no JSX/HTML in frontmatter!
const hostingProviders = [
  {
    name: 'IdCloudHost.com',
    logo: 'https://img.penasihathosting.com/2025/May/IdCloudHost.webp',
    url: 'https://penasihathosting.com/go/idcloudhost',
    features: [
      { text: 'Kecepatan server hosting tercepat ke #1', isPositive: true },
      { text: 'Harga hosting terjangkau + struktur harga yang adil', isPositive: true },
      { text: 'Rata-rata uptime 100% dalam 7 bulan terakhir', isPositive: true },
      { text: 'Layanan support yang cukup cepat', isPositive: true },
      { text: 'Tidak ada garansi hosting!', isPositive: false }
    ]
  },
  {
    name: 'KencengSolusindo.com',
    logo: 'https://img.penasihathosting.com/2025/May/logo-kenceng-solusindo.webp',
    url: 'https://penasihathosting.com/go/kencengsolusindo',
    features: [
      { text: 'Kecepatan server hosting tercepat ke #2', isPositive: true },
      { text: 'Rata-rata uptime sangat stabil dalam 7 bulan terakhir', isPositive: true },
      { text: 'Layanan support yang cepat', isPositive: true },
      { text: 'Harga hosting terjangkau + struktur harga yang adil', isPositive: true },
      { text: 'Tidak ada garansi hosting!', isPositive: false }
    ]
  },
  {
    name: 'Cloudways.com',
    logo: 'https://cdn.penasihathosting.com/hosting/cloudways-logo.webp',
    url: 'https://penasihathosting.com/go/cloudways',
    isSponsored: true,
    features: 'Cloud hosting platform premium dengan performa tinggi dan dukungan 24/7. Kelola WordPress dengan mudah di infrastruktur cloud terbaik dari DigitalOcean, AWS, Google Cloud, dan Vultr. Dilengkapi dengan CloudwaysCDN, staging environment, automated backups, dan advanced caching untuk website yang super cepat.'
  }
];

interface Props {
  title?: string;
}

const { title } = Astro.props;
---

<section class="py-4 bg-bg-page dark:bg-dark">
  <div class="max-w-global mx-auto px-4 sm:px-6">
      <div class="max-w-full lg:max-w-[57ch] border-t-4 border-primary dark:border-primary pt-2 mb-8">
      <h2 class="text-2xl font-bold">{title}</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      {hostingProviders.map((provider) => (
        <div class={`rounded-lg bg-bg-section shadow-md p-6 relative overflow-hidden $`}>
          {provider.isSponsored && (
            <div class="mb-3">
              <div class="inline-flex items-center text-xs font-semibold sponsored-badge">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Sponsored
              </div>
            </div>
          )}
          <img src={provider.logo} alt={provider.name} class="h-6 mb-6" />

          {Array.isArray(provider.features) ? (
            <div class="mb-4">
            <h3 class="font-bold mb-2">Pros/Cons:</h3>
              <ul class="space-y-2">
                {provider.features.map((feature) => (
                  <li class="flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class={`h-5 w-5 mr-2 flex-shrink-0 mt-0.5 ${feature.isPositive ? 'text-primary' : 'text-neutral-400'}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      {feature.isPositive ? (
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      ) : (
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      )}
                    </svg>
                    <span class="text-sm">{feature.text}</span>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p class="text-sm mb-6">
              {provider.features}
            </p>
          )}

          <a
            href={provider.url}
            target="_blank"
            rel="nofollow noopener noreferrer"
            data-umami-event={`Click TopHosting - ${provider.name}`}
            class={`block font-bold $`}
          >
            {provider.name} →
          </a>
        </div>
      ))}
    </div>
  </div>
</section>
