---
import type { HostingReview as HostingReviewType } from './reviews/types';
import HostingReview from './reviews/HostingReview.astro';
import HostingList from './HostingList.astro';
import HostingContent from './HostingContent.astro';
import FAQSection from './FAQSection.astro';

type FAQComponent = typeof FAQSection;

interface SidebarContent {
  title?: string;
  content?: unknown; // This will accept any content that can be rendered in Astro
}

interface Props {
  hostingList?: string[];
  reviews: HostingReviewType[];
  title?: string;
  intro?: string;
  paragraphs?: string[];
  showSidebar?: boolean;
  showFAQ?: boolean;
  showHostingList?: boolean;
  sidebarContent?: SidebarContent;
  faqComponent?: FAQComponent; // Accept a custom FAQ component
}

const {
  hostingList = [],
  reviews,
  title = '',
  intro = '',
  paragraphs = [],
  showSidebar = false, // Hide sidebar by default
  showFAQ = true,
  showHostingList = hostingList.length > 0, // Only show if hostingList is provided
  faqComponent: CustomFAQ = null
} = Astro.props;
---

<section class="py-8 bg-bg-page dark:bg-dark">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <div class="lg:grid lg:grid-cols-3 lg:gap-12">
      {/* Main Content - 2/3 Width */}
      <div class={showSidebar ? 'lg:col-span-2' : 'lg:col-span-3'}>
        <div class="prose max-w-none">
          <HostingContent title={title} intro={intro} paragraphs={paragraphs}>
            {showHostingList && hostingList.length > 0 && (
              <HostingList hostingList={hostingList} />
            )}
          </HostingContent>
          
          {reviews.length > 0 && (
            <div class="mt-12">
              {reviews.map((review, index) => (
                <HostingReview review={review} index={index} />
              ))}
            </div>
          )}
          
          {showFAQ && (
            <div class="mt-12">
              {CustomFAQ ? <CustomFAQ /> : <FAQSection />}
            </div>
          )}
        </div>
      </div>
      
      
    </div>
  </div>
</section>

<script is:inline>
  function initSidebar() {
    const sidebarWrapper = document.getElementById('sidebar-wrapper');
    const target = document.getElementById('review-idcloudhost');
    
    if (sidebarWrapper && target) {
      function toggleSidebar() {
        if (window.scrollY + window.innerHeight > target.offsetTop) {
          sidebarWrapper.style.display = '';
        } else {
          sidebarWrapper.style.display = 'none';
        }
      }
      
      // Tambahkan event listener untuk scroll
      window.addEventListener('scroll', toggleSidebar);
      
      // Jalankan toggleSidebar dengan delay kecil untuk memastikan layout sudah selesai
      setTimeout(toggleSidebar, 100);
      
      // Jalankan lagi setelah semua gambar dan resource dimuat
      window.addEventListener('load', toggleSidebar);
    }
  }
  
  // Inisialisasi saat DOM sudah siap
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSidebar);
  } else {
    initSidebar();
  }
  
  // Inisialisasi juga saat navigasi Astro selesai
  document.addEventListener('astro:after-swap', initSidebar);
</script>
