---
interface Props {
  hostingList: string[];
}

const { hostingList } = Astro.props;
---

<ul class={`list-disc pl-6 mb-6 space-y-1 [&>li]:my-0`}>
  {hostingList.map(host => {
    // Extract just the provider name (before the first hyphen) for the ID
    const providerName = typeof host === 'string' ? host.split(' - ')[0].trim() : host;
    const slug = providerName.toLowerCase().replace(/\s+/g, '-');
    return (
      <li>
        <a 
          href={`#review-${slug}`}
          class="hover:text-default !text-default transition-colors hover:underline cursor-pointer no-underline"
        >
          {host}
        </a>
      </li>
    );
  })}
</ul>

<style>
  /* Smooth scroll behavior for all anchor links */
  html {
    scroll-behavior: smooth;
  }
  
  /* Add some padding to account for fixed header */
  [id^="review-"] {
    scroll-margin-top: 100px;
  }
</style>
