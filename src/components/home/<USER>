---
import Accordion from "../ui/Accordion.astro";
import AccordionItem from "../ui/AccordionItem.astro";

const faqs = [
  {
    question: "Mengapa beberapa provider hosting tidak lagi direview oleh Penasihat Hosting?",
    answer: [
      "Ada beberapa alasan mengapa Anda tidak lagi melihat beberapa provider hosting tertentu dalam daftar review kami, seperti Niagahoster, Dewaweb, DomoSquare, Jetorbit, Rumahweb, ArdetaMedia, Exabytes, Masterweb.",
      "Provider terkait memilih untuk menarik diri dari penelitian hosting kami,",
      "Provider melakukan blocking terhadap alat monitoring uptime kami, sehingga kami tidak dapat menguji stabilitas uptimenya. Hal ini menghambat penelitian kami dan kami tidak dapat menilai kinerja server mereka.",
      "Atau provider hosting tersebut merupakan anak perusahaan dari perusahaan induk. Sebagai contoh, ArdetaMedia adalah anak perusahaan dari Jo<PERSON>, sehingga fokus penelitian kami akan lebih tertuju pada JogjaHost saja. Hal ini memungkinkan kami untuk melakukan tinjauan yang lebih terfokus..",
      "Ada kemungkinan bahwa beberapa provider hosting melakukan kecurangan, seperti memindahkan website test kami ke server dengan performa dan stabilitas uptime yang tinggi untuk hasil penelitian yang lebih baik. Hal ini dapat terdeteksi dan membuat kami meragukan integritas mereka. Oleh karena itu, kami kehilangan kepercayaan untuk melanjutkan penelitian terhadap mereka.",
      "Update 2024: Kami tidak memasukkan penyedia hosting seperti Dapur Hosting, Rumah Hosting, Dewabiz, ArdHosting, JogjaHost, dan Indowebsite dalam penelitian tahun ini. Hal ini dikarenakan kami tidak melihat adanya perbaikan dalam layanan mereka selama penelitian di tahun-tahun sebelumnya, sehingga kami memutuskan untuk tidak melanjutkan penilaian terhadap mereka. Ada kemungkinan akan kembali masuk dalam penelitian tahun depan, 2025.",
    ]
  },
  {
    question: "Apa Perbedaan Antara Web Hosting dan Domain?",
    answer: [
      "Sederhananya, web hosting adalah rumah bagi website, sedangkan domain adalah alamatnya.",
      "Untuk membuat website, Anda membutuhkan keduanya. Untuk membuat pengunjung bisa berkunjung ke rumah (website) Anda, Anda membutuhkan alamat (domain).",
      "Penjelasan lengkapnya, Anda bisa membaca panduan hosting yang telah kami tulis secara komprehensif ini.",
    ]
  },
  {
    question: "Apa perbedaan shared hosting dengan virtual private server (VPS)?",
    answer: [
      "VPS masih mirip dengan <a href=\"/direktori/shared-hosting/\">shared hosting</a>, Anda masih berbagi dalam satu server yang sama.",
      "Hanya saja Anda mendapatkan sumber daya yang lebih besar dan pasti ukurannya, baik CPU, RAM, disk space maupun bandwidthnya, tanpa di bagi-bagi dengan pengguna lain nya. Ibarat nya Anda sedang menyewa sebuah kamar khusus/private room dari sebuah ruangan yang luas (ruangan diartikan sebagai server).",
      "VPS terbagi menjadi dua, ada yang <a href=\"/direktori/unmanaged-vps/\">unmanaged VPS</a> dan ada yang <a href=\"/direktori/managed-vps/\">managed VPS</a>.",
      "UnManaged VPS berarti VPS yang Anda sewa, Anda sendiri yang mengurus nya. Pihak hosting tidak berkewajiban memberikan support kepada Anda. Sedangkan managed VPS berarti VPS yang Anda sewa, pihak hosting lah yang mengelola nya, sama seperti pada shared hosting.",
    ]
  },
  {
    question: "Apa itu cloud hosting?",
    answer: [
      "Cloud hosting merupakan salah satu kemajuan terbaru dalam teknologi web hosting. Dengan cloud hosting, beberapa server virtual ditempatkan di atas beberapa tumpukan server fisik yang terkumpul secara terpusat, mirip dengan konsep cluster. Mereka bekerja bersama-sama dalam menjalankan tugas.",
      "Misalnya, jika salah satu server mengalami kegagalan, maka akan digantikan oleh server lainnya yang masih aktif. Hal ini memungkinkan sistem untuk terus berjalan dan menghindari terjadinya downtime.",
      "Konsep ini sangat berbeda dengan teknologi hosting tradisional yang hanya menggunakan satu server fisik. Pada teknologi tradisional, jika server mengalami kegagalan, maka sistem pun akan berhenti beroperasi.",
      "Tujuan dari cloud hosting adalah untuk meningkatkan performa server secara keseluruhan. Dengan uptime yang lebih tinggi dan waktu pemuatan yang lebih cepat, cloud hosting menawarkan solusi yang lebih andal dan efisien untuk kebutuhan hosting modern. Lihat kategori <a href=\"/direktori/cloud-hosting/\">Cloud Hosting</a>.",
    ]
  },
  {
    question: "Apa perbedaan shared hosting dengan cloud hosting?",
    answer: [
      "Cloud hosting bukanlah sebuah tipe produk, melainkan sebuah teknologi atau lingkungan. Sementara <a href=\"/direktori/shared-hosting/\">shared hosting</a>, VPS, dan dedicated server adalah tipe-tipe produk atau tipe web hosting.",
      "Beberapa penyedia layanan lokal sudah menggunakan teknologi cloud hosting, seperti IDCloudHost, Indowebsite, dan DomaiNesia. Namun, yang perlu Anda perhatikan adalah tipe web hosting yang digunakan, apakah itu shared, VPS, atau dedicated.",
      "Baik shared hosting, VPS (Virtual Private Server), maupun dedicated hosting dapat menggunakan teknologi cloud. Oleh karena itu, Anda mungkin menemukan istilah seperti \"cloud shared hosting\", \"cloud VPS hosting\", atau \"cloud dedicated hosting\". Istilah-istilah ini menunjukkan that model hosting tersebut (shared, VPS, atau dedicated) dijalankan pada infrastruktur cloud.",
      "Jika penyedia hosting hanya menyebutnya sebagai cloud hosting tanpa penjelasan lebih lanjut, kemungkinan besar tipe hosting yang dimaksud adalah shared. Lihat kategori <a href=\"/direktori/cloud-hosting/\">Cloud Hosting</a> dan <a href=\"/direktori/shared-hosting/\">Shared Hosting</a>.",
      "Jadi, menurut kami lebih tepat disebut sebagai Shared Hosting on The Cloud. Jika sudah menggunakan SSD, maka namanya menjadi SSD Shared Hosting on The Cloud.",
    ]
  },
  {
    question: "Apakah storage dan bandwidth unlimited benar-benar unlimited?",
    answer: [
      "Secara teknis mustahil untuk mendapatkan bandwidth yang unlimited, karena dalam prakteknya bandwidth yang unlimited pasti memiliki ketentuan-ketentuan.",
      "Anda bisa mendapatkan peringatan dari provider hosting Anda ketika menggunakan terlalu banyak sumber daya.",
      "Dari sekian banyak ketentuan yang kami perhatikan ada istilah yang namanya more bandwidth than you'll ever use. Maksudnya adalah bandwidthnya pasti ada batasan nya.",
      "Namun jika Anda sedang membangun website baru atau website Anda belum menerima banyak pengunjung, persoalan bandwidth ini tidak akan membuat Anda pusing, karena belum membutuhkan resource yang banyak.",
      "Tetapi, jika Anda berencana untuk menyimpan data yang banyak, katakanlah 100GB, 200GB, maka kami pikir ini tidak bisa di lakukan di lingkungan shared hosting. Anda memerlukan hosting dengan sumber daya yang pasti, seperti pada VPS.",
    ]
  },
  {
    question: "Berapa traffic yang direkomendasikan di shared hosting?",
    answer: [
      "Tergantung dari spesifikasi atau paket yang Anda pilih, tapi menurut kami, <a href=\"/direktori/shared-hosting/\">shared hosting</a> hanya akan stable performa nya untuk blog atau website dengan traffic low to medium (biasanya yang kurang dari 25.000/bulan nya).",
      "Angka 25.000 bukanlah sebuah tolak ukur, pastikan Anda selalu memeriksa penggunaan resources (CPU, RAM, I/O, dll) pada cPanel hosting Anda.",
      "Kami tidak mengatakan bahwa shared hosting tidak mampu menampung tingkat kunjungan di atas itu, tapi performa nya tentu saja akan berbeda.",
      "Kalau Anda benar-benar serius di bisnis online Anda dan menginginkan web hosting terbaik untuk menunjang bisnis Anda, maka Anda perlu upgrade ke VPS. Bahkan juga tidak ada salah nya jika Anda memutuskan untuk langsung memilih VPS sedari awal memulai.",
    ]
  },
  {
    question: "Apakah website e-larning bisa berjalan baik di lingkungan shared hosting?",
    answer: [
      "Tidak.",
      "Anda membutuhkan VPS dengan spesifikasi tertentu sesuai kebutuhan Anda. Kami menyarankan Anda untuk mengkonsultasikan nya kepada developer Anda.",
    ]
  },
  {
    question: "Apa itu Reseller Hosting?",
    answer: [
      "Reseller Hosting adalah layanan untuk menjual kembali paket hosting ke klien dengan merek Anda sendiri (white‑label) menggunakan WHM/cPanel. Cocok untuk web developer/agency atau pelaku usaha yang ingin menambah layanan hosting.",
      "Lihat daftar penyedia terbaik di kategori <a href=\"/direktori/reseller-hosting/\">Reseller Hosting</a>.",
    ]
  },
];
---

<section class="prose dark:prose-invert max-w-none mt-12">
  <h2>Punya pertanyaan? Mungkin ada disini jawabannya</h2>
  <Accordion>
    {
      faqs.map(faq => (
        <AccordionItem title={faq.question}>
          {
            faq.answer.map(paragraph => (
              <p set:html={paragraph}></p>
            ))
          }
        </AccordionItem>
      ))
    }
  </Accordion>
</section> 