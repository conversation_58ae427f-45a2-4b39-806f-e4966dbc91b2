---
// Table header component for hosting comparison table
interface Props {
  type?: 'shared' | 'vps' | 'wordpress';
}

const { type = 'shared' } = Astro.props;

const getLoadTimeLabel = () => {
  switch (type) {
    case 'vps': return 'Location';
    case 'shared': return 'Load Time';
    default: return 'Load Time';
  }
};

const getSupportLabel = () => {
  switch (type) {
    case 'vps': return 'Support Type';
    case 'shared': return 'Support';
    default: return 'Support';
  }
};
---

<thead class="bg-gray-50 dark:bg-gray-700">
  <tr>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Rank</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Provider</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Uptime</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{getLoadTimeLabel()}</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{getSupportLabel()}</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price/month</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Guarantee</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Rating</th>
    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
  </tr>
</thead>