---
import Button from '~/components/ui/Button.astro';
import type { HostingProvider } from '~/types';
import { getStatusColor, getBadgeColor, getRankIcon, getStatusDescription } from '~/utils/hostingTableUtils';

interface Props {
  provider: HostingProvider;
}

const { provider } = Astro.props;
---

<tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
  <!-- Rank -->
  <td class="px-4 py-4 whitespace-nowrap">
    <div class="flex items-center justify-center">
      {provider.rank && getRankIcon(provider.rank) ? (
        <span class="text-3xl">{getRankIcon(provider.rank)}</span>
      ) : (
        <span class="font-bold text-2xl">{provider.rank}</span>
      )}
    </div>
  </td>
  
  <!-- Provider -->
  <td class="px-4 py-4">
    <div class="flex flex-col gap-2">
      <a
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        class="font-semibold text-gray-900 dark:text-white hover:text-primary transition-colors"
      >
        {provider.name}
      </a>
      {provider.badge && (
        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border w-fit ${getBadgeColor(provider.badgeColor || 'gray')}`}>
          {provider.badge}
        </span>
      )}
    </div>
  </td>
  
  <!-- Uptime -->
  <td class="px-4 py-4">
    <div>
      <div class="font-semibold">{provider.uptime}</div>
      <div class={`text-xs ${getStatusColor(provider.uptimeStatus)}`}>
        {getStatusDescription(provider.uptimeStatus, 'uptime')}
      </div>
    </div>
  </td>
  
  <!-- Load Time -->
  <td class="px-4 py-4">
    <div>
      <div class="font-semibold">{provider.loadTime}</div>
      <div class={`text-xs ${getStatusColor(provider.loadTimeStatus)}`}>
        {getStatusDescription(provider.loadTimeStatus, 'loadTime')}
      </div>
    </div>
  </td>
  
  <!-- Support -->
  <td class="px-4 py-4">
    <div>
      <div class="font-semibold">{provider.support}</div>
      <div class={`text-xs ${getStatusColor(provider.supportStatus)}`}>
        {getStatusDescription(provider.supportStatus, 'support')}
      </div>
    </div>
  </td>
  
  <!-- Price -->
  <td class="px-4 py-4">
    <div>
      <div class="font-semibold">{provider.price}</div>
      <div class={`text-xs ${getStatusColor(provider.priceStatus)}`}>
        {getStatusDescription(provider.priceStatus, 'price')}
      </div>
    </div>
  </td>
  
  <!-- Guarantee -->
  <td class="px-4 py-4">
    <div>
      {provider.guarantee === 'Tidak ada' ? (
        <span class="text-red-500">⊘ Tidak ada</span>
      ) : (
        <span class="text-green-600">✓ {provider.guarantee}</span>
      )}
    </div>
  </td>
  
  <!-- Rating -->
  <td class="px-4 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <span class="text-yellow-400 mr-1">★</span>
      <span class="font-semibold">{provider.rating}/10</span>
    </div>
  </td>
  
  <!-- Actions -->
  <td class="px-4 py-4 whitespace-nowrap">
    <div class="space-y-2">
      <a
        href={provider.reviewLink}
        class="block text-center px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 hover:no-underline hover:text-primary hover:bg-gray-200 rounded transition-colors"
      >
        Baca Review
      </a>
      <Button
        href={provider.affiliateLink}
        target="_blank"
        rel="nofollow noopener"
        variant="primary"
        class={`text-xs px-3 py-1 w-full ${provider.discount ? 'bg-black hover:bg-gray-800' : ''}`}
        text={provider.discount ? `Beli ${provider.discount}` : 'Beli Sekarang'}
      />
    </div>
  </td>
</tr>