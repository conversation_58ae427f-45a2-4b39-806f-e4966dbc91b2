---
import type { ImageMetadata } from 'astro';

// Import images directly
import idcloudhostLogo from '~/assets/images/providers/idcloudhost-logo.png';
import kencengLogo from '~/assets/images/providers/kenceng-logo.jpg';
const xcloudLogo = 'https://cdn.penasihathosting.com/hosting/xCloud-logo.webp';

// Define interface for hosting recommendation items
interface HostingItem {
  logo: ImageMetadata | string; // Use ImageMetadata type from Astro or string URL
  name: string;
  description: string;
  discount?: {
    amount: string;
    code: string;
  };
  url: string;
  isSponsored?: boolean;
  badge?: string;
}

// Default hosting recommendations, modify as needed
const defaultHosting: HostingItem[] = [
  {
    logo: idcloudhostLogo, // Use imported image object
    name: "IdCloudHost",
    description: "Provider hosting dengan uptime dan speed terbaik.",
    discount: {
      amount: "40%",
      code: "PENASIHATHOSTING"
    },
    url: "https://penasihathosting.com/go/idcloudhost",
    isSponsored: false,
    badge: "Hosting Terbaik"
  },
  {
    logo: kenceng<PERSON>ogo, // Use imported image object
    name: "Kenceng Solusindo",
    description: "Alternatif hosting terbaik dengan uptime dan speed yang juga bisa diandalkan.",
    url: "https://penasihathosting.com/go/kencengsolusindo",
    isSponsored: false,
    badge: "Pilihan Alternatif"
  },
  {
    logo: xcloudLogo, // Use direct URL
    name: "xCloud Hosting",
    description: "Panel manajemen server Linux untuk deployment aplikasi dengan mudah.",
    discount: {
      amount: "Free Plan Tersedia",
      code: "Gratis untuk 1 server - 10 sites"
    },
    url: "https://penasihathosting.com/go/xcloud",
    isSponsored: true,
    badge: "Sponsored"
  },
  {
    logo: 'https://cdn.penasihathosting.com/image/Samudrahost%20Logo.webp',
    name: 'Samudra Host',
    description: 'Hosting Unlimited NVMe dengan harga terjangkau + Free Domain Selamanya.',
    discount: {
      amount: 'Diskon 80%',
      code: 'kode: MERDEKA80'
    },
    url: 'https://penasihathosting.com/go/samudrahost',
    isSponsored: true,
    badge: 'Sponsored'
  }
];

interface Props {
  hosting?: HostingItem[];
}

const { hosting = defaultHosting } = Astro.props;
---

<div>
  <h3 class="text-[16px] font-bold mb-2">Rekomendasi Hosting Murah:</h3>
  
  <div class="space-y-3">
    {hosting.filter(item => !item.isSponsored).map((item) => (
      <a 
        href={item.url}
        target="_blank"
        rel="nofollow noopener noreferrer"
        data-umami-event={`Click Hosting - ${item.name}`}
        class="bg-bg-section dark:bg-slate-800 border border-neutral-200 dark:border-neutral-700 rounded-md overflow-hidden shadow-sm flex group hover:shadow-md transition-shadow no-underline hover:no-underline"
      >
        <div class="p-3 pl-4 flex-grow">
          <div class="flex items-center">
            <div class="w-[60px] flex-shrink-0 mr-3">
              <img src={typeof item.logo === 'string' ? item.logo : item.logo.src} alt={item.name} class="max-w-full max-h-[60px] object-contain" />
            </div>
            <div>
              <h4 class="text-[18px] font-bold mb-1 group-hover:text-accent transition-colors">{item.name}</h4>
              <p class="text-neutral-700 dark:text-neutral-300 text-[14px]">{item.description}</p>
              
              {item.discount && (
                <div class="mt-1">
                  <p>
                    <span class="text-[14px] text-red-600 font-bold inline-flex items-center">
                      🔥 Hemat {item.discount.amount} 🔥
                    </span>
                  </p>
                  <p class="text-[14px] text-neutral-700 dark:text-neutral-300">
                    kode: {item.discount.code}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

      </a>
    ))}
  </div>
  
  {hosting.some(item => item.isSponsored) && (
    <div class="mt-8">
      <div class="mb-4">
        <div class="inline-flex items-center sponsored-badge font-bold text-[15px]">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
          </svg>
          Sponsored Premium Service
        </div>
      </div>
      
      <div class="space-y-4">
        {hosting.filter(item => item.isSponsored).map(item => (
          <a 
            href={item.url}
            target="_blank"
            rel="nofollow noopener noreferrer"
            data-umami-event={`Click Sponsored Hosting - ${item.name}`}
            class="border border-neutral-200 shadow-md rounded-md overflow-hidden flex group transition-all duration-300 hover:-translate-y-1 no-underline hover:no-underline"
          >
              <div class="p-4 pl-5 flex-grow bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200 shadow-lg ring-1 ring-purple-100">
                <div class="flex items-center">
                  <div class="w-[60px] flex-shrink-0 mr-4">
                    <img src={typeof item.logo === 'string' ? item.logo : item.logo.src} alt={item.name} class="max-w-full max-h-[60px] object-contain" />
                  </div>
                  <div>
                    <h4 class="text-[18px] font-bold mb-1 sponsored-text-gradient">{item.name}</h4>
                    <p class="text-neutral-700 dark:text-neutral-300 text-[14px]">{item.description}</p>
                    
                    {item.discount && (
                      <div class="mt-2">
                        <p>
                          <span class="text-[14px] text-red-600 font-bold inline-flex items-center">
                            🔥 {item.discount.amount} 🔥
                          </span>
                        </p>
                        <p class="text-[14px] text-neutral-700 dark:text-neutral-300">
                          {item.discount.code}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

            </a>
        ))}
      </div>
    </div>
  )}
</div>