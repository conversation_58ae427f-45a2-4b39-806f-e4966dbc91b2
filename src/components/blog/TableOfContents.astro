---
interface Heading {
  depth: number; // h1=1, h2=2, etc.
  slug: string;  // ID for linking
  text: string;  // Content of the heading
}

interface Props {
  headings?: Heading[]; // Optional now, will be extracted from DOM if not provided
  title?: string; // Custom title for the TOC
  maxDepth?: number; // How many levels of headings to show
}

const { 
  headings = [], 
  title = "DAFTAR ISI ARTIKEL",
  maxDepth = 2
} = Astro.props;

// Only proceed if we have headings or if we'll extract them client-side
const hasProvidedHeadings = headings && headings.length > 0;

if (!hasProvidedHeadings) {
  // We'll extract headings client-side
}
---

<div>
  <h3 class="text-sm !text-default font-bold mb-4 dark:text-neutral-200">{title}</h3>
  
  {hasProvidedHeadings ? (
    <ul class="space-y-3">
      {headings
        .filter(heading => heading.depth > 1 && heading.depth <= maxDepth)
        .map((heading) => (
          <li class:list={[
            "text-sm",
            { "pl-4": heading.depth === 3 },
            { "pl-8": heading.depth > 3 }
          ]}>
            <a 
              href={`#${heading.slug}`}
              class="toc-link !text-default hover:text-primary transition-colors"
            >
              {heading.text}
            </a>
          </li>
        ))}
    </ul>
  ) : (
    <ul id="toc-container" class="space-y-3">
      <!-- Will be filled dynamically via JavaScript -->
      <li class="text-sm text-muted">Loading table of contents...</li>
    </ul>
  )}
</div>

<script>
  // Handle extracting headings and highlighting the current section
  const initTOC = () => {
    // Small delay to ensure all elements are in the DOM after transition
    setTimeout(() => {
      try {
        const tocLinks = document.querySelectorAll('.toc-link');
        const tocContainer = document.getElementById('toc-container');
        const hasPrebuiltTOC = tocLinks.length > 0;
        
        // Find the content container that has headings
        const contentContainer = document.querySelector('.prose');
        if (!contentContainer) return;
        
        // Get all headings from the content
        const headings = contentContainer.querySelectorAll('h2, h3, h4');
        if (headings.length === 0) return;
    
      // Ensure headings have proper scroll margin to account for sticky header
      headings.forEach(heading => {
        heading.classList.add('scroll-mt-24');
        
        // Ensure headings have IDs for linking
        if (!heading.id && heading.textContent) {
          heading.id = heading.textContent
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '');
        }
      });
    
      // If we need to build the TOC dynamically
      if (!hasPrebuiltTOC && tocContainer) {
        // Clear loading message
        tocContainer.innerHTML = '';
        
        // Build TOC from headings
        headings.forEach(heading => {
          const level = parseInt(heading.tagName.charAt(1));
          if (level === 2) { // Only include H2 headings
            const link = document.createElement('a');
            link.href = `#${heading.id}`;
            link.textContent = heading.textContent;
            link.classList.add(
              'toc-link',
              'text-default',
              'dark:text-default',
              'hover:text-primary',
              'transition-colors'
            );
            
            const listItem = document.createElement('li');
            listItem.classList.add('text-sm');
            
            listItem.appendChild(link);
            tocContainer.appendChild(listItem);
          }
        });
      }
      
      // Set up intersection observer for highlighting current section
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // Remove active class from all links
              document.querySelectorAll('.toc-link').forEach(link => {
                link.classList.remove('text-primary', 'font-medium');
              });
              
              // Add active class to the current section link
              const activeLink = document.querySelector(`.toc-link[href="#${entry.target.id}"]`);
              if (activeLink) {
                activeLink.classList.add('text-primary', 'font-medium');
              }
            }
          });
        },
        {
          rootMargin: '-100px 0px -60% 0px', // Adjust these values as needed
          threshold: 0
        }
      );
      
      // Observe all headings
      headings.forEach(heading => {
        observer.observe(heading);
      });
      
      console.log('Table of Contents initialized successfully');
    } catch (error) {
      console.error('Error initializing Table of Contents:', error);
    }
    }, 100); // Small delay to ensure DOM is ready
  };
  
  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initTOC);
  
  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initTOC);
  
  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initTOC);
  
  // Additional fallback for extra reliability
  window.addEventListener('load', initTOC);
</script> 