---
import { Icon } from 'astro-icon/components';

export interface Props {
  class?: string;
}

const { class: className = '' } = Astro.props;
---

<div class={`mb-8 ${className}`}>
  <div class="relative max-w-global mx-auto">
    <div class="relative">
      <input
        id="search-input"
        type="text"
        placeholder="Cari istilah hosting..."
        class="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm"
        autocomplete="off"
        spellcheck="false"
      />
      <Icon 
        name="tabler:search" 
        class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" 
      />
      <button
        id="clear-search"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors opacity-0 invisible"
        title="Hapus pencarian"
      >
        <Icon name="tabler:x" class="w-4 h-4 text-gray-400" />
      </button>
    </div>
    
    <!-- Search Results Dropdown -->
    <div 
      id="search-results"
      class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto hidden"
    >
      <div id="search-results-content" class="p-2">
        <!-- Results will be populated by JavaScript -->
      </div>
      <div id="search-no-results" class="p-4 text-center text-gray-500 dark:text-gray-400 hidden">
        <Icon name="tabler:alert-circle" class="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p>Tidak ada istilah yang ditemukan</p>
        <p class="text-sm mt-1">Coba gunakan kata kunci yang berbeda</p>
      </div>
    </div>
  </div>
  
  <!-- Search Stats -->
  <div id="search-stats" class="text-center mt-4 hidden">
    <p class="text-sm text-muted">
      Menampilkan <span id="results-count">0</span> hasil untuk "<span id="search-term"></span>"
    </p>
  </div>
  
  <!-- Popular Search Suggestions -->
  <div id="popular-searches" class="mt-6">
    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">🔥 Pencarian Populer</h4>
    <div class="flex flex-wrap gap-2">
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="hosting">
        Hosting
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="domain">
        Domain
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="ssl">
        SSL
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="cpanel">
        cPanel
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="wordpress">
        WordPress
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="bandwidth">
        Bandwidth
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="mysql">
        MySQL
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="ftp">
        FTP
      </button>
      <button class="search-suggestion px-3 py-1 text-sm bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200 dark:border-blue-700 transition-colors cursor-pointer" data-term="dns">
        DNS
      </button>
    </div>
  </div>
</div>

<script>
  interface SearchResult {
    term: string;
    definition: string;
    section: string;
    element: HTMLElement;
  }

  class KamusSearch {
    private searchInput: HTMLInputElement;
    private searchResults: HTMLElement;
    private searchResultsContent: HTMLElement;
    private noResults: HTMLElement;
    private clearButton: HTMLElement;
    private searchStats: HTMLElement;
    private resultsCount: HTMLElement;
    private searchTerm: HTMLElement;
    private allTerms: SearchResult[] = [];
    private currentResults: SearchResult[] = [];

    constructor() {
      this.searchInput = document.getElementById('search-input') as HTMLInputElement;
      this.searchResults = document.getElementById('search-results') as HTMLElement;
      this.searchResultsContent = document.getElementById('search-results-content') as HTMLElement;
      this.noResults = document.getElementById('search-no-results') as HTMLElement;
      this.clearButton = document.getElementById('clear-search') as HTMLElement;
      this.searchStats = document.getElementById('search-stats') as HTMLElement;
      this.resultsCount = document.getElementById('results-count') as HTMLElement;
      this.searchTerm = document.getElementById('search-term') as HTMLElement;

      this.init();
    }

    private init(): void {
      this.extractTerms();
      this.bindEvents();
    }

    private extractTerms(): void {
      // Extract all dictionary terms from the page
      const termElements = document.querySelectorAll('h3');
      let currentSection = 'A';
      
      termElements.forEach((element) => {
        const term = element.textContent?.replace(/\*\*/g, '').trim() || '';
        const definitionElement = element.nextElementSibling;
        const definition = definitionElement?.textContent?.trim() || '';
        
        // Find current section by looking for previous h2 with section-x id
        const allPreviousElements: Element[] = [];
        let currentEl = element.previousElementSibling;
        while (currentEl && allPreviousElements.length < 10) {
          allPreviousElements.push(currentEl);
          if (currentEl.tagName === 'H2') {
            const sectionEl = currentEl.querySelector('span[id^="section-"]');
            if (sectionEl) {
              const sectionMatch = sectionEl.id.match(/section-([a-z])/i);
              if (sectionMatch) {
                currentSection = sectionMatch[1].toUpperCase();
              }
            }
            break;
          }
          currentEl = currentEl.previousElementSibling;
        }

        if (term && definition) {
          this.allTerms.push({
            term,
            definition: definition.length > 150 ? definition.substring(0, 150) + '...' : definition,
            section: currentSection,
            element: element as HTMLElement
          });
        }
      });
      
      console.log('Extracted terms:', this.allTerms.length);
    }

    private bindEvents(): void {
      // Search input events
      this.searchInput.addEventListener('input', (e) => {
        const query = (e.target as HTMLInputElement).value;
        this.handleSearch(query);
      });

      this.searchInput.addEventListener('focus', () => {
        if (this.currentResults.length > 0 && this.searchInput.value.trim()) {
          this.showResults();
        }
      });

      // Clear button
      this.clearButton.addEventListener('click', () => {
        this.clearSearch();
      });

      // Popular search suggestions
      const suggestions = document.querySelectorAll('.search-suggestion');
      suggestions.forEach((suggestion) => {
        suggestion.addEventListener('click', (e) => {
          e.preventDefault();
          const term = (e.target as HTMLElement).dataset.term || '';
          this.searchInput.value = term;
          this.handleSearch(term);
          this.hidePopularSearches();
          // Small delay to ensure search results are processed first
          setTimeout(() => {
            this.searchInput.focus();
          }, 100);
        });
      });

      // Click outside to close results
      document.addEventListener('click', (e) => {
        const popularSearches = document.getElementById('popular-searches');
        if (!this.searchInput.contains(e.target as Node) && 
            !this.searchResults.contains(e.target as Node) &&
            !popularSearches?.contains(e.target as Node)) {
          this.hideResults();
          // Show popular searches again if search input is empty
          if (!this.searchInput.value.trim()) {
            this.showPopularSearches();
          }
        }
      });

      // Keyboard navigation
      this.searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.hideResults();
          this.searchInput.blur();
          // Show popular searches again if search input is empty
          if (!this.searchInput.value.trim()) {
            this.showPopularSearches();
          }
        }
      });
    }

    private handleSearch(query: string): void {
      const trimmedQuery = query.trim();
      
      // Toggle clear button
      if (trimmedQuery) {
        this.clearButton.classList.remove('opacity-0', 'invisible');
      } else {
        this.clearButton.classList.add('opacity-0', 'invisible');
        this.hideResults();
        this.hideStats();
        this.showPopularSearches();
        return;
      }

      // Search logic
      this.currentResults = this.allTerms.filter(item => 
        item.term.toLowerCase().includes(trimmedQuery.toLowerCase()) ||
        item.definition.toLowerCase().includes(trimmedQuery.toLowerCase())
      );

      if (this.currentResults.length > 0) {
        this.displayResults();
        this.showStats(trimmedQuery);
        this.hidePopularSearches();
      } else {
        this.showNoResults();
        this.showStats(trimmedQuery);
        this.hidePopularSearches();
      }
    }

    private displayResults(): void {
      this.searchResultsContent.innerHTML = '';
      
      const maxResults = 8; // Limit results for performance
      const resultsToShow = this.currentResults.slice(0, maxResults);
      
      resultsToShow.forEach((result) => {
        const resultElement = document.createElement('div');
        resultElement.className = 'p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors';
        resultElement.innerHTML = `
          <div class="font-medium text-primary mb-1">${result.term}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">${result.definition}</div>
          <div class="text-xs text-muted">Bagian ${result.section}</div>
        `;
        
        resultElement.addEventListener('click', () => {
          this.scrollToTerm(result.element);
          this.hideResults();
          this.searchInput.blur();
        });
        
        this.searchResultsContent.appendChild(resultElement);
      });

      if (this.currentResults.length > maxResults) {
        const moreElement = document.createElement('div');
        moreElement.className = 'p-3 text-center text-sm text-muted border-t border-gray-200 dark:border-gray-600';
        moreElement.textContent = `+${this.currentResults.length - maxResults} hasil lainnya`;
        this.searchResultsContent.appendChild(moreElement);
      }

      this.showResults();
      this.noResults.classList.add('hidden');
    }

    private showNoResults(): void {
      this.searchResultsContent.innerHTML = '';
      this.noResults.classList.remove('hidden');
      this.showResults();
    }

    private showResults(): void {
      this.searchResults.classList.remove('hidden');
    }

    private hideResults(): void {
      this.searchResults.classList.add('hidden');
    }

    private showStats(query: string): void {
      this.resultsCount.textContent = this.currentResults.length.toString();
      this.searchTerm.textContent = query;
      this.searchStats.classList.remove('hidden');
    }

    private hideStats(): void {
      this.searchStats.classList.add('hidden');
    }

    private clearSearch(): void {
      this.searchInput.value = '';
      this.hideResults();
      this.hideStats();
      this.clearButton.classList.add('opacity-0', 'invisible');
      this.currentResults = [];
      this.showPopularSearches();
    }

    private showPopularSearches(): void {
      const popularSearches = document.getElementById('popular-searches');
      if (popularSearches) {
        popularSearches.classList.remove('hidden');
      }
    }

    private hidePopularSearches(): void {
      const popularSearches = document.getElementById('popular-searches');
      if (popularSearches) {
        popularSearches.classList.add('hidden');
      }
    }

    private scrollToTerm(element: HTMLElement): void {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      
      // Highlight the term briefly
      element.classList.add('highlight-term');
      setTimeout(() => {
        element.classList.remove('highlight-term');
      }, 2000);
    }
  }

  // Initialize search when DOM is loaded
  const initKamusSearch = () => {
    // Small delay to ensure all elements are in the DOM after transition
    setTimeout(() => {
      try {
        new KamusSearch();
        console.log('Kamus search initialized successfully');
      } catch (error) {
        console.error('Error initializing Kamus search:', error);
      }
    }, 100);
  };

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initKamusSearch);
  
  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initKamusSearch);
  
  // Additional fallback for extra reliability
  window.addEventListener('load', initKamusSearch);
</script>

<style>
  /* Highlight animation for found terms */
  .highlight-term {
    @apply bg-yellow-100 dark:bg-yellow-900/50 transition-colors duration-300;
    animation: pulse 1s ease-in-out;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  /* Enhanced focus states */
  #search-input:focus {
    @apply ring-2 ring-primary ring-opacity-50;
  }

  /* Smooth transitions for dropdown */
  #search-results {
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  }

  #search-results.hidden {
    opacity: 0;
    visibility: hidden;
  }

  #search-results:not(.hidden) {
    opacity: 1;
    visibility: visible;
  }
</style>
