---
import { Icon } from 'astro-icon/components';
import KamusNavigation from '~/components/blog/KamusNavigation.astro';
import KamusSearch from '~/components/blog/KamusSearch.astro';
import BackToTopButton from '~/components/common/BackToTopButton.astro';
import { getFormattedDate } from '~/utils/utils';

export interface Props {
  frontmatter: {
    title: string;
    description: string;
    excerpt?: string;
    keywords: string;
    author?: string;
    publishedAt: Date | string;
    updateDate?: Date | string;
  };
}

const { frontmatter } = Astro.props;
// Extract terms from the content
// This would typically be done by parsing the MDX content
// For now, we'll create a structure that matches the kamus content
const alphabetSections = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 
  'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 
  'U', 'V', 'W', 'X', 'Y', 'Z'
];

// Actual term counts based on hosting-dictionary.mdx content
const termCounts = {
  'A': 10, 'B': 10, 'C': 8, 'D': 10, 'E': 10, 'F': 10, 'G': 10, 'H': 10,
  'I': 10, 'J': 6, 'K': 8, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 10,
  'Q': 5, 'R': 10, 'S': 11, 'T': 10, 'U': 10, 'V': 10, 'W': 10, 'X': 5, 'Y': 4, 'Z': 2
};
---

<section class="mx-auto">
  <article class="max-w-global mx-auto px-4 sm:px-6">
    {/* Header Section */}
    <header class="mb-8">
      <div class="mb-6">
        <div class="border-t-8 border-primary dark:border-primary pt-2 mb-4 mt-8">
          <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-white mb-4">
            {frontmatter.title}
          </h1>
        </div>
        <p class="text-xl text-muted mb-6">
          {frontmatter.description}
        </p>
        
        {/* Meta Info */}
        <div class="flex flex-wrap gap-4 text-sm text-muted mb-6">
          {frontmatter.author && (
            <span class="flex items-center gap-1">
              <Icon name="tabler:user" class="w-4 h-4" />
              {frontmatter.author}
            </span>
          )}
          <span class="flex items-center gap-1">
            <Icon name="tabler:calendar" class="w-4 h-4" />
            {getFormattedDate(new Date(frontmatter.publishedAt))}
          </span>
          {frontmatter.updateDate && (
            <span class="flex items-center gap-1">
              <Icon name="tabler:refresh" class="w-4 h-4" />
              Diperbarui {getFormattedDate(new Date(frontmatter.updateDate))}
            </span>
          )}
          <span class="flex items-center gap-1">
            <Icon name="tabler:book" class="w-4 h-4" />
            200+ Istilah
          </span>
        </div>

        {/* Quick Stats */}
        <div class="grid grid-cols-3 gap-4 mb-8 text-center">
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-primary">200+</div>
            <div class="text-sm text-muted">Total Istilah</div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-primary">26</div>
            <div class="text-sm text-muted">Kategori A-Z</div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-primary">Gratis</div>
            <div class="text-sm text-muted">Akses Lengkap</div>
          </div>
        </div>
      </div>

      {/* Search Component */}
      <KamusSearch />
    </header>

    {/* Main Content */}
    <div class="lg:flex lg:gap-8">
      {/* Sticky Navigation */}
      <aside class="lg:w-64 lg:sticky lg:top-24 lg:self-start mb-8 lg:mb-0">
        <KamusNavigation 
          sections={alphabetSections} 
          termCounts={termCounts}
        />
      </aside>

      {/* Dictionary Content */}
      <main class="lg:flex-1 min-w-0">
        {/* Content Slot */}
        <div class="prose prose-lg dark:prose-invert max-w-none" id="kamus-content">
          <slot />
        </div>

      </main>
    </div>

    {/* Footer Section */}

  </article>
  
  <!-- Back to Top Button -->
  <BackToTopButton />
</section>

{/* JavaScript for interactivity */}
<script>
  // Smooth scrolling for navigation links
  function setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const href = link.getAttribute('href');
        if (href) {
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({ 
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      });
    });
  }
  
  
  // Initialize on DOM loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupSmoothScrolling();
  });
</script>

{/* CSS for enhanced styling */}
<style>
  /* Smooth scrolling for the whole page */
  html {
    scroll-behavior: smooth;
  }
  
  /* Enhanced prose styling for dictionary content */
  .prose h2 {
    @apply scroll-mt-24 border-l-4 border-primary pl-4 bg-gray-50 dark:bg-gray-800/50 py-2 rounded-r-lg;
  }
  
  .prose h3 {
    @apply scroll-mt-24 text-primary font-semibold;
  }
  
  /* Dictionary entry styling */
  .prose .dictionary-entry {
    @apply mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  .prose .dictionary-term {
    @apply font-bold text-lg text-primary mb-2 block;
  }
  
  .prose .dictionary-definition {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }
  
</style>
