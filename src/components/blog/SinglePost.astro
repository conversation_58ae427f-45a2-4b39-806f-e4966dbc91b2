---
import Image from '~/components/common/Image.astro';
import PostTags from '~/components/blog/Tags.astro';
import SocialShare from '~/components/common/SocialShare.astro';

import { getFormattedDate } from '~/utils/utils';

import type { Post } from '~/types';

export interface Props {
  post: Post;
  url: string | URL;
}

const { post, url } = Astro.props;
const authorDescription = "Hosting Reviewer"; // Example description - customize as needed
---

<section class="py-8 sm:py-12 lg:py-10 mx-auto">
  <article>
    <header class="mb-2">
      <div class="max-w-global mx-auto px-4 sm:px-6">
        <!-- Wirecutter style thick top border above title -->
        <div class="border-t-8 border-primary dark:border-blue-500 pt-6 pb-2 mx-auto max-w-3xl">
          <h1 class="text-4xl md:text-5xl text-left font-bold leading-tighter tracking-tighter font-heading mb-4">
        {post.title}
      </h1>
          <p class="text-base text-muted dark:text-neutral-400 mb-4">
        {post.excerpt}
      </p>

          <!-- Updated date below title -->
          <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
            <span>Updated <span class="font-medium">{getFormattedDate(post.updateDate ? post.updateDate : post.publishDate)}</span></span>
          </div>
          
          <!-- Social Share below updated date -->
          <SocialShare url={url} text={post.title} class="flex gap-2 mb-4" />
        </div>
      </div>

      {post.image ? (
        <div class="max-w-global mx-auto mt-4 mb-2 px-0 sm:px-6">
          <Image
            src={post.image}
            class="max-w-full mx-auto rounded-none sm:rounded-md"
            widths={[400, 900]}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post?.excerpt || ''}
            width={900}
            height={506}
            loading="eager"
            decoding="async"
          />
        </div>
      ) : null}
      
      <!-- Author info below featured image -->
      <div class="max-w-3xl mx-auto px-4 sm:px-6 mb-8">
        <div>
          {post.author && (
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-blue-400 dark:from-blue-500 dark:to-purple-600 flex items-center justify-center text-white font-bold text-lg mr-4">
                {post.author.charAt(0).toUpperCase()}
              </div>
              <div>
                <span class="block text-base font-medium text-gray-900 dark:text-white">{post.author}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{authorDescription}</span>
              </div>
            </div>
          )}
        </div>
          </div>
    </header>
    
    <div class="mx-auto px-4 sm:px-6 max-w-3xl prose prose-base dark:prose-invert dark:prose-headings:text-slate-300 prose-headings:font-heading prose-headings:leading-tighter prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-img:rounded-md prose-img:shadow-lg mt-8 prose-headings:scroll-mt-[80px] prose-li:my-0 leading-normal">
      <slot />
    </div>
    
    <div class="mx-auto px-6 sm:px-6 max-w-3xl mt-8 flex justify-between flex-col sm:flex-row pt-6 border-t border-gray-200 dark:border-gray-700">
      <PostTags tags={post.tags} class="mr-5 rtl:mr-0 rtl:ml-5" />
    </div>
  </article>
</section>
