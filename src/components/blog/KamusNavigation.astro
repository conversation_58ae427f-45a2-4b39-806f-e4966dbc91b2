---
import { Icon } from 'astro-icon/components';

export interface Props {
  sections: string[];
  termCounts: Record<string, number>;
  class?: string;
}

const { sections, termCounts, class: className = '' } = Astro.props;
---

<nav class={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
  <div class="mb-4">
    <h3 class="font-semibold text-lg mb-2 flex items-center gap-2">
      <Icon name="tabler:menu-2" class="w-5 h-5 text-primary" />
      Navigasi A-Z
    </h3>
    <p class="text-sm text-muted">Klik huruf untuk melompat ke bagian yang diinginkan</p>
  </div>
  
  <div class="grid grid-cols-6 sm:grid-cols-8 lg:grid-cols-4 xl:grid-cols-6 gap-2">
    {sections.map((letter) => (
      <a 
        href={`#section-${letter.toLowerCase()}`}
        class="group flex flex-col items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-primary hover:bg-blue-50 dark:hover:bg-blue-900 dark:hover:bg-opacity-20 transition-all duration-200 text-center"
        title={`${termCounts[letter] || 0} istilah dimulai dengan huruf ${letter}`}
      >
        <span class="font-bold text-lg text-gray-700 dark:text-gray-300 group-hover:text-primary transition-colors">
          {letter}
        </span>
        <span class="text-xs text-muted group-hover:text-primary transition-colors mt-1">
          {termCounts[letter] || 0}
        </span>
      </a>
    ))}
  </div>
  
  <!-- Quick Actions -->
  <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
    <h4 class="font-medium text-sm text-muted mb-3">Aksi Cepat</h4>
    <div class="space-y-2">
      <button 
        onclick="document.getElementById('search-input')?.focus()"
        class="w-full flex items-center gap-2 p-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors"
      >
        <Icon name="tabler:search" class="w-4 h-4 text-muted" />
        <span>Cari Istilah</span>
        <span class="text-xs text-muted ml-auto">Ctrl+K</span>
      </button>
      
      <button 
        id="bookmark-toggle"
        class="w-full flex items-center gap-2 p-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors"
      >
        <Icon name="tabler:bookmark" class="w-4 h-4 text-muted" />
        <span>Bookmark Halaman</span>
      </button>
    </div>
  </div>
</nav>

<script>
  interface Bookmark {
    url: string;
    title: string;
    timestamp: string;
  }

  // Keyboard shortcut for search
  document.addEventListener('keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      const searchInput = document.getElementById('search-input') as HTMLInputElement;
      searchInput?.focus();
    }
  });
  
  // Bookmark functionality
  const bookmarkBtn = document.getElementById('bookmark-toggle');
  if (bookmarkBtn) {
    bookmarkBtn.addEventListener('click', () => {
      // Simple bookmark to localStorage
      const bookmarks: Bookmark[] = JSON.parse(localStorage.getItem('kamus-bookmarks') || '[]');
      const currentPage: Bookmark = {
        url: window.location.href,
        title: document.title,
        timestamp: new Date().toISOString()
      };
      
      const existingIndex = bookmarks.findIndex((b: Bookmark) => b.url === currentPage.url);
      if (existingIndex > -1) {
        bookmarks.splice(existingIndex, 1);
        bookmarkBtn.innerHTML = `
          <svg class="w-4 h-4 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
          </svg>
          <span>Bookmark Halaman</span>
        `;
      } else {
        bookmarks.push(currentPage);
        bookmarkBtn.innerHTML = `
          <svg class="w-4 h-4 text-primary" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
          </svg>
          <span>Tersimpan!</span>
        `;
      }
      
      localStorage.setItem('kamus-bookmarks', JSON.stringify(bookmarks));
    });
    
    // Check if current page is bookmarked
    const bookmarks: Bookmark[] = JSON.parse(localStorage.getItem('kamus-bookmarks') || '[]');
    const isBookmarked = bookmarks.some((b: Bookmark) => b.url === window.location.href);
    if (isBookmarked) {
      bookmarkBtn.innerHTML = `
        <svg class="w-4 h-4 text-primary" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
        </svg>
        <span>Tersimpan!</span>
      `;
    }
  }
</script>

<style>
  /* Highlight active section */
  .nav-link-active {
    @apply border-primary bg-blue-50 dark:bg-blue-900 text-primary;
  }
  
  /* Enhance hover states */
  nav a:hover {
    transform: translateY(-1px);
  }
</style>
