---
import { Icon } from 'astro-icon/components';
import { getPermalink } from '~/utils/permalinks';

export interface FilterItem {
  slug: string;
  title: string;
  count?: number;
  icon?: string;
}

export interface Props {
  categories?: FilterItem[];
  tags?: FilterItem[];
  postTypes?: FilterItem[];
  activeItem?: string;
  isCategory?: boolean;
  isTag?: boolean;
  isPostType?: boolean;
}

const {
  categories = [],
  tags = [],
  postTypes = [],
  activeItem = '',
  // These props can be used in future enhancements
  // isCategory = false,
  // isTag = false,
  // isPostType = false,
} = Astro.props;

// Default post types if none provided
const defaultPostTypes: FilterItem[] = [
  { slug: 'all', title: 'Semua', icon: 'tabler:article' },
  { slug: 'guide-page', title: 'Panduan', icon: 'tabler:book' },
  { slug: 'review-hosting', title: 'Review', icon: 'tabler:star' },
  { slug: 'tutorial', title: 'Tutorial', icon: 'tabler:school' },
];

const finalPostTypes = postTypes.length > 0 ? postTypes : defaultPostTypes;

// Helper to get the correct permalink
const getFilterPermalink = (item: FilterItem, type: 'category' | 'tag' | 'post-type') => {
  if (type === 'category') {
    return getPermalink(item.slug, 'category');
  } else if (type === 'tag') {
    return getPermalink(item.slug, 'tag');
  } else if (type === 'post-type') {
    if (item.slug === 'all') {
      return getPermalink('', 'blog');
    } else if (item.slug === 'guide-page') {
      return getPermalink('panduan', 'category');
    } else if (item.slug === 'review-hosting') {
      return getPermalink('review-hosting', 'category');
    } else if (item.slug === 'tutorial') {
      return getPermalink('tutorial', 'category');
    }
  }
  return '#';
};

// Helper to check if an item is active
const isItemActive = (item: FilterItem) => {
  return activeItem === item.slug;
};
---

<div class="mb-8">
  {finalPostTypes.length > 0 && (
    <div class="mb-6">
      {/* Horizontal layout with 2 columns */}
      <div class="flex flex-col sm:flex-row gap-4 rounded-lg">
        <div class="font-semibold text-gray-700 dark:text-gray-300">
          Jenis Konten:
        </div>
        <div class="flex flex-wrap justify-center gap-2">
          {finalPostTypes.map((type) => (
            <a
              href={getFilterPermalink(type, 'post-type')}
              class={`flex items-center px-3 py-1.5 rounded-full text-sm border transition-colors ${
                isItemActive(type) || (type.slug === 'all' && !activeItem)
                  ? 'bg-bg-muted text-default border-primary'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-default dark:text-default hover:border-primary hover:text-primary dark:hover:text-primary'
              }`}
            >
              {type.icon && <Icon name={type.icon} class="w-4 h-4 mr-1" />}
              {type.title}
              {type.count !== undefined && (
                <span class={`ml-1 text-xs ${
                  isItemActive(type) || (type.slug === 'all' && !activeItem)
                    ? 'text-muted'
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  ({type.count})
                </span>
              )}
            </a>
          ))}
        </div>
      </div>
    </div>
  )}

  {categories.length > 0 && (
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3 dark:text-white">Kategori</h3>
      <div class="flex flex-wrap gap-2">
        {categories.map((category) => (
          <a
            href={getFilterPermalink(category, 'category')}
            class={`px-3 py-1 rounded-full text-sm border transition-colors ${
              isItemActive(category)
                ? 'bg-bg-muted text-default border-primary'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-default dark:text-default hover:border-primary hover:text-primary dark:hover:text-primary'
            }`}
          >
            {category.title}
            {category.count !== undefined && (
              <span class={`ml-1 text-xs ${
                isItemActive(category)
                  ? 'text-muted'
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                ({category.count})
              </span>
            )}
          </a>
        ))}
      </div>
    </div>
  )}

  {tags.length > 0 && (
    <div>
      <h3 class="text-lg font-semibold mb-3 dark:text-white">Tag</h3>
      <div class="flex flex-wrap gap-2">
        {tags.map((tag) => (
          <a
            href={getFilterPermalink(tag, 'tag')}
            class={`px-3 py-1 rounded-full text-sm border transition-colors ${
              isItemActive(tag)
                ? 'bg-bg-muted text-default border-primary'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-default dark:text-default hover:border-primary hover:text-primary dark:hover:text-primary'
            }`}
          >
            {tag.title}
            {tag.count !== undefined && (
              <span class={`ml-1 text-xs ${
                isItemActive(tag)
                  ? 'text-muted'
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                ({tag.count})
              </span>
            )}
          </a>
        ))}
      </div>
    </div>
  )}
</div>
