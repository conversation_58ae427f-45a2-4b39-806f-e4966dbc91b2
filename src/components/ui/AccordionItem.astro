---
export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<details class="accordion-item border-b border-border py-3">
  <summary class="accordion-title text-lg font-medium cursor-pointer list-none flex justify-between items-center">
    {title}
    <span class="accordion-icon text-muted transition-transform duration-200 ease-out">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
        <path d="m6 9 6 6 6-6"/>
      </svg>
    </span>
  </summary>
  <div class="accordion-content pt-3">
    <slot />
  </div>
</details>

<style>
  .accordion-item[open] .accordion-icon {
    transform: rotate(180deg);
  }
  .accordion-item:last-of-type {
    border-bottom: none;
  }

  .accordion-item[open] {
    @apply bg-bg-muted;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    margin-left: -0.75rem;
    margin-right: -0.75rem;
  }

  /* Hide default marker for summary */
  .accordion-title::-webkit-details-marker {
    display: none;
  }
  .accordion-title {
    list-style-type: none; /* For Firefox */
  }
</style> 