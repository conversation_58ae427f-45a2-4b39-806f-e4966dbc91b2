---
import { Icon } from 'astro-icon/components';

export interface Props {
  href: string;
  target?: string;
  color?: 'primary' | 'secondary' | 'accent' | 'default';
  size?: 'sm' | 'base' | 'lg';
  className?: string;
  withArrow?: boolean;
}

const {
  href,
  target,
  color = 'primary',
  size = 'base',
  className = '',
  withArrow = true
} = Astro.props;

// Size classes
const sizeClasses = {
  sm: 'text-sm py-1 px-2',
  base: 'text-base py-1.5 px-3',
  lg: 'text-lg py-2 px-4'
};

// Color classes
const colorClasses = {
  primary: 'bg-primary hover:bg-primary/90 text-white',
  secondary: 'bg-secondary hover:bg-secondary/90 text-white',
  accent: 'bg-accent hover:bg-accent/90 text-white',
  default: 'bg-bg-muted hover:bg-bg-section text-default'
};

// Combine classes
const classes = `inline-flex items-center rounded ${sizeClasses[size]} ${colorClasses[color]} font-medium transition-colors ${className}`;
---

<a href={href} target={target} class={classes}>
  <span><slot /></span>
  {withArrow && <Icon name="tabler:arrow-right" class="w-4 h-4 ml-1.5" />}
</a> 