---
export interface Props {
  href: string;
  target?: string;
  color?: 'primary' | 'secondary' | 'accent' | 'text' | 'none';
  size?: 'xs' | 'sm' | 'base' | 'lg';
  className?: string;
}

const {
  href,
  target,
  color = 'text',
  size = 'base',
  className = '',
} = Astro.props;

// Size classes
const sizeClasses = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg'
};

// Color classes
const colorClasses = {
  primary: 'text-primary hover:text-primary/80',
  secondary: 'text-secondary hover:text-secondary/80',
  accent: '!text-accent hover:!text-[var(--aw-color-accent-hover)] !font-bold',
  text: 'text-default hover:text-heading',
  none: ''
};

// Combine classes
const classes = `inline-flex items-center ${sizeClasses[size]} ${colorClasses[color]} font-medium ${className}`;
---

<a href={href} target={target} class={classes}>
  <slot />
</a> 