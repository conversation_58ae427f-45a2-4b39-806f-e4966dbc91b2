---
import Button from '../../ui/Button.astro';

// Simple location slug function
function getLocationSlug(location: string): string {
  return location.toLowerCase().replace(/\s+/g, '-');
}

interface Provider {
  id: string;
  collection: string;
  data: {
    name: string;
    displayName?: string;
    slug: string;
    logo: string;
    description: string;
    pricing: {
      startingPrice: number;
      promoCode?: string;
      promoDescription?: string;
    };
    datacenters: Array<{
      location: string;
      flag?: string;
    }>;
    affiliateLink?: string;
    website: string;
  };
}

export interface Props {
  providers: Provider[];
  title?: string;
}

const { 
  providers = [],
  title = "Provider Serupa"
} = Astro.props;

// Only show if we have providers
if (providers.length === 0) return null;
---

<section class="bg-bg-muted py-12">
  <div class="mx-auto max-w-4xl px-4 md:px-6">
    <h2 class="text-2xl font-bold text-heading text-center mb-8">{title}</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {providers.map((related) => (
        <div class="rounded-lg border border-gray-200 bg-white p-5 shadow-sm hover:shadow-md transition-shadow">
          <!-- Header with Logo and Name -->
          <div class="flex items-center space-x-3 mb-4">
            <img
              src={related.data.logo}
              alt={`${related.data.name} logo`}
              class="h-12 w-12 rounded-lg object-contain border border-gray-200 bg-white p-1"
              loading="lazy"
            />
            <div class="flex-1">
              <h3 class="font-semibold text-heading text-base">
                <a href={`/direktori/hosting/${related.data.slug}/`} class="hover:text-primary transition-colors">
                  {related.data.displayName || related.data.name}
                </a>
              </h3>
              <div class="flex items-center gap-2 mt-1">
                <span class="text-sm font-medium text-primary">
                  Rp {related.data.pricing.startingPrice.toLocaleString('id-ID')}
                </span>
                <span class="text-xs text-muted">/bulan</span>
                {related.data.pricing.promoDescription && (
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-700">
                    Promo
                  </span>
                )}
              </div>
            </div>
          </div>

          <!-- Description -->
          <p class="text-sm text-muted mb-4 line-clamp-3 leading-relaxed">
            {related.data.description}
          </p>

          <!-- Data Centers -->
          {related.data.datacenters && related.data.datacenters.length > 0 && (
            <div class="mb-4">
              <div class="text-xs font-medium text-heading mb-2">Data Center:</div>
              <div class="flex flex-wrap gap-1">
                {related.data.datacenters.slice(0, 3).map((dc) => (
                  <a 
                    href={`/direktori/lokasi/${getLocationSlug(dc.location)}/`} 
                    class="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-gray-100 text-xs text-gray-700 hover:bg-gray-200 transition-colors"
                  >
                    <span class="text-sm">{dc.flag}</span>
                    {dc.location}
                  </a>
                ))}
                {related.data.datacenters.length > 3 && (
                  <span class="inline-flex items-center px-2 py-1 rounded-md bg-gray-100 text-xs text-gray-700">
                    +{related.data.datacenters.length - 3} lainnya
                  </span>
                )}
              </div>
            </div>
          )}

          <!-- Promo Info -->
          {related.data.pricing.promoDescription && (
            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div class="flex items-center gap-2 mb-1">
                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span class="text-xs font-medium text-red-700">Promo Tersedia</span>
              </div>
              <div class="text-xs text-red-600">{related.data.pricing.promoDescription}</div>
            </div>
          )}

          <!-- CTA Button -->
          <Button
            href={`/direktori/hosting/${related.data.slug}/`}
            class="w-full rounded-lg px-4 py-2 text-center text-sm font-medium transition-colors"
            variant="primary"
          >
            Lihat Detail
          </Button>
        </div>
      ))}
    </div>
  </div>
</section>
