---
export interface Props {
  category: {
    id: string;
    data: {
      title: string;
      description: string;
      icon: string;
      providerCount: number;
      featured?: boolean;
    };
  };
  variant?: 'default' | 'featured' | 'compact';
  showDescription?: boolean;
}

const { 
  category, 
  variant = 'default',
  showDescription = true
} = Astro.props;

// Icon mapping for common hosting categories
const getIconSvg = (iconName: string) => {
  const icons = {
    'tabler:server': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>`,
    'tabler:cloud': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>`,
    'tabler:brand-wordpress': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>`,
    'tabler:server-2': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2z"></path>`,
    'tabler:database': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>`,
    'default': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>`
  };
  
  return icons[iconName as keyof typeof icons] || icons.default;
};

const iconSvg = getIconSvg(category.data.icon);
---

<a
  href={`/direktori/${category.id}/`}
  class={`direktori-category-card group block rounded-xl border bg-white shadow-md ${
    category.data.featured ? 'border-primary/30 ring-1 ring-primary/20 direktori-featured-glow' : 'border-gray-200'
  } ${
    variant === 'featured' ? 'p-8' :
    variant === 'compact' ? 'p-4' :
    'p-6'
  }`}
>
  <!-- Featured Badge -->
  {category.data.featured && (
    <div class="mb-4">
      <span class="inline-flex items-center px-2 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
        Populer
      </span>
    </div>
  )}

  <!-- Icon and Title -->
  <div class="flex items-center space-x-4 mb-3">
    <div class={`flex items-center justify-center rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors ${
      variant === 'featured' ? 'h-16 w-16' :
      variant === 'compact' ? 'h-10 w-10' :
      'h-12 w-12'
    }`}>
      <svg 
        class={`text-primary ${
          variant === 'featured' ? 'h-8 w-8' :
          variant === 'compact' ? 'h-5 w-5' :
          'h-6 w-6'
        }`} 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        set:html={iconSvg}
      >
      </svg>
    </div>
    
    <div class="flex-1">
      <h3 class={`font-semibold text-heading group-hover:text-primary transition-colors ${
        variant === 'featured' ? 'text-xl' :
        variant === 'compact' ? 'text-base' :
        'text-lg'
      }`}>
        {category.data.title}
      </h3>
      
      <p class={`text-muted ${
        variant === 'compact' ? 'text-xs' : 'text-sm'
      }`}>
        {category.data.providerCount} provider
      </p>
    </div>
    
    <!-- Arrow Icon -->
    <div class="flex-shrink-0">
      <svg 
        class="h-5 w-5 text-gray-400 group-hover:text-primary transition-colors" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </div>
  </div>

  <!-- Description -->
  {showDescription && variant !== 'compact' && (
    <p class={`text-muted ${
      variant === 'featured' ? 'text-base' : 'text-sm'
    } line-clamp-2`}>
      {category.data.description}
    </p>
  )}

  <!-- Stats for featured variant -->
  {variant === 'featured' && (
    <div class="mt-4 pt-4 border-t border-gray-100">
      <div class="flex items-center justify-between text-sm">
        <span class="text-muted">Provider tersedia</span>
        <span class="font-medium text-heading">{category.data.providerCount}</span>
      </div>
    </div>
  )}
</a>

<style>
  /* Featured category glow effect */
  .ring-primary\/20 {
    animation: featured-glow 4s ease-in-out infinite alternate;
  }
  
  @keyframes featured-glow {
    0% {
      box-shadow: 0 0 5px rgba(var(--aw-color-primary-rgb), 0.2);
    }
    100% {
      box-shadow: 0 0 15px rgba(var(--aw-color-primary-rgb), 0.4), 0 0 25px rgba(var(--aw-color-primary-rgb), 0.2);
    }
  }
</style>
