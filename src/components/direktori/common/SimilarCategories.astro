---
import { getCollection } from 'astro:content';
import { getPermalink } from '~/utils/permalinks';
import { getSimilarCategories } from '~/utils/categorySimilarity';

interface Props {
  categorySlug: string;
  title?: string;
  maxItems?: number;
  strategy?: 'blended' | 'curated' | 'data';
}

const { categorySlug, title = 'Kategori Terkait', maxItems = 6, strategy = 'blended' } = Astro.props as Props;

// Load all categories to resolve titles from slugs
const categories = await getCollection('hosting-categories');
const titleBySlug = new Map(categories.map((c) => [c.id, c.data.title as string]));

// Curated mapping of similar categories per category slug
const curatedMap: Record<string, string[]> = {
  'shared-hosting': ['cloud-hosting', 'reseller-hosting', 'unmanaged-vps', 'dedicated-server'],
  'cloud-hosting': ['shared-hosting', 'managed-vps', 'unmanaged-vps', 'wordpress-hosting'],
  'wordpress-hosting': ['shared-hosting', 'managed-vps', 'unmanaged-vps', 'cloud-hosting'],
  'unmanaged-vps': ['managed-vps', 'cloud-hosting', 'dedicated-server', 'shared-hosting'],
  'managed-vps': ['unmanaged-vps', 'cloud-hosting', 'dedicated-server', 'wordpress-hosting'],
  'dedicated-server': ['unmanaged-vps', 'managed-vps', 'colocation-server', 'bare-metal-server'],
  'colocation-server': ['dedicated-server', 'bare-metal-server', 'unmanaged-vps'],
  'bare-metal-server': ['dedicated-server', 'colocation-server', 'unmanaged-vps'],
  'cpanel-hosting': ['directadmin-hosting', 'plesk-hosting', 'shared-hosting'],
  'directadmin-hosting': ['cpanel-hosting', 'plesk-hosting', 'shared-hosting'],
  'plesk-hosting': ['cpanel-hosting', 'directadmin-hosting', 'shared-hosting'],
  'email-hosting': ['shared-hosting', 'wordpress-hosting', 'cloud-hosting'],
  'reseller-hosting': ['shared-hosting', 'domain', 'reseller-domain'],
  'reseller-domain': ['domain', 'reseller-hosting'],
  'domain': ['reseller-domain', 'reseller-hosting', 'shared-hosting'],
  'vps-windows': ['unmanaged-vps', 'managed-vps', 'cloud-hosting']
};

// Build the list of similar slugs
let similarSlugs: string[] = [];

if (strategy === 'curated' || strategy === 'blended') {
  similarSlugs = curatedMap[categorySlug] || [];
}

if ((strategy === 'data') || (strategy === 'blended' && similarSlugs.length < maxItems)) {
  const exclude = similarSlugs.slice();
  const needed = Math.max(0, maxItems - similarSlugs.length);
  const dataSimilar = await getSimilarCategories(categorySlug, needed || maxItems, exclude);
  for (const s of dataSimilar) {
    if (!similarSlugs.includes(s.slug)) similarSlugs.push(s.slug);
  }
}

similarSlugs = similarSlugs.slice(0, maxItems);

// Resolve to items with title and href, only keep ones that exist
const items = similarSlugs
  .map((slug) => ({ slug, title: titleBySlug.get(slug), href: getPermalink(`/direktori/${slug}/`) }))
  .filter((i) => Boolean(i.title));
---

{items.length > 0 && (
  <section class="pt-8">
    <div class="mx-auto max-w-global">
      <div class="rounded-md shadow-md border border-gray-200 bg-white">
        <div class="px-6 pt-6">
          <h2 class="text-xl font-semibold" id="similar-categories-heading">{title}</h2>
        </div>
        <div class="px-6 pb-6 pt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-3 gap-x-12">
          {items.map((item) => (
            <a href={item.href} aria-label={`Lihat kategori ${item.title}`} class="group no-underline">
              <div class="flex items-center justify-between">
                <span class="text-sm group-hover:text-primary transition-colors">{item.title}</span>
                <span class="text-orange-600 group-hover:text-orange-700 transition-transform group-hover:translate-x-0.5">›</span>
              </div>
            </a>
          ))}
        </div>
      </div>
    </div>
  </section>
)}
