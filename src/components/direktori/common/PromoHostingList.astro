---
import { getCollection } from 'astro:content';
import Button from '~/components/ui/Button.astro';

// Fetch promo-enabled providers
const allProviders = await getCollection('hosting-providers');
const filteredPromoProviders = allProviders.filter((provider) => 
  provider.data.isActive && 
  (provider.data.pricing.promoCode || provider.data.pricing.promoDescription)
);

// Prioritize sponsored -> promoted -> recommended -> others
const sponsored = filteredPromoProviders.filter(p => p.data.badges?.some(b => b.type === 'sponsored'));
const promoted = filteredPromoProviders.filter(p => p.data.badges?.some(b => b.type === 'promoted') && !p.data.badges?.some(b => b.type === 'sponsored'));
const regular = filteredPromoProviders.filter(p => {
  const types = p.data.badges?.map(b => b.type) || [];
  return !types.includes('sponsored') && !types.includes('promoted');
});

// Sort inside each bucket (alphabetically for stability)
sponsored.sort((a,b) => a.data.name.localeCompare(b.data.name));
promoted.sort((a,b) => a.data.name.localeCompare(b.data.name));
regular.sort((a,b) => a.data.name.localeCompare(b.data.name));

// Compose final list and limit to 3 items for compact block
const topPromos = [...sponsored, ...promoted, ...regular].slice(0, 3);
---

{topPromos.length > 0 && (
  <section class="my-6">
    <div class="rounded-md border-2 border-primary/20 bg-bg-section shadow-md hover:shadow-lg">
      <div class="px-4 py-3 border-b border-primary/20 flex items-center justify-between flex-wrap gap-2">
        <div>
          <h3 class="text-base font-semibold text-heading">Promo Hosting dari PenasihatHosting</h3>
          <p class="text-xs text-muted">Kumpulan diskon dan kupon terbaru pilihan tim PenasihatHosting.</p>
        </div>
      </div>

      <div class="p-4 grid grid-cols-1 md:grid-cols-3 gap-3">
        {topPromos.map((provider) => (
          <div class="rounded-lg border border-gray-200 bg-white p-4 hover:shadow-md transition flex flex-col h-full">
            <div class="flex items-center mb-3">
              <img src={provider.data.logo} alt={`${provider.data.name} logo`} class="h-10 w-14 object-contain border border-gray-200 rounded bg-white p-1 mr-3" loading="lazy" />
              <div class="min-w-0">
                <a href={`/direktori/hosting/${provider.data.slug}/`} class="text-sm font-semibold text-primary hover:underline">
                  {provider.data.displayName || provider.data.name}
                </a>
                <div class="mt-1 flex flex-wrap items-center gap-1">
                  {provider.data.badges && provider.data.badges.map((badge) => {
                    if (badge.type === 'sponsored') {
                      return (
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium bg-purple-50 text-purple-700 border border-purple-200">👑 {badge.label}</span>
                      );
                    }
                    if (badge.type === 'promoted') {
                      return (
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium bg-orange-50 text-orange-700 border border-orange-200">🎯 {badge.label}</span>
                      );
                    }
                    if (badge.type === 'recommended') {
                      return (
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium bg-green-50 text-green-700 border border-green-200">⭐ {badge.label}</span>
                      );
                    }
                    if (badge.type === 'verified') {
                      return (
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-md text-[10px] font-medium bg-blue-50 text-blue-700 border border-blue-200">✓ {badge.label}</span>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            </div>

            {(provider.data.pricing.promoDescription || provider.data.pricing.promoCode) && (
              <div class="mb-3">
                {provider.data.pricing.promoDescription && (
                  <div class="text-xs text-heading font-medium">🎉 {provider.data.pricing.promoDescription}</div>
                )}
                {provider.data.pricing.promoCode && (
                  <div class="text-[11px] text-muted mt-1">Kode: <span class="font-mono bg-gray-100 px-1.5 py-0.5 rounded border border-gray-200">{provider.data.pricing.promoCode}</span></div>
                )}
              </div>
            )}

            <div class="flex items-center justify-between mt-auto pt-2">
              <div class="text-[11px] text-muted">Mulai {provider.data.pricing.startingPrice === 0 ? 'GRATIS' : `Rp ${provider.data.pricing.startingPrice.toLocaleString('id-ID')}`}{provider.data.pricing.startingPrice > 0 && '/bulan'}</div>
              <Button href={provider.data.affiliateLink || provider.data.website} target="_blank" rel="noopener" variant="primary" class="text-xs px-3 py-1.5">Ambil Promo</Button>
            </div>
          </div>
        ))}
      </div>

      <div class="px-4 pb-4">
        <a href="/promo-hosting/" class="inline-flex items-center text-primary text-xs font-medium hover:text-primary/80">Lihat semua promo hosting →</a>
      </div>
    </div>
  </section>
)}
