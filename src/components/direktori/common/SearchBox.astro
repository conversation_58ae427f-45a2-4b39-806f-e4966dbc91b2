---
export interface Props {
  placeholder?: string;
  variant?: 'default' | 'large' | 'compact';
  showFilters?: boolean;
  categories?: Array<{
    id: string;
    title: string;
  }>;
}

const { 
  placeholder = "Cari provider hosting...",
  variant = 'default',
  showFilters = false,
  categories = []
} = Astro.props;
---

<div class={`relative ${
  variant === 'large' ? 'max-w-2xl mx-auto' :
  variant === 'compact' ? 'max-w-sm' :
  'max-w-md mx-auto'
}`}>
  <!-- Main Search Input -->
  <div class="relative">
    <input
      type="text"
      placeholder={placeholder}
      class={`direktori-search-input w-full rounded-lg transition-colors ${
        variant === 'large' ? 'px-6 py-4 pl-12 text-lg' :
        variant === 'compact' ? 'px-3 py-2 pl-9 text-sm' :
        'px-4 py-3 pl-10'
      }`}
      id="search-input"
      autocomplete="off"
    />
    
    <!-- Search Icon -->
    <div class={`absolute inset-y-0 left-0 flex items-center ${
      variant === 'large' ? 'pl-4' :
      variant === 'compact' ? 'pl-3' :
      'pl-3'
    }`}>
      <svg 
        class={`text-gray-400 ${
          variant === 'large' ? 'h-6 w-6' :
          variant === 'compact' ? 'h-4 w-4' :
          'h-5 w-5'
        }`} 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </div>
    
    <!-- Clear Button -->
    <button
      type="button"
      class={`absolute inset-y-0 right-0 items-center hidden ${
        variant === 'large' ? 'pr-4' :
        variant === 'compact' ? 'pr-3' :
        'pr-3'
      }`}
      id="clear-search"
    >
      <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Quick Filters -->
  {showFilters && categories.length > 0 && (
    <div class="mt-3">
      <div class="flex flex-wrap gap-2">
        <span class="text-sm font-medium text-heading">Kategori:</span>
        {categories.slice(0, 5).map((category) => (
          <button
            type="button"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700 hover:bg-primary hover:text-white transition-colors"
            data-category={category.id}
          >
            {category.title}
          </button>
        ))}
      </div>
    </div>
  )}

  <!-- Search Results Dropdown -->
  <div 
    class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden"
    id="search-results"
  >
    <div class="p-4">
      <div class="text-sm text-muted mb-2">Hasil pencarian:</div>
      <div id="search-results-list" class="space-y-2">
        <!-- Results will be populated by JavaScript -->
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div 
    class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden"
    id="search-loading"
  >
    <div class="p-4 text-center">
      <div class="inline-flex items-center space-x-2">
        <svg class="animate-spin h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm text-muted">Mencari...</span>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const clearButton = document.getElementById('clear-search') as HTMLButtonElement;
    const searchResults = document.getElementById('search-results') as HTMLElement;
    const searchLoading = document.getElementById('search-loading') as HTMLElement;
    const searchResultsList = document.getElementById('search-results-list') as HTMLElement;
    
    let searchTimeout: NodeJS.Timeout;
    
    if (!searchInput) return;

    // Show/hide clear button
    function toggleClearButton() {
      if (searchInput.value.length > 0) {
        clearButton?.classList.remove('hidden');
        clearButton?.classList.add('flex');
      } else {
        clearButton?.classList.add('hidden');
        clearButton?.classList.remove('flex');
      }
    }

    // Clear search
    clearButton?.addEventListener('click', function() {
      searchInput.value = '';
      toggleClearButton();
      hideResults();
      searchInput.focus();
    });

    // Hide results
    function hideResults() {
      searchResults?.classList.add('hidden');
      searchLoading?.classList.add('hidden');
    }

    // Show loading
    function showLoading() {
      hideResults();
      searchLoading?.classList.remove('hidden');
    }

    // Show results
    function showResults() {
      searchLoading?.classList.add('hidden');
      searchResults?.classList.remove('hidden');
    }

    // Perform search
    async function performSearch(query: string) {
      if (query.length < 2) {
        hideResults();
        return;
      }

      showLoading();

      try {
        // Simulate API call - replace with actual search endpoint
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Mock results - replace with actual API call
        const mockResults = [
          { name: 'IdCloudHost', slug: 'idcloudhost', description: 'Cloud hosting Indonesia' },
          { name: 'Domainesia', slug: 'domainesia', description: 'Domain dan hosting Indonesia' },
          { name: 'Niagahoster', slug: 'niagahoster', description: 'Web hosting terpercaya' }
        ].filter(provider => 
          provider.name.toLowerCase().includes(query.toLowerCase()) ||
          provider.description.toLowerCase().includes(query.toLowerCase())
        );

        // Populate results
        if (searchResultsList) {
          searchResultsList.innerHTML = mockResults.map(provider => `
            <a href="/direktori-hosting/hosting/${provider.slug}/" class="block p-2 rounded hover:bg-gray-50 transition-colors">
              <div class="font-medium text-heading">${provider.name}</div>
              <div class="text-sm text-muted">${provider.description}</div>
            </a>
          `).join('');
        }

        showResults();

      } catch (error) {
        console.error('Search error:', error);
        hideResults();
      }
    }

    // Search input handler
    searchInput.addEventListener('input', function(e) {
      const query = (e.target as HTMLInputElement).value;
      toggleClearButton();

      // Clear previous timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      // Debounce search
      searchTimeout = setTimeout(() => {
        performSearch(query);
      }, 300);
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
      if (!searchInput.contains(e.target as Node) && 
          !searchResults?.contains(e.target as Node) &&
          !searchLoading?.contains(e.target as Node)) {
        hideResults();
      }
    });

    // Category filter buttons
    const categoryButtons = document.querySelectorAll('[data-category]');
    categoryButtons.forEach(button => {
      button.addEventListener('click', function() {
        const category = this.getAttribute('data-category');
        if (category) {
          window.location.href = `/direktori-hosting/${category}/`;
        }
      });
    });

    // Initialize
    toggleClearButton();
  });
</script>
