---
interface Provider {
  id: string;
  collection: string;
  data: {
    name: string;
    displayName?: string;
    slug: string;
    logo: string;
    description: string;
    pricing: {
      startingPrice: number;
      promoCode?: string;
      promoDescription?: string;
    };
    affiliateLink?: string;
    website: string;
    badges?: Array<{
      type: string;
      label: string;
    }>;
  };
}

export interface Props {
  providers: Provider[];
  currentSlug?: string;
}

const { providers, currentSlug } = Astro.props;

// Filter sponsored providers and exclude current provider
const sponsoredProviders = providers
  .filter(p => 
    p.data.badges?.some(badge => badge.type === 'sponsored') &&
    p.data.slug !== currentSlug
  )
  .slice(0, 3); // Take only 3 providers

// Don't render if no sponsored providers
if (sponsoredProviders.length === 0) return null;

// Don't render if current provider is also sponsored (sponsored shouldn't see other sponsored)
const currentProvider = providers.find(p => p.data.slug === currentSlug);
const isCurrentSponsored = currentProvider?.data.badges?.some(badge => badge.type === 'sponsored');
if (isCurrentSponsored) return null;
---

<!-- Sponsored Providers Section -->
<div class="mb-8">
  <div class="flex items-center gap-2 mb-4">
    <h3 class="text-sm font-medium text-muted uppercase tracking-wide">Sponsored</h3>
    <div class="h-px bg-gray-200 flex-1"></div>
  </div>
  
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    {sponsoredProviders.map((provider) => (
      <div class="group bg-bg-section border border-gray-200 rounded-lg p-2 hover:shadow-md hover:border-primary/20 transition-all duration-200">
        <div class="flex items-center justify-between space-x-3">
          <!-- Logo -->
          <img
            src={provider.data.logo}
            alt={`${provider.data.name} logo`}
            class="h-12 w-14 rounded-md object-contain border border-gray-200 bg-white p-1 flex-shrink-0"
            loading="lazy"
          />
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <h4 class="font-semibold text-heading text-sm group-hover:text-primary transition-colors truncate">
                {provider.data.displayName || provider.data.name}
              </h4>
              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200 flex-shrink-0">
                👑
              </span>
            </div>
            
            <!-- Price -->
            <div class="flex items-center gap-1">
              <span class="text-sm font-medium text-primary">
                Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')}
              </span>
              <span class="text-xs text-muted">/bulan</span>
            </div>
          </div>
          
          <!-- View Button -->
          <div class="flex-shrink-0">
            <a
              href={provider.data.affiliateLink || provider.data.website}
              target="_blank"
              rel="noopener"
              class="flex items-center justify-center gap-1 bg-gray-100 hover:bg-gray-200 border border-gray-200 rounded-lg px-2 py-1.5 text-xs font-medium text-heading transition-colors hover:no-underline group-hover:border-primary/30"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              View
            </a>
          </div>
        </div>
      </div>
    ))}
  </div>
</div>

<style>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
