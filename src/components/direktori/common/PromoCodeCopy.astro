---
// Promo Code Copy Component
---

<script>
  // Simple, safe implementation
  function initPromoCodeCopy() {
    const copyButtons = document.querySelectorAll('[data-promo-code]');

    copyButtons.forEach(button => {
      // Prevent multiple initializations
      if (button.hasAttribute('data-initialized')) return;
      button.setAttribute('data-initialized', 'true');
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        const code = button.getAttribute('data-promo-code');
        if (!code) return;

        try {
          // Try modern clipboard API
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(code);
          } else {
            // Fallback
            const textArea = document.createElement('textarea');
            textArea.value = code;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
          }

          // Show success feedback
          const originalHTML = button.innerHTML;
          const originalClasses = button.className;

          button.innerHTML = `
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          `;
          button.className = originalClasses + ' text-green-600';

          setTimeout(() => {
            button.innerHTML = originalHTML;
            button.className = originalClasses;
          }, 2000);

        } catch (err) {
          console.error('Copy failed:', err);
        }
      });
    });
  }

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initPromoCodeCopy);

  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initPromoCodeCopy);

  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initPromoCodeCopy);

  // Additional fallback for extra reliability
  window.addEventListener('load', initPromoCodeCopy);

  // Initialize immediately if DOM is already ready
  if (document.readyState === 'loading') {
    // DOM is still loading, events will handle it
  } else {
    // DOM is already ready, initialize now
    initPromoCodeCopy();
  }
</script>
