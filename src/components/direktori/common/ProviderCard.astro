---
export interface Props {
  provider: {
    slug: string;
    name: string;
    displayName?: string;
    logo: string;
    website: string;
    description: string;
    badges: Array<{
      type: 'recommended' | 'sponsored' | 'promoted' | 'verified';
      label: string;
    }>;
    features: Array<{
      name: string;
      icon?: string;
      description?: string;
    }>;
    datacenters: Array<{
      location: string;
      country: string;
      flag?: string;
    }>;
    pricing: {
      startingPrice: number;
      currency: string;
      promoCode?: string;
      promoDescription?: string;
    };
    tier: 'basic' | 'verified' | 'sponsor' | 'recommended';
    affiliateLink?: string;
  };
  variant?: 'default' | 'featured' | 'compact';
  showFeatures?: boolean;
  showDatacenters?: boolean;
}

const { 
  provider, 
  variant = 'default',
  showFeatures = true,
  showDatacenters = true
} = Astro.props;

const displayName = provider.displayName || provider.name;
const isSponsored = provider.badges.some(badge => badge.type === 'sponsored');
---

<div class={`direktori-provider-card rounded-xl border bg-white shadow-md ${
  isSponsored ? 'sponsored-card' :
  'border-gray-200'
} ${
  variant === 'featured' ? 'p-8 direktori-featured-glow' :
  variant === 'compact' ? 'p-4' :
  'p-6'
}`}>
  
  <!-- Sponsored Glow Effect -->
  {isSponsored && (
    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-red-500/10 to-orange-500/10 pointer-events-none"></div>
  )}

  <!-- Provider Header -->
  <div class="flex items-start justify-between mb-4 relative">
    <div class="flex items-center space-x-4">
      <div class="relative">
        <img
          src={provider.logo}
          alt={`${provider.name} logo`}
          class={`rounded-lg object-contain border border-gray-200 ${
            variant === 'featured' ? 'h-16 w-16 p-2' :
            variant === 'compact' ? 'h-10 w-10 p-1' :
            'h-12 w-12 p-2'
          }`}
          loading="lazy"
        />
        
        <!-- Tier indicator -->
        {provider.tier === 'sponsor' && (
          <div class="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-gradient-to-r from-red-500 to-orange-500 animate-pulse"></div>
        )}
      </div>
      
      <div class="flex-1">
        <h3 class={`font-semibold text-heading ${
          variant === 'featured' ? 'text-xl' :
          variant === 'compact' ? 'text-base' :
          'text-lg'
        }`}>
          {displayName}
        </h3>
        
        <!-- Badges -->
        <div class="flex items-center space-x-2 mt-1">
          {provider.badges.map((badge) => (
            <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              badge.type === 'sponsored' ? 'bg-purple-100 text-purple-700 border border-purple-200' :
              badge.type === 'recommended' ? 'bg-green-100 text-green-700 border border-green-200' :
              badge.type === 'verified' ? 'bg-blue-100 text-blue-700 border border-blue-200' :
              'bg-gray-100 text-gray-800'
            }`}>
              {badge.type === 'sponsored' ? '👑' : 
               badge.type === 'recommended' ? '⭐' : 
               badge.type === 'verified' ? '✓' : ''} {badge.label}
            </span>
          ))}
        </div>
      </div>
    </div>
    
    <!-- Pricing -->
    <div class="text-right">
      <div class={`font-bold text-primary ${
        variant === 'featured' ? 'text-xl' :
        variant === 'compact' ? 'text-base' :
        'text-lg'
      }`}>
        Rp {provider.pricing.startingPrice.toLocaleString('id-ID')}
      </div>
      <div class="text-sm text-muted">/bulan</div>
      
      {provider.pricing.promoCode && (
        <div class="mt-1">
          <span class="inline-flex items-center px-2 py-1 rounded-md bg-red-100 text-red-800 text-xs font-medium">
            {provider.pricing.promoDescription || provider.pricing.promoCode}
          </span>
        </div>
      )}
    </div>
  </div>

  <!-- Description -->
  <p class={`text-muted mb-4 ${
    variant === 'compact' ? 'text-sm line-clamp-1' : 'text-sm line-clamp-2'
  }`}>
    {provider.description}
  </p>

  <!-- Features -->
  {showFeatures && provider.features.length > 0 && (
    <div class="mb-4">
      <div class={`grid gap-2 ${
        variant === 'featured' ? 'grid-cols-2' :
        variant === 'compact' ? 'grid-cols-1' :
        'grid-cols-2'
      }`}>
        {provider.features.slice(0, variant === 'compact' ? 2 : 4).map((feature) => (
          <div class="flex items-center space-x-2 text-sm">
            <svg class="h-4 w-4 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-muted truncate">{feature.name}</span>
          </div>
        ))}
      </div>
    </div>
  )}

  <!-- Data Centers -->
  {showDatacenters && provider.datacenters.length > 0 && (
    <div class="mb-4">
      <div class="flex items-center space-x-2">
        <span class="text-sm font-medium text-heading">Data Center:</span>
        <div class="flex space-x-1">
          {provider.datacenters.slice(0, 4).map((dc) => (
            <span class="text-sm" title={`${dc.location}, ${dc.country}`}>
              {dc.flag || '🌐'}
            </span>
          ))}
          {provider.datacenters.length > 4 && (
            <span class="text-xs text-muted">+{provider.datacenters.length - 4}</span>
          )}
        </div>
      </div>
    </div>
  )}

  <!-- Actions -->
  <div class={`flex space-x-3 ${variant === 'compact' ? 'flex-col space-x-0 space-y-2' : ''}`}>
    <a
      href={`/direktori/hosting/${provider.slug}/`}
      class={`btn-primary rounded-lg text-center font-medium transition-colors ${
        variant === 'compact' ? 'px-3 py-2 text-sm' : 'flex-1 px-4 py-2 text-sm'
      }`}
    >
      Lihat Detail
    </a>
    <a
      href={provider.affiliateLink || provider.website}
      target="_blank"
      rel="noopener"
      class={`rounded-lg border border-gray-300 text-center font-medium text-gray-700 hover:bg-gray-50 transition-colors ${
        variant === 'compact' ? 'px-3 py-2 text-sm' : 'flex-1 px-4 py-2 text-sm'
      }`}
      onclick={`
        // Track provider click
        if (typeof umami !== 'undefined') {
          umami.track('provider-click', { 
            provider: '${provider.slug}', 
            source: 'card',
            tier: '${provider.tier}'
          });
        }
      `}
    >
      Kunjungi Website
    </a>
  </div>
</div>

<style>
  /* Sponsored card glow animation */
  .ring-red-100 {
    animation: sponsored-glow 3s ease-in-out infinite alternate;
  }
  
  @keyframes sponsored-glow {
    0% {
      box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
    }
    100% {
      box-shadow: 0 0 20px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4);
    }
  }
</style>
