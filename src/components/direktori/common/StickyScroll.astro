---
// Sticky Scroll Component for Sidebar
---

<script>
  // Simple, safe implementation
  function initStickyScroll() {
    const sidebar = document.getElementById('sidebar-pricing');
    if (!sidebar) return;

    let ticking = false;
    const scrollThreshold = 100;

    function updateSidebar() {
      if (!sidebar) return;

      const scrollY = window.scrollY;

      if (scrollY > scrollThreshold) {
        // Add padding when scrolling - exactly 1rem
        sidebar.style.paddingTop = '2rem';
        sidebar.style.top = '2rem';
      } else {
        // Remove padding when at top
        sidebar.style.paddingTop = '0';
        sidebar.style.top = '2rem'; // original top-8 = 2rem
      }

      ticking = false;
    }

    function onScroll() {
      if (!ticking) {
        requestAnimationFrame(updateSidebar);
        ticking = true;
      }
    }

    window.addEventListener('scroll', onScroll, { passive: true });
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initStickyScroll);
  } else {
    initStickyScroll();
  }
</script>
