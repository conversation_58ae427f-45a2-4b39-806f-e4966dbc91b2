---
// Image Modal Component
---

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden items-center justify-center p-4">
  <div class="relative max-w-4xl max-h-full">
    <button 
      id="closeModalBtn"
      class="absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2 transition-colors"
      title="Tutup modal"
    >
      <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
    <img 
      id="modalImage" 
      src="" 
      alt="" 
      class="max-w-full max-h-full object-contain rounded-lg"
    />
  </div>
</div>

<script>
  // Simple, safe implementation
  function initImageModal() {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage') as HTMLImageElement;
    const closeBtn = document.getElementById('closeModalBtn');

    if (!modal || !modalImage || !closeBtn) return;

    // Prevent multiple initializations
    if (modal.hasAttribute('data-initialized')) return;
    modal.setAttribute('data-initialized', 'true');

    // Close button
    closeBtn.addEventListener('click', () => {
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      document.body.style.overflow = 'auto';
    });

    // Click outside to close
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.style.overflow = 'auto';
      }
    });

    // ESC key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.style.overflow = 'auto';
      }
    });

    // Setup image triggers
    const triggers = document.querySelectorAll('.image-modal-trigger');
    triggers.forEach(trigger => {
      trigger.addEventListener('click', (e) => {
        e.preventDefault();
        const imageSrc = trigger.getAttribute('data-image');
        const imageAlt = trigger.getAttribute('data-alt');

        if (imageSrc && imageAlt && modalImage) {
          modalImage.src = imageSrc;
          modalImage.alt = imageAlt;
          modal.classList.remove('hidden');
          modal.classList.add('flex');
          document.body.style.overflow = 'hidden';
        }
      });
    });
  }

  // Astro lifecycle event for view transitions
  document.addEventListener('astro:page-load', initImageModal);

  // Fallback for direct navigation and non-Astro environments
  document.addEventListener('DOMContentLoaded', initImageModal);

  // Re-initialize if using view transitions (backup)
  document.addEventListener('astro:after-swap', initImageModal);

  // Additional fallback for extra reliability
  window.addEventListener('load', initImageModal);

  // Initialize immediately if DOM is already ready
  if (document.readyState === 'loading') {
    // DOM is still loading, events will handle it
  } else {
    // DOM is already ready, initialize now
    initImageModal();
  }
</script>
