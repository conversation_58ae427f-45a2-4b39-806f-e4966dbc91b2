---
import type { LocationData } from '~/utils/locationUtils';

export interface Props {
  location: LocationData;
}

const { location } = Astro.props;
---

<a
  href={`/direktori/lokasi/${location.slug}/`}
  class="group block rounded-md border border-gray-200 bg-white p-6 shadow-md hover:no-underline transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
>
  <div class="mb-4">
    <div class="flex items-center gap-3 mb-2">
      <span class="text-2xl">{location.flag}</span>
      <h3 class="font-semibold text-heading group-hover:text-primary transition-colors text-lg">
        {location.name}
      </h3>
    </div>
    <p class="text-sm text-muted">
      {location.providerCount} provider tersedia
    </p>
  </div>

  <div class="text-sm text-muted">
    <p class="mb-2">Provider yang tersedia:</p>
    <div class="flex flex-wrap gap-1">
      {location.providers.slice(0, 3).map((provider) => (
        <span class="inline-flex items-center px-2 py-1 rounded-md bg-gray-100 text-xs font-medium text-gray-700">
          {provider.data.displayName || provider.data.name}
        </span>
      ))}
      {location.providers.length > 3 && (
        <span class="inline-flex items-center px-2 py-1 rounded-md bg-gray-100 text-xs font-medium text-gray-700">
          +{location.providers.length - 3} lainnya
        </span>
      )}
    </div>
  </div>
</a>
