---

// TODO: This code is temporary
---

<script is:inline>
  function applyTheme(theme) {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    const matches = document.querySelectorAll('[data-aw-toggle-color-scheme] > input');

    if (matches && matches.length) {
      matches.forEach((elem) => {
        elem.checked = theme !== 'dark';
      });
    }
  }

  // Always use light mode by default, never auto-activate dark mode
  applyTheme('light');
</script>
