---
import { Icon } from 'astro-icon/components';

export interface Props {
  class?: string;
}

const { 
  class: className = ''
} = Astro.props;
---

<button
  class:list={[
    'search-trigger p-2 rounded-lg hover:bg-bg-muted transition-colors',
    className
  ]}
  data-search-trigger
  aria-label="Cari"
  type="button"
>
  <Icon name="tabler:search" class="w-6 h-6 md:w-5 md:h-5 text-muted" />
</button>

<script>
  // Search trigger functionality - Astro best practices for View Transitions
  function initSearchTriggers() {
    const triggers = document.querySelectorAll('[data-search-trigger]');
    
    triggers.forEach(trigger => {
      // Remove existing listeners to prevent duplicates
      trigger.removeEventListener('click', handleSearchTriggerClick);
      // Add fresh listener
      trigger.addEventListener('click', handleSearchTriggerClick);
    });
  }

  function handleSearchTriggerClick() {
    document.dispatchEvent(new CustomEvent('search:open'));
  }

  function handleGlobalKeydown(e: KeyboardEvent) {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      document.dispatchEvent(new CustomEvent('search:open'));
    }
    
    if (e.key === '/' && !isInputFocused()) {
      e.preventDefault();
      document.dispatchEvent(new CustomEvent('search:open'));
    }
  }

  function isInputFocused() {
    const activeElement = document.activeElement;
    return activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      (activeElement as HTMLElement).contentEditable === 'true'
    );
  }

  // Initialize triggers on every page load
  function setupSearch() {
    initSearchTriggers();
    
    // Add keyboard shortcuts (these are global and don't need re-initialization)
    document.removeEventListener('keydown', handleGlobalKeydown); // Remove first to prevent duplicates
    document.addEventListener('keydown', handleGlobalKeydown);
  }

  // Astro View Transitions lifecycle events - best practice
  document.addEventListener('astro:page-load', setupSearch);
  document.addEventListener('DOMContentLoaded', setupSearch);
</script>

<style>
  .search-trigger kbd {
    font-family: ui-monospace, SFMono-Regular, monospace;
    font-size: 10px;
    line-height: 1;
  }
</style>
