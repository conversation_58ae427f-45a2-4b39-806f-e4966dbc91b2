---
import { Icon } from 'astro-icon/components';
import SearchResults from './SearchResults.astro';

export interface Props {
  class?: string;
}

const { class: className = '' } = Astro.props;
---

<div
  id="search-overlay"
  class:list={[
    'search-overlay fixed inset-0 z-50 hidden',
    className
  ]}
  data-search-overlay
  transition:name="global-search-overlay"
  transition:persist
>
  <!-- Backdrop -->
  <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" data-search-backdrop></div>
  
  <!-- Search Modal -->
  <div class="relative z-10 flex min-h-full items-start justify-center p-4 pt-20">
    <div class="search-modal w-full max-w-2xl bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
      
      <!-- Search Input Header -->
      <div class="flex items-center px-4 py-3 border-b border-gray-200 dark:border-gray-600">
        <Icon name="tabler:search" class="w-5 h-5 text-muted mr-3" />
        <input
          type="text"
          placeholder="Cari provider hosting, kategori, atau artikel..."
          class="flex-1 bg-transparent border-0 outline-0 text-heading placeholder:text-muted text-base"
          data-search-input
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
        />
        <button 
          class="ml-3 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          data-search-close
          aria-label="Close search"
        >
          <Icon name="tabler:x" class="w-4 h-4 text-muted" />
        </button>
      </div>

      <!-- Search Results -->
      <div class="max-h-96 overflow-y-auto" data-search-results-container>
        <SearchResults />
      </div>

      <!-- Search Footer -->
      <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between text-xs text-muted">
          <div class="flex items-center space-x-4">
            <span class="flex items-center">
              <kbd class="mr-1">↑↓</kbd> untuk navigasi
            </span>
            <span class="flex items-center">
              <kbd class="mr-1">↵</kbd> untuk pilih
            </span>
            <span class="flex items-center">
              <kbd class="mr-1">esc</kbd> untuk tutup
            </span>
          </div>
          <div class="text-muted">
            Powered by PenasihatHosting.com
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  import { setupSearchLifecycle } from '~/utils/searchController';

  // Initialize search functionality with Astro View Transitions lifecycle management
  // This is the Astro best practice for components that need to work with View Transitions
  setupSearchLifecycle();
</script>

<style>
  /* Search overlay animation styles */
  .search-overlay {
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .search-overlay.hidden {
    pointer-events: none;
  }

  .search-overlay.open {
    opacity: 1;
  }

  /* Modal animation */
  .search-modal {
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1),
                opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0.95);
    opacity: 0;
  }

  .search-overlay.open .search-modal {
    transform: scale(1);
    opacity: 1;
  }

  /* Respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    .search-overlay,
    .search-modal {
      transition: none;
    }
  }

  .search-overlay kbd {
    display: inline-block;
    padding: 2px 4px;
    font-size: 10px;
    line-height: 1;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    font-family: ui-monospace, SFMono-Regular, monospace;
  }

  .search-result-item {
    cursor: pointer;
  }

  .search-result-item:hover,
  .search-result-item.bg-primary\/10 {
    background-color: rgba(var(--aw-color-primary-rgb), 0.1);
  }
</style>
