---
---

<script is:inline>
  // Cek apakah script sudah dijalankan sebelumnya
  if (!window.basic_script) {
    window.basic_script = true;

  function applyTheme(theme) {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  // Always use light mode by default, never auto-activate dark mode
  const initTheme = function () {
    applyTheme('light');
  };
  initTheme();

  function attachEvent(selector, event, fn) {
    const matches = typeof selector === 'string' ? document.querySelectorAll(selector) : selector;
    if (matches && matches.length) {
      matches.forEach((elem) => {
        elem.addEventListener(event, (e) => fn(e, elem), false);
      });
    }
  }

  const onLoad = function () {
    let lastKnownScrollPosition = window.scrollY;
    let ticking = true;

    attachEvent('#header nav', 'click', function () {
      document.querySelector('[data-aw-toggle-menu]')?.classList.remove('expanded');
      document.body.classList.remove('overflow-hidden');
      document.getElementById('header')?.classList.remove('h-screen');
      document.getElementById('header')?.classList.remove('expanded');
      document.getElementById('header')?.classList.remove('bg-page');
      document.querySelector('#header nav')?.classList.add('hidden');
      document.querySelector('#header > div > div:last-child')?.classList.add('hidden');
    });

    attachEvent('[data-aw-toggle-menu]', 'click', function (_, elem) {
      elem.classList.toggle('expanded');
      document.body.classList.toggle('overflow-hidden');
      document.getElementById('header')?.classList.toggle('h-screen');
      document.getElementById('header')?.classList.toggle('expanded');
      document.getElementById('header')?.classList.toggle('bg-page');
      document.querySelector('#header nav')?.classList.toggle('hidden');
      document.querySelector('#header > div > div:last-child')?.classList.toggle('hidden');
    });



    attachEvent('[data-aw-social-share]', 'click', function (_, elem) {
      const network = elem.getAttribute('data-aw-social-share');
      const url = encodeURIComponent(elem.getAttribute('data-aw-url'));
      const text = encodeURIComponent(elem.getAttribute('data-aw-text'));

      let href;
      switch (network) {
        case 'facebook':
          href = `https://www.facebook.com/sharer.php?u=${url}`;
          break;
        case 'twitter':
          href = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
          break;
        case 'linkedin':
          href = `https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=${text}`;
          break;
        case 'whatsapp':
          href = `https://wa.me/?text=${text}%20${url}`;
          break;
        case 'mail':
          href = `mailto:?subject=%22${text}%22&body=${text}%20${url}`;
          break;

        default:
          return;
      }

      const newlink = document.createElement('a');
      newlink.target = '_blank';
      newlink.href = href;
      newlink.click();
    });

    const screenSize = window.matchMedia('(max-width: 767px)');
    screenSize.addEventListener('change', function () {
      document.querySelector('[data-aw-toggle-menu]')?.classList.remove('expanded');
      document.body.classList.remove('overflow-hidden');
      document.getElementById('header')?.classList.remove('h-screen');
      document.getElementById('header')?.classList.remove('expanded');
      document.getElementById('header')?.classList.remove('bg-page');
      document.querySelector('#header nav')?.classList.add('hidden');
      document.querySelector('#header > div > div:last-child')?.classList.add('hidden');
    });

    function applyHeaderStylesOnScroll() {
      const header = document.querySelector('#header[data-aw-sticky-header]');
      if (!header) return;
      if (lastKnownScrollPosition > 60 && !header.classList.contains('scroll')) {
        header.classList.add('scroll');
      } else if (lastKnownScrollPosition <= 60 && header.classList.contains('scroll')) {
        header.classList.remove('scroll');
      }
      ticking = false;
    }
    applyHeaderStylesOnScroll();

    attachEvent([document], 'scroll', function () {
      lastKnownScrollPosition = window.scrollY;

      if (!ticking) {
        window.requestAnimationFrame(() => {
          applyHeaderStylesOnScroll();
        });
        ticking = true;
      }
    });

    // Initialize Umami tracking
    const initUmamiTracking = () => {
      // Remove any existing click listeners to prevent duplicates
      const oldClickHandler = window._umamiClickHandler;
      if (oldClickHandler) {
        document.removeEventListener('click', oldClickHandler);
      }
      
      // Create new click handler
      const clickHandler = function(event) {
        // Find the closest ancestor <a> element
        const targetLink = event.target.closest('a');
  
        // Check if it's a link and its href contains '/go/' (affiliate links only)
        // Skip if element already has data-umami-event to prevent duplicate tracking
        const isAffiliateLink = targetLink && targetLink.href && targetLink.href.includes('/go/');
        
        if (targetLink && targetLink.href && isAffiliateLink &&
            !targetLink.hasAttribute('data-umami-event')) {
          // Special case: sponsor card inside shared hosting table
          const isSharedTableSponsor = !!targetLink.closest('#shared-hosting-table aside[data-sponsor="samudra-host"]');
          if (isSharedTableSponsor) {
            if (window.umami && typeof window.umami.track === 'function') {
              const linkUrl = targetLink.href;
              const pageUrl = window.location.pathname;
              // Fire a distinct event separate from generic affiliate tracking
              window.umami.track('sponsor-inline-samudrahost-shared', {
                url: linkUrl,
                placement: 'shared-table',
                sponsor: 'samudra-host',
                page: pageUrl
              });
              targetLink.setAttribute('data-umami-event', 'tracked');
            }
            return; // Prevent default affiliate tracking block below (avoid duplicate event)
          }
          
          // Check if umami object is available
          if (window.umami && typeof window.umami.track === 'function') {
            const linkUrl = targetLink.href;
            const linkText = targetLink.textContent ? targetLink.textContent.trim() : 'No Text';
            const pageUrl = window.location.pathname;
  
            // Determine event type and extract relevant info
            let eventNameSuffix = '';
            const eventType = 'aff';
            
            // Extract the part of the URL after /go/ for the event name
            const goIndex = linkUrl.indexOf('/go/');
            if (goIndex !== -1) {
              eventNameSuffix = linkUrl.substring(goIndex + 4); // +4 to skip '/go/'
              // Optional: Sanitize eventNameSuffix if needed (remove slashes, query params, etc.)
              eventNameSuffix = eventNameSuffix.split(/[?#]/)[0]; // Remove query params and hash
              if (eventNameSuffix.endsWith('/')) eventNameSuffix = eventNameSuffix.slice(0, -1); // Remove trailing slash
            }
  
            // Use a shorter event name with specific page context to avoid Umami length limits
            let pageContext = 'other';
            
            // Check if click is from sponsor banner
            const isSponsorBanner = targetLink.hasAttribute('data-sponsor-banner');
            if (isSponsorBanner) {
              const sponsorType = targetLink.getAttribute('data-sponsor-banner');
              pageContext = `sponsor-${sponsorType}`;
            } else {
              // Check if click is from HostingComparisonTable and determine hosting type
              const isFromComparisonTable = targetLink.closest('table') ||
                                          targetLink.closest('[id*="hosting-table"]') ||
                                          targetLink.closest('.hosting-comparison-table');
              
              // Determine hosting type from table context
              let hostingType = '';
              if (isFromComparisonTable) {
                const tableElement = targetLink.closest('table') || 
                                   targetLink.closest('[id*="hosting-table"]') || 
                                   targetLink.closest('.hosting-comparison-table');
                
                if (tableElement) {
                  const tableId = tableElement.id || '';
                  const tableClass = tableElement.className || '';
                  const tableContent = tableElement.textContent.toLowerCase();
                  
                  // Check for specific table types first
                  if (tableId.includes('hosting-murah') || tableClass.includes('hosting-murah') || 
                      tableContent.includes('hosting murah') || tableContent.includes('web hosting murah')) {
                    hostingType = 'hm'; // hosting murah
                  } else if (tableId.includes('cloudpanel') || tableClass.includes('cloudpanel') || 
                            tableContent.includes('cloudpanel') || tableContent.includes('cloud panel')) {
                    hostingType = 'cloudpanel';
                  }
                  // Check for VPS indicators
                  else if (tableId.includes('vps') || tableClass.includes('vps') || 
                          tableContent.includes('vps') || tableContent.includes('virtual private server')) {
                    hostingType = 'vps';
                  }
                  // Check for shared hosting indicators (default if not VPS)
                  else {
                    hostingType = 'shared';
                  }
                }
              }
              
              if (pageUrl === '/' || pageUrl === '/index.html' || pageUrl === '') {
                // Home page context
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  pageContext = 'home';
                }
              } else if (pageUrl.includes('/hosting-murah')) {
                // Hosting murah page context
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  pageContext = 'hosting-murah';
                }
              } else if (pageUrl.includes('/direktori/hosting/')) {
                pageContext = 'detail';
              } else if (pageUrl.includes('/direktori/lokasi/')) {
                // Extract location from URL like /direktori/lokasi/singapore/
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  const locationMatch = pageUrl.match(/\/direktori\/lokasi\/([^/]+)\/?$/);
                  pageContext = locationMatch ? locationMatch[1] : 'location';
                }
              } else if (pageUrl.includes('/direktori/') && pageUrl.split('/').length === 4) {
                // Extract category from URL like /direktori/wordpress-hosting/
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  const categoryMatch = pageUrl.match(/\/direktori\/([^/]+)\/?$/);
                  pageContext = categoryMatch ? categoryMatch[1] : 'category';
                }
              } else if (pageUrl.includes('/direktori-hosting')) {
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  pageContext = 'directory';
                }
              } else if (pageUrl.includes('/promo-hosting')) {
                if (isFromComparisonTable && hostingType) {
                  pageContext = `table-${hostingType}`;
                } else {
                  pageContext = 'promo';
                }
              }
            }
            
            const eventName = eventNameSuffix ? `${eventType}-${eventNameSuffix}-${pageContext}` : `${eventType}-click-${pageContext}`;
  
            // Track the affiliate link event
            window.umami.track(eventName, {
              url: linkUrl,
              text: linkText,
              page: pageUrl
            });
            
            // Mark as tracked to prevent duplicate tracking
            targetLink.setAttribute('data-umami-event', 'tracked');
          }
        }
      };
      
      // Store the handler for future cleanup
      window._umamiClickHandler = clickHandler;
      
      // Add the event listener
      document.addEventListener('click', clickHandler);
    };
    
    // Initialize immediately if possible
    initUmamiTracking();
    
    // Also initialize on various events for reliability
    document.addEventListener('DOMContentLoaded', () => {
      initUmamiTracking();
    });
    
    // Re-initialize after Astro navigation
    document.addEventListener('astro:after-swap', () => {
      setTimeout(initUmamiTracking, 100); // Small delay to ensure DOM is ready
    });
    
    // Also initialize on window load for extra reliability
    window.addEventListener('load', () => {
      initUmamiTracking();
    });
  };
  
  const onPageShow = function () {
    document.documentElement.classList.add('motion-safe:scroll-smooth');
    const elem = document.querySelector('[data-aw-toggle-menu]');
    if (elem) {
      elem.classList.remove('expanded');
    }
    document.body.classList.remove('overflow-hidden');
    document.getElementById('header')?.classList.remove('h-screen');
    document.getElementById('header')?.classList.remove('expanded');
    document.querySelector('#header nav')?.classList.add('hidden');
  };

  window.onload = onLoad;
  window.onpageshow = onPageShow;

  document.addEventListener('astro:after-swap', () => {
    initTheme();
    onLoad();
    onPageShow();
  });
  
  // Additional fallback: Check for Umami periodically and reinitialize if needed
  const umamiCheckInterval = setInterval(() => {
    if (window.umami && typeof window.umami.track === 'function') {
      if (typeof initUmamiTracking === 'function') {
        initUmamiTracking();
      }
      clearInterval(umamiCheckInterval);
    }
  }, 1000);
  
  // Clear interval after 10 seconds to avoid infinite checking
  setTimeout(() => {
    clearInterval(umamiCheckInterval);
  }, 10000);
  
  } // Tutup if (!window.basic_script)
</script>

<script is:inline>
  /* Inspired by: https://github.com/heidkaemper/tailwindcss-intersect */
  const Observer = {
    observer: null,
    delayBetweenAnimations: 100,
    animationCounter: 0,

    start() {
      const selectors = [
        '[class*=" intersect:"]',
        '[class*=":intersect:"]',
        '[class^="intersect:"]',
        '[class="intersect"]',
        '[class*=" intersect "]',
        '[class^="intersect "]',
        '[class$=" intersect"]',
      ];

      const elements = Array.from(document.querySelectorAll(selectors.join(',')));

      const getThreshold = (element) => {
        if (element.classList.contains('intersect-full')) return 0.99;
        if (element.classList.contains('intersect-half')) return 0.5;
        if (element.classList.contains('intersect-quarter')) return 0.25;
        return 0;
      };

      elements.forEach((el) => {
        el.setAttribute('no-intersect', '');
        el._intersectionThreshold = getThreshold(el);
      });

      const callback = (entries) => {
        entries.forEach((entry) => {
          requestAnimationFrame(() => {
            const target = entry.target;
            const intersectionRatio = entry.intersectionRatio;
            const threshold = target._intersectionThreshold;

            if (target.classList.contains('intersect-no-queue')) {
              if (entry.isIntersecting) {
                target.removeAttribute('no-intersect');
                if (target.classList.contains('intersect-once')) {
                  this.observer.unobserve(target);
                }
              } else {
                target.setAttribute('no-intersect', '');
              }
              return;
            }

            if (intersectionRatio >= threshold) {
              if (!target.hasAttribute('data-animated')) {
                target.removeAttribute('no-intersect');
                target.setAttribute('data-animated', 'true');

                const delay = this.animationCounter * this.delayBetweenAnimations;
                this.animationCounter++;

                target.style.transitionDelay = `${delay}ms`;
                target.style.animationDelay = `${delay}ms`;

                if (target.classList.contains('intersect-once')) {
                  this.observer.unobserve(target);
                }
              }
            } else {
              target.setAttribute('no-intersect', '');
              target.removeAttribute('data-animated');
              target.style.transitionDelay = '';
              target.style.animationDelay = '';

              this.animationCounter = 0;
            }
          });
        });
      };

      this.observer = new IntersectionObserver(callback.bind(this), { threshold: [0, 0.25, 0.5, 0.99] });

      elements.forEach((el) => {
        this.observer.observe(el);
      });
    },
  };

  Observer.start();

  document.addEventListener('astro:after-swap', () => {
    Observer.start();
  });
</script>
