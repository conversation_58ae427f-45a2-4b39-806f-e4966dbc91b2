---
export interface Props {
  label?: string;
  class?: string;
}

const {
  label = 'Toggle Menu',
  class: className = 'flex flex-col h-12 w-12 rounded justify-center items-center cursor-pointer group z-50',
} = Astro.props;
---

<button type="button" class={className} aria-label={label} data-aw-toggle-menu>
  <span class="sr-only">{label}</span>
  <slot>
    <span
      aria-hidden="true"
      class="h-0.5 w-6 my-1 rounded-full bg-black transition ease transform duration-200 opacity-80 group-[.expanded]:rotate-45 group-[.expanded]:translate-y-2.5"
    ></span>
    <span
      aria-hidden="true"
      class="h-0.5 w-6 my-1 rounded-full bg-black transition ease transform duration-200 opacity-80 group-[.expanded]:opacity-0"
    ></span>
    <span
      aria-hidden="true"
      class="h-0.5 w-6 my-1 rounded-full bg-black transition ease transform duration-200 opacity-80 group-[.expanded]:-rotate-45 group-[.expanded]:-translate-y-2.5"
    ></span>
  </slot>
</button>
