---
// Back to Top Component
---

<button
  id="back-to-top"
  class="fixed bottom-6 right-6 z-50 hidden bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-300 hover:scale-110"
  aria-label="Kembali ke atas"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
  </svg>
</button>

<script>
  function initBackToTop() {
    const backToTopButton = document.getElementById('back-to-top');
    
    if (!backToTopButton) return;

    // Show/hide button based on scroll position
    function toggleBackToTop() {
      if (!backToTopButton) return;
      
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('hidden');
      } else {
        backToTopButton.classList.add('hidden');
      }
    }

    // Smooth scroll to top
    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // Remove existing event listeners to avoid duplicates
    window.removeEventListener('scroll', toggleBackToTop);
    backToTopButton.removeEventListener('click', scrollToTop);

    // Add event listeners
    window.addEventListener('scroll', toggleBackToTop);
    backToTopButton.addEventListener('click', scrollToTop);

    // Initial check
    toggleBackToTop();
  }

  // Initialize on different page load events
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBackToTop);
  } else {
    initBackToTop();
  }
  
  // Re-initialize for Astro view transitions
  document.addEventListener('astro:page-load', initBackToTop);
  document.addEventListener('astro:after-swap', initBackToTop);
</script>
