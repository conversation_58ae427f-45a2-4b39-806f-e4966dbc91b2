---
import { Icon } from 'astro-icon/components';
---

<button
  id="back-to-top"
  class="fixed bottom-6 right-6 bg-primary dark:bg-primary-dark text-white p-2 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-xl z-40"
  aria-label="Back to top"
>
  <Icon name="tabler:arrow-up" class="w-5 h-5" />
</button>

<script is:inline>
  const backToTopButton = document.getElementById('back-to-top');

  if (backToTopButton) {
    // Function to toggle button visibility
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) { // Show button after scrolling 300px
        backToTopButton.classList.remove('invisible', 'opacity-0');
        backToTopButton.classList.add('visible', 'opacity-100');
      } else {
        backToTopButton.classList.remove('visible', 'opacity-100');
        backToTopButton.classList.add('invisible', 'opacity-0');
      }
    };

    // Function to smoothly scroll to top
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    };

    // Event listeners
    window.addEventListener('scroll', toggleVisibility);
    backToTopButton.addEventListener('click', scrollToTop);

    // Initial check on page load
    toggleVisibility();
  }
</script> 