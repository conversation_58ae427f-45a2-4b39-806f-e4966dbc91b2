<!-- Script Hybrid untuk penasihathosting.com 
<script>
(function() {
  // Deteksi apakah dari Indonesia (berdasarkan timezone atau lainnya)
  const isIndonesia = Intl.DateTimeFormat().resolvedOptions().timeZone.includes('Jakarta') || 
                     Intl.DateTimeFormat().resolvedOptions().timeZone.includes('Asia') ||
                     navigator.language.includes('id');
  
  if (isIndonesia) {
    // Untuk Indonesia: gunakan direct connection
    const script = document.createElement('script');
    script.defer = true;
    script.src = 'https://analitik.harunstudio.com/script.js';
    script.setAttribute('data-website-id', '1138ddfe-fa4a-4399-a719-d505cd5c70c6');
    document.head.appendChild(script);
  } else {
    // Untuk luar negeri: gunakan proxy
    const script = document.createElement('script');
    script.defer = true;
    script.src = 'https://penasihathosting.com/stats.js';
    script.setAttribute('data-website-id', '1138ddfe-fa4a-4399-a719-d505cd5c70c6');
    script.setAttribute('data-host-url', 'https://penasihathosting.com/umami-api');
    document.head.appendChild(script);
  }
})();
</script>

 -->