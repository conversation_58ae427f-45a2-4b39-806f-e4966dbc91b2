import { useEffect, useState, useCallback } from 'preact/hooks';
import { getCompareItems, onCompareChange, toggleCompare, clearCompare } from '~/utils/compareStore';
import CompareToast from './CompareToast.tsx';

function prettify(slug: string) {
  return slug.split('-').map(s => s[0]?.toUpperCase() + s.slice(1)).join(' ');
}

interface LogoInfo { slug: string; src?: string; }

export default function CompareBar() {
  const [items, setItems] = useState<string[]>([]);
  const [logos, setLogos] = useState<LogoInfo[]>([]);

  const syncLogos = useCallback((slugs: string[]) => {
    const list: LogoInfo[] = slugs.map(slug => {
      const el = document.querySelector(`[data-provider-card="${slug}"] img` ) as HTMLImageElement | null;
      return { slug, src: el?.getAttribute('src') || undefined };
    });
    setLogos(list);
  }, []);

  useEffect(() => {
    const current = getCompareItems();
    setItems(current);
    if (typeof window !== 'undefined') {
      queueMicrotask(() => syncLogos(current));
    }
    const off = onCompareChange(list => {
      setItems(list);
      queueMicrotask(() => syncLogos(list));
    });
    return () => off();
  }, [syncLogos]);

  const remove = useCallback((slug: string) => { toggleCompare(slug); }, []);
  const reset = useCallback(() => { clearCompare(); }, []);

  if (items.length === 0) return null;

  const maxSlots = 4;
  const emptySlots = Array.from({ length: Math.max(0, maxSlots - items.length) });
  const compareUrl = items.length >= 2 ? `/compare/?providers=${items.join(',')}` : undefined;

  return (
    <div className="fixed z-40 left-2 right-2 bottom-20 translate-x-0 sm:left-1/2 sm:right-auto sm:bottom-4 sm:-translate-x-1/2 print:hidden">
      <CompareToast />
      <div className="rounded-md border border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/80 shadow-lg shadow-black/5 ring-1 ring-black/5 px-3 py-2 flex items-center gap-3 overflow-x-auto max-w-full scrollbar-thin">
        <button onClick={reset} className="shrink-0 text-[11px] font-medium text-gray-600 hover:text-gray-900 focus:outline-none" type="button">Remove All</button>
        <div className="flex items-center gap-2 shrink-0">
          {logos.map(l => (
            <div key={l.slug} className="relative group h-12 w-16 flex items-center justify-center border border-gray-300 bg-white rounded-sm shrink-0">
              {l.src ? <img src={l.src} alt={prettify(l.slug)} className="max-h-10 max-w-[60px] object-contain" loading="lazy" /> : <span className="text-[10px] text-gray-400 text-center px-1 leading-tight">{prettify(l.slug)}</span>}
              <button
                type="button"
                onClick={() => remove(l.slug)}
                aria-label={`Remove ${prettify(l.slug)}`}
                className="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 h-4 w-4 rounded-full bg-gray-800 text-white text-[19px] flex items-center justify-center shadow ring-1 ring-white hover:bg-black focus:outline-none focus:ring-2 focus:ring-primary/60"
              >
                ×
              </button>
            </div>
          ))}
          {emptySlots.map((_, i) => (
            <div key={i} className="h-12 w-16 flex items-center justify-center border border-dashed border-gray-300 rounded-sm bg-gray-50 text-[10px] text-gray-400 shrink-0">Empty</div>
          ))}
        </div>
        {compareUrl ? (
          <a href={compareUrl} className="ml-1 inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-xs font-semibold text-white shadow hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 shrink-0">
            Bandingkan
          </a>
        ) : (
          <button type="button" disabled className="ml-1 inline-flex items-center justify-center rounded-md bg-gray-300 px-4 py-2 text-xs font-semibold text-gray-600 cursor-not-allowed shrink-0" title="Select at least 2 providers">Compare Now</button>
        )}
      </div>
    </div>
  );
}
