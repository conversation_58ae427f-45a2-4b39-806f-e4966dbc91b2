import { useEffect, useState } from 'preact/hooks';

interface Toast { id: number; message: string; type?: 'info' | 'warning'; }
let idCounter = 0;

export default function CompareToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  useEffect(() => {
    function push(message: string, type: Toast['type'] = 'info') {
      const id = ++idCounter;
      setToasts(t => [...t, { id, message, type }]);
      setTimeout(() => setToasts(t => t.filter(x => x.id !== id)), 3000);
    }
    const onAdded = (e: Event) => {
      const { slug } = (e as CustomEvent).detail;
      push(`Ditambahkan: ${slug}`);
    };
    const onRemoved = (e: Event) => {
      const { slug } = (e as CustomEvent).detail;
      push(`Dihapus: ${slug}`);
    };
    const onCleared = () => push('Semua pilihan dihapus');
    const onLimit = (e: Event) => {
      const { max } = (e as CustomEvent).detail; push(`Maksimal ${max} provider`, 'warning');
    };
    window.addEventListener('compare:added', onAdded);
    window.addEventListener('compare:removed', onRemoved);
    window.addEventListener('compare:cleared', onCleared);
    window.addEventListener('compare:limit', onLimit);
    return () => {
      window.removeEventListener('compare:added', onAdded);
      window.removeEventListener('compare:removed', onRemoved);
      window.removeEventListener('compare:cleared', onCleared);
      window.removeEventListener('compare:limit', onLimit);
    };
  }, []);

  if (!toasts.length) return null;
  return (
    <div className="fixed z-50 bottom-20 left-1/2 -translate-x-1/2 flex flex-col gap-2 w-full max-w-xs">
      {toasts.map(t => (
        <div key={t.id} className={`rounded-md px-3 py-2 text-xs font-medium shadow border backdrop-blur bg-white/90 flex items-center justify-between ${t.type==='warning' ? 'border-amber-300 text-amber-800' : 'border-gray-200 text-gray-700'}`}> 
          <span>{t.message}</span>
          <button onClick={() => setToasts(ts => ts.filter(x => x.id !== t.id))} className="ml-2 text-gray-500 hover:text-gray-800 focus:outline-none">×</button>
        </div>
      ))}
    </div>
  );
}
