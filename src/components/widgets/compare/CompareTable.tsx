import { useEffect, useState } from 'preact/hooks';
import { onCompareChange, toggleCompare, getCompareItems, setCompareItems } from '~/utils/compareStore';
import CompareToast from './CompareToast';

interface ComparableProvider {
  slug: string;
  name: string;
  displayName?: string;
  logo?: string;
  startingPrice?: number;
  promoDescription?: string;
  promoCode?: string;
  features: string[];
  datacenters: { flag?: string; location: string }[];
  headquarters?: string;
  founded?: string | number;
  support: { key: string; label: string; icon: string }[];
  controlPanels: { key: string; label: string; icon: string }[];
  badges: { type: string; label: string }[];
  affiliateLink?: string;
  website?: string;
}

interface Props {
  all: ComparableProvider[]; // full dataset (static)
}

export default function CompareTable({ all }: Props) {
  const [providers, setProviders] = useState<ComparableProvider[]>([]);
  const [removing, setRemoving] = useState<string | null>(null);

  // Initialize selection & sync with URL + store (client-side only)
  useEffect(() => {
    if (typeof window === 'undefined') return; // safety
    const allMap = new Map(all.map(p => [p.slug, p] as const));

    const params = new URLSearchParams(window.location.search);
    const paramSlugs = (params.get('providers') || '')
      .split(',')
      .map(s => s.trim())
      .filter(Boolean);

    const storeSlugs = getCompareItems();
    let selected = paramSlugs.length ? paramSlugs : storeSlugs;

    // Sanitize & limit
    selected = selected.filter(s => allMap.has(s));
    if (selected.length > 4) selected = selected.slice(0, 4);

    // If none selected, redirect (maintains previous UX)
    if (selected.length < 2) {
      // Fallback: redirect user back to directory
      window.location.href = '/direktori-hosting/';
      return;
    }

    // Ensure URL contains providers param (for shareability) if not provided
    if (!paramSlugs.length) {
      params.set('providers', selected.join(','));
      window.history.replaceState({}, '', window.location.pathname + '?' + params.toString());
    }

    // Sync store & local state
    setCompareItems(selected);
    setProviders(selected.map(s => allMap.get(s)!).filter(Boolean));

    const off = onCompareChange(items => {
      const filtered = items.filter(s => allMap.has(s));
      // Redirect if user removed down to <2
      if (filtered.length < 2) {
        window.location.href = '/direktori-hosting/';
        return;
      }
      setProviders(filtered.map(s => allMap.get(s)!) as ComparableProvider[]);
      const p = new URLSearchParams(window.location.search);
      p.set('providers', filtered.join(','));
      window.history.replaceState({}, '', window.location.pathname + '?' + p.toString());
    });

    return () => off();
  }, [all]);

  // Reset removing flag once provider is gone
  useEffect(() => {
    if (removing && !providers.find(p => p.slug === removing)) setRemoving(null);
  }, [providers, removing]);

  // Dynamic document.title and on-page heading reflecting compared providers
  useEffect(() => {
    if (typeof document === 'undefined') return;
    if (providers.length >= 2) {
      const names = providers.map(p => p.displayName || p.name).join(' vs ');
      document.title = 'Perbandingan Hosting (' + names + ')';
      const h = document.getElementById('compare-page-heading');
      if (h) h.textContent = 'Perbandingan Hosting: ' + names + '';
    }
  }, [providers]);

  function remove(slug: string) {
    setRemoving(slug);
    setTimeout(() => toggleCompare(slug), 180); // allow fade-out animation
  }

  if (!providers.length) return null; // avoid FOUC before redirect or init

  return (
    <div className="overflow-x-auto">
      <CompareToast />
      <table className="border-collapse text-sm w-full">
        <thead>
          <tr>
            <th className="text-left bg-gray-50 border border-gray-200 p-3 font-semibold min-w-[10rem]">Metric</th>
            {providers.map(p => (
              <th key={p.slug} className={`text-left bg-gray-50 border border-gray-200 p-3 font-semibold align-top relative transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                <button
                  onClick={() => remove(p.slug)}
                  aria-label={`Remove ${p.name}`}
                  className="absolute top-1 right-1 h-6 w-6 inline-flex items-center justify-center rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm font-semibold"
                >×</button>
                <div className="flex flex-col gap-2 pr-4">
                  <div className="flex items-center gap-2">
                    <img src={p.logo} alt={p.name + ' logo'} className="h-10 w-14 object-contain border border-gray-200 rounded-md bg-white p-1" loading="lazy" />
                    <span className="font-medium text-gray-900 leading-snug break-words">{p.displayName || p.name}</span>
                  </div>
                  {p.startingPrice && (
                    <div className="text-xs text-gray-600">Mulai Rp {p.startingPrice.toLocaleString('id-ID')}/bln</div>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          <tr className="border-t border-gray-200">
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Harga Awal</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.startingPrice ? (
                  <span className="font-semibold text-gray-800">Rp {p.startingPrice.toLocaleString('id-ID')}</span>
                ) : (
                  <span className="text-gray-400 text-xs italic">N/A</span>
                )}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Promo</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.promoDescription ? (
                  <span className="text-xs inline-flex items-center rounded bg-amber-50 text-amber-700 border border-amber-200 px-2 py-1">🏷️ {p.promoDescription}</span>
                ) : (
                  <span className="text-gray-400 text-xs italic">—</span>
                )}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Control Panel</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.controlPanels.length ? (
                  <div className="flex flex-wrap gap-1">
                    {p.controlPanels.map(cp => (
                      <span key={cp.key} className="inline-flex items-center gap-1 text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        <span>{cp.icon}</span>{cp.label}
                      </span>
                    ))}
                  </div>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Fitur Utama</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.features.length ? (
                  <ul className="list-disc list-inside space-y-1 break-words">
                    {p.features.map(f => (<li key={f} className="text-xs leading-snug">{f}</li>))}
                  </ul>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Data Center</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.datacenters.length ? (
                  <div className="flex flex-wrap gap-1 items-center">
                    {p.datacenters.slice(0,8).map(dc => (
                      <span key={dc.location+dc.flag} className="text-lg" title={dc.location}>{dc.flag}</span>
                    ))}
                    {p.datacenters.length > 8 && (
                      <span className="text-[10px] bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded font-medium">+{p.datacenters.length - 8}</span>
                    )}
                  </div>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">HQ</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top text-xs transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.headquarters || <span className="text-gray-400 italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Founded</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top text-xs transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.founded || <span className="text-gray-400 italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Support</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.support.length ? (
                  <div className="flex flex-wrap gap-1">
                    {p.support.map(s => (
                      <span key={s.key} className="inline-flex items-center gap-1 text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded" title={s.label}>
                        <span>{s.icon}</span>{s.label}
                      </span>
                    ))}
                  </div>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Badges</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.badges.length ? (
                  <div className="flex flex-wrap gap-1">
                    {p.badges.map(b => (
                      <span key={b.type + b.label} className="inline-flex items-center px-2 py-0.5 rounded-md text-[11px] font-medium border bg-gray-50 text-gray-700">
                        {b.label}
                      </span>
                    ))}
                  </div>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
          <tr>
            <td className="border border-gray-200 p-3 font-medium bg-gray-50 min-w-[10rem]">Kunjungi</td>
            {providers.map(p => (
              <td key={p.slug} className={`border border-gray-200 p-3 align-top transition-opacity duration-150 min-w-[14rem] ${removing===p.slug ? 'opacity-0' : 'opacity-100'}`}>
                {p.affiliateLink || p.website ? (
                  <a href={p.affiliateLink || p.website} target="_blank" rel="noopener" className="flex items-center justify-center rounded bg-primary text-white hover:text-white text-xs font-semibold px-3 py-2.5 hover:bg-primary/90">Visit</a>
                ) : <span className="text-gray-400 text-xs italic">—</span>}
              </td>
            ))}
          </tr>
        </tbody>
      </table>
    </div>
  );
}
