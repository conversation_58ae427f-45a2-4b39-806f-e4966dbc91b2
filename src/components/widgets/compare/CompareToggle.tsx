import { useEffect, useState } from 'preact/hooks';
import { isCompared, toggleCompare, onCompareLimit, onCompareChange } from '~/utils/compareStore';

interface Props {
  slug: string;
  label?: string;
}

export default function CompareToggle({ slug, label = 'Bandingkan' }: Props) {
  const [active, setActive] = useState<boolean>(false);
  const [limitFlash, setLimitFlash] = useState(false);

  useEffect(() => {
    setActive(isCompared(slug));
    const offLimit = onCompareLimit(() => {
      setLimitFlash(true);
      setTimeout(() => setLimitFlash(false), 1000);
    });
    const offChange = onCompareChange(() => {
      setActive(isCompared(slug));
    });
    return () => {
      offLimit();
      offChange();
    };
  }, [slug]);

  function onChange() {
    const { reason } = toggleCompare(slug);
    setActive(isCompared(slug));
    if (reason === 'limit') {
      // already flashed via event
    }
  }

  return (
    <label className={`inline-flex items-center gap-2 cursor-pointer select-none text-xs font-medium ${active ? 'text-primary' : 'text-gray-600'} ${limitFlash ? 'animate-pulse' : ''}`}
    >
      <input
        type="checkbox"
        checked={active}
        onChange={onChange}
        className={`h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary focus:outline-none disabled:opacity-50 ${active ? 'ring-0' : ''}`}
        aria-label={active ? 'Hapus dari perbandingan' : 'Tambahkan ke perbandingan'}
      />
      <span className={active ? 'font-semibold' : ''}>{label}</span>
    </label>
  );
}
