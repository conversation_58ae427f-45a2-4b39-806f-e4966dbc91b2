---
import Button from '~/components/ui/Button.astro';
import SubmitHostingAd from '~/components/direktori/common/SubmitHostingAd.astro';
import PromoHostingList from '~/components/direktori/common/PromoHostingList.astro';
import type { CollectionEntry } from 'astro:content';
import CompareToggle from '~/components/widgets/compare/CompareToggle.tsx';
import CompareBar from '~/components/widgets/compare/CompareBar.tsx';
import SimilarCategories from '~/components/direktori/common/SimilarCategories.astro';

export interface Props {
  providers: CollectionEntry<'hosting-providers'>[];
  currentPage?: number;
  totalPages?: number;
  baseUrl?: string;
  title?: string;
  subtitle?: string;
  showPagination?: boolean;
  // Optional: for rendering Similar Categories after pagination
  categorySlug?: string;
  showSimilarCategories?: boolean;
  similarCategoriesTitle?: string;
}

const { 
  providers, 
  currentPage = 1, 
  totalPages = 1, 
  baseUrl = '', 
  title,
  subtitle,
  showPagination = true,
  categorySlug,
  showSimilarCategories = true,
  similarCategoriesTitle = 'Kategori Terkait'
} = Astro.props;

// Sort providers by badge priority, then featured status, then by modified date latest
const sortedProviders = providers.sort((a, b) => {
  // Get badge types
  const aSponsored = a.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const bSponsored = b.data.badges?.some(badge => badge.type === 'sponsored') || false;
  const aPromoted = a.data.badges?.some(badge => badge.type === 'promoted') || false;
  const bPromoted = b.data.badges?.some(badge => badge.type === 'promoted') || false;
  const aRecommended = a.data.badges?.some(badge => badge.type === 'recommended') || false;
  const bRecommended = b.data.badges?.some(badge => badge.type === 'recommended') || false;
  const aVerified = a.data.badges?.some(badge => badge.type === 'verified') || false;
  const bVerified = b.data.badges?.some(badge => badge.type === 'verified') || false;

  // Sponsored always first
  if (aSponsored && !bSponsored) return -1;
  if (!aSponsored && bSponsored) return 1;
  
  // If both or neither are sponsored, check promoted
  if (!aSponsored && !bSponsored) {
    if (aPromoted && !bPromoted) return -1;
    if (!aPromoted && bPromoted) return 1;
  }
  
  // If both or neither are sponsored/promoted, check recommended
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted) {
    if (aRecommended && !bRecommended) return -1;
    if (!aRecommended && bRecommended) return 1;
  }
  
  // If both or neither are sponsored/promoted/recommended, check verified
  if (!aSponsored && !bSponsored && !aPromoted && !bPromoted && !aRecommended && !bRecommended) {
    if (aVerified && !bVerified) return -1;
    if (!aVerified && bVerified) return 1;
  }
  
  // If same badge status, check featured
  if (a.data.isFeatured && !b.data.isFeatured) return -1;
  if (!a.data.isFeatured && b.data.isFeatured) return 1;
  
  // Finally sort by modified date (latest first)
  const aModified = new Date(a.data.modifiedAt || 0);
  const bModified = new Date(b.data.modifiedAt || 0);
  return bModified.getTime() - aModified.getTime();
});
---

<section class="pb-12" x-data>
  <script>
    import { initCompareStore } from '~/utils/compareStore';
    if (typeof window !== 'undefined') {
      queueMicrotask(() => initCompareStore());
    }
  </script>
  <div class="mx-auto max-w-global px-4 md:px-6">    
    <!-- Dynamic List Title -->
    {title && (
      <div class="mb-8">
        <h2 class="pt-2 text-xl font-normal text-heading mb-2">
          {title}
        </h2>
        {subtitle && (
          <p class="text-muted">
            {subtitle}
          </p>
        )}
      </div>
    )}

    <!-- Providers List -->
    <div class="space-y-4">
      {sortedProviders.map((provider, index) => {
        // Check if provider is sponsored
        const isSponsored = provider.data.badges?.some(badge => badge.type === 'sponsored') || false;
        
        return (
        <>
          <div class={`rounded-md border hover:shadow-lg transition-all duration-200 relative overflow-hidden ${
            isSponsored 
              ? 'bg-gradient-to-br border-purple-200 from-purple-50 ring-1 ring-purple-100 shadow-lg' 
              : 'border-gray-200 shadow-md'
          }`} data-provider-card={provider.data.slug}>
            <!-- Header Row: Logo, Name, Pricing, Actions -->
            <div class="flex items-start justify-between p-6 pb-0">
              <div class="flex md:items-center space-x-4 flex-1 min-w-0">
                <img
                  src={provider.data.logo}
                  alt={`${provider.data.name} logo`}
                  class="h-20 w-24 md:h-24 md:w-28 shadow-md rounded-md object-contain border border-gray-200  p-1 flex-shrink-0"
                  loading="lazy"
                />
                <div class="flex flex-col min-w-0">
                  <!-- Name and badges in same line -->
                  <div class="items-center gap-2 mb-2 flex-wrap inline-flex">
                    <h3 class="font-semibold text-base">
                      <a
                        href={`/direktori/hosting/${provider.data.slug}/`}
                        class="text-primary transition-colors"
                      >
                        {provider.data.displayName || provider.data.name}
                      </a>
                    </h3>
                    
                    <!-- Badges next to name -->
                    <div class="flex flex-wrap items-center gap-1">
                      {provider.data.badges && provider.data.badges.map((badge) => {
                        if (badge.type === 'verified') {
                          return (
                            <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                              ✓ {badge.label}
                            </span>
                          );
                        }
                        if (badge.type === 'sponsored') {
                          return (
                            <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">
                              👑 {badge.label}
                            </span>
                          );
                        }
                        if (badge.type === 'recommended') {
                          return (
                            <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                              ⭐ {badge.label}
                            </span>
                          );
                        }
                        if (badge.type === 'promoted') {
                          return (
                            <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200">
                              🎯 {badge.label}
                            </span>
                          );
                        }
                        return null;
                      })}
                    </div>
                  </div>
                  
                  <!-- Pricing below name and badges -->
                  <div class="gap-2 mb-2">
                    <div class="flex items-baseline gap-1 mb-2">
                      <div class={`font-semibold ${
                        isSponsored 
                          ? 'text-purple-700 text-lg' 
                          : 'text-gray-900 text-base'
                      }`}>
                        <div class="text-sm text-gray-500">Mulai Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')} 
                          </div>
                      </div>
                      <div class="text-sm text-gray-500">/bulan</div>
                    </div>
                    
                    <!-- Promo Badge -->
                    {(provider.data.pricing.promoCode || provider.data.pricing.promoDescription) && (
                      <span class={`text-xs font-medium border px-2 py-1.5 rounded-md`}>
                        🏷️ {provider.data.pricing.promoDescription || 'Promo'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <!-- Actions in header row -->
              <div class="hidden lg:flex flex-col md:flex-row gap-2 ml-4 flex-shrink-0 min-w-0">
                <Button
                  href={provider.data.affiliateLink || provider.data.website}
                  target="_blank"
                  rel="noopener"
                  variant="primary"
                  class={`text-xs px-3 py-1.5 whitespace-nowrap ${
                    isSponsored 
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700' 
                      : ''
                  }`}
                  icon="tabler:external-link"
                >
                  Visit Website
                </Button>
              </div>
            </div>

            <!-- Content Grid: Description | Company Info | Data Centers -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-0 md:border-gray-100 py-4">
              
              <!-- Mobile Layout: Stack all content vertically -->
              <div class="lg:hidden">
            
                <!-- Company Info Section (Compact at top) -->
                <div class="px-4 pb-2">
                  <div class="flex items-center justify-start gap-6">
                    <!-- Founded Year -->
                    {provider.data.company?.founded && (
                      <div class="flex items-center gap-1.5">
                        <svg class="h-4 w-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <div class="text-sm font-medium">
                          {provider.data.company.founded}
                        </div>
                      </div>
                    )}

                    <!-- Location -->
                    {provider.data.company?.headquarters && provider.data.company.headquarters.length > 0 && (
                      <div class="flex items-center gap-1.5">
                        <svg class="h-4 w-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <div class="text-sm font-medium">
                          {provider.data.company.headquarters[0]}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <!-- Description Section -->
                <div class="py-2 px-4 md:p-4 md:border-b md:border-gray-100">
                  <p class="text-gray-600 text-sm leading-relaxed">
                    {provider.data.longDescription ? 
                      provider.data.longDescription.length > 350 
                        ? provider.data.longDescription.substring(0, 350) + '... '
                        : provider.data.longDescription + ' '
                      : provider.data.description + ' '
                    }
                    <a 
                      href={`/direktori/hosting/${provider.data.slug}/`}
                      class="text-primary font-medium hover:underline"
                    >
                      Baca lebih banyak tentang {provider.data.displayName || provider.data.name}
                    </a>
                  </p>
                </div>
                
                <!-- Data Centers Section -->
                <div class="p-4 border-b border-gray-100">
                  {provider.data.datacenters && provider.data.datacenters.length > 0 ? (
                    <div>
                      <div class="text-xs text-gray-500 mb-2">
                        {provider.data.categories.includes('cloud-management-platform') ? 'Server Target' : 'Data Centers'}
                      </div>
                      <div class="flex flex-wrap gap-1 items-center">
                        {provider.data.datacenters.slice(0, 10).map((dc) => (
                          <span class="text-lg" title={dc.location}>
                            {dc.flag}
                          </span>
                        ))}
                        {provider.data.datacenters.length > 10 && (
                          <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md font-medium">
                            +{provider.data.datacenters.length - 10}
                          </span>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div class="text-sm text-gray-500 italic">
                      No data center info
                    </div>
                  )}
                </div>
                
                <!-- Mobile Actions Section (Bottom) -->
                <div class="p-4">
                  <Button
                    href={provider.data.affiliateLink || provider.data.website}
                    target="_blank"
                    rel="noopener"
                    variant="primary"
                    class={`text-sm px-4 py-2.5 w-full ${
                      isSponsored 
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700' 
                        : ''
                    }`}
                    icon="tabler:external-link"
                  >
                    Visit Website
                  </Button>
                </div>

                    <div class="px-4 pb-2 flex justify-start">
                  <CompareToggle client:load slug={provider.data.slug} />
                </div>
              </div>
              
              <!-- Desktop Layout: Keep existing 3-column layout -->
              <!-- Column 1: Description (50%) -->
              <div class="hidden lg:block lg:col-span-6 p-6 py-4">
                <div class="space-y-3">
                  <!-- Description -->
                  <p class="text-gray-600 text-sm leading-relaxed">
                    {provider.data.longDescription ? 
                      provider.data.longDescription.length > 350 
                        ? provider.data.longDescription.substring(0, 350) + '... '
                        : provider.data.longDescription + ' '
                      : provider.data.description + ' '
                    }
                    <a 
                      href={`/direktori/hosting/${provider.data.slug}/`}
                      class="text-primary font-medium hover:underline"
                    >
                      Baca lebih banyak tentang {provider.data.displayName || provider.data.name}
                    </a>
                  </p>
                </div>
              </div>

              <!-- Column 2: Company Info (25%) -->
              <div class="hidden lg:block lg:col-span-3 p-6 py-4 border-l border-gray-200">
                <div class="space-y-4">
                  

                  <!-- Founded Year -->
                  {provider.data.company?.founded && (
                    <div class="flex items-center gap-2">
                      <svg class="h-5 w-5 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          {provider.data.company.founded}
                        </div>
                      </div>
                    </div>
                  )}

                  <!-- Location -->
                  {provider.data.company?.headquarters && provider.data.company.headquarters.length > 0 && (
                    <div class="flex items-center gap-2">
                      <svg class="h-5 w-5 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          {provider.data.company.headquarters[0]}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <!-- Column 3: Data Centers (25%) -->
              <div class="hidden lg:block lg:col-span-3 p-6 py-4 border-l border-gray-200 bg-gray-50/30">
                {provider.data.datacenters && provider.data.datacenters.length > 0 ? (
                  <div>
                    <div class="text-sm font-medium text-gray-700 mb-2">
                      {provider.data.categories.includes('cloud-management-platform') ? 'Server Target:' : 'Data Center:'}
                    </div>
                    <div class="flex flex-wrap gap-1">
                      {provider.data.datacenters.map((dc) => (
                        <span class="text-lg" title={dc.location}>
                          {dc.flag}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div class="text-sm text-gray-500 italic">
                    No data center info
                  </div>
                )}
              </div>
                     <!-- Desktop bottom-left compare toggle bar -->
            <div class="hidden lg:flex items-center gap-3 px-6 pt-3 text-xs text-gray-500">
              <CompareToggle client:load slug={provider.data.slug} />
            </div>
            </div>
          </div>
        
        <!-- Insert Submit Hosting Ad after 5th provider (index 4) and only on page 1 -->
        {index === 1 && currentPage === 1 && (
          <PromoHostingList />
        )}

        {index === 4 && currentPage === 1 && (
          <SubmitHostingAd />
        )}
        </>
        );
      })}
    </div>

  <CompareBar client:idle />

  <!-- Pagination -->
  {showPagination && totalPages > 1 && (
    <div class="mt-12 flex justify-center">
      <nav class="flex items-center space-x-2">
        <!-- Previous Button -->
        {currentPage > 1 && (
          <Button
            href={currentPage === 2 ? baseUrl : `${baseUrl}page/${currentPage - 1}/`}
            variant="secondary"
            class="px-3 py-2 text-sm hover:no-underline hover:text-white"
          >
            ← Sebelumnya
          </Button>
        )}

        <!-- Page Numbers -->
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <Button
            href={page === 1 ? baseUrl : `${baseUrl}page/${page}/`}
            variant={page === currentPage ? "primary" : "secondary"}
            class="px-3 py-2 text-sm min-w-[40px] hover:no-underline hover:text-white"
          >
            {page}
          </Button>
        ))}

        <!-- Next Button -->
        {currentPage < totalPages && (
          <Button
            href={`${baseUrl}page/${currentPage + 1}/`}
            variant="secondary"
            class="px-3 py-2 text-sm hover:no-underline hover:text-white"
          >
            Selanjutnya →
          </Button>
        )}
      </nav>
    </div>
  )}

  <!-- Similar Categories Section -->
  {categorySlug && showSimilarCategories && (
    <SimilarCategories categorySlug={categorySlug} title={similarCategoriesTitle} />
  )}
</div>

</section>
