---
import type { CollectionEntry } from 'astro:content';
import Button from '~/components/ui/Button.astro';
export interface Props {
  providers?: CollectionEntry<'hosting-providers'>[];
  title?: string;
  description?: string;
  category?: string;
  location?: string;
  showTitle?: boolean;
}

const { 
  providers = [], 
  title = "Sponsored", 
  description,
  category,
  location,
  showTitle = true 
} = Astro.props;

// Filter sponsored providers based on category or location if provided
let filteredProviders = providers.filter((provider) => 
  provider.data.badges?.some(badge => badge.type === 'sponsored')
);

// Filter by category if provided
if (category) {
  filteredProviders = filteredProviders.filter((provider) =>
    provider.data.categories.includes(category)
  );
}

// Filter by location if provided (check datacenters)
if (location) {
  filteredProviders = filteredProviders.filter((provider) =>
    provider.data.datacenters?.some(dc => 
      dc.location.toLowerCase() === location.toLowerCase() ||
      dc.country.toLowerCase() === location.toLowerCase()
    )
  );
}

// Don't render if no sponsored providers found
if (filteredProviders.length === 0) return;
---

<!-- Sponsored Providers Section -->
<section class="pb-12">
  <div class="mx-auto max-w-global px-4 md:px-6">
    {showTitle && (
      <div class="mb-4">
        <h2 class="border-t-2 border-primary pt-2 mt-4 text-xl font-bold text-heading">
          {title}
        </h2>
        {description && (
          <p class="text-muted mt-1">
            {description}
          </p>
        )}
      </div>
    )}

    <!-- Sponsored Providers Grid -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {filteredProviders.map((provider) => (
        <div class="group p-6 overflow-hidden rounded-md hover:shadow-lg transition-shadow relative bg-gradient-to-br from-purple-50 to-blue-50 border border-purple-200 shadow-lg ring-1 ring-purple-100">
          <!-- Badges -->
          <div class="absolute top-4 right-4 flex flex-row flex-wrap items-center gap-2">            
            <!-- Discount Badge -->
            {provider.data.pricing.promoDescription && (
              <span class="inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800">
                🏷️ {provider.data.pricing.promoDescription}
              </span>
            )}
          </div>

          <!-- Provider Logo -->
          <div class="mb-4">
            <img
              src={provider.data.logo}
              alt={`Logo ${provider.data.displayName || provider.data.name}`}
              class="h-6 w-auto object-contain"
              loading="lazy"
            />
          </div>

          <!-- Provider Info -->
          <div class="mb-4">
  
            <p class="text-sm text-muted line-clamp-2">
              {provider.data.description}
            </p>
          </div>

          <!-- Features -->
          <div class="mb-4">
            <div class="flex flex-wrap gap-1">
              {provider.data.features.slice(0, 3).map((feature) => (
                <span class="inline-flex items-center rounded-md bg-purple-200 px-2 py-1 text-xs text-muted">
                  {feature.name}
                </span>
              ))}
              {provider.data.features.length > 3 && (
                <span class="inline-flex items-center rounded-md bg-purple-200 px-2 py-1 text-xs text-muted">
                  +{provider.data.features.length - 3} lainnya
                </span>
              )}
            </div>
          </div>

          <!-- Pricing -->
          <div class="mb-4">
            <div class="flex items-baseline gap-1">
              <span class="text-sm text-muted">Mulai dari</span>
              <span class="text-lg font-bold text-heading">
                Rp {provider.data.pricing.startingPrice.toLocaleString('id-ID')}
              </span>
              <span class="text-sm text-muted">/bulan</span>
            </div>
          </div>

          <!-- CTA Button -->
          <div class="flex gap-3">
            <Button
              href={provider.data.affiliateLink}
              target="_blank"
              rel="noopener noreferrer"
              variant="primary"
              class="flex-1 inline-flex items-center justify-center gap-2 rounded-lg bg-primary px-4 py-2 text-center text-sm font-medium !text-white hover:bg-primary/90 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
              </svg>
              Kunjungi {provider.data.displayName || provider.data.name}
            </Button>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
