---
import Logo from '~/components/Logo.astro';
import ToggleMenu from '~/components/common/ToggleMenu.astro';
import SearchTrigger from '~/components/common/SearchTrigger.astro';
import SearchOverlay from '~/components/common/SearchOverlay.astro';
import HeaderNavigation from './header/HeaderNavigation.astro';
import HeaderActions from './header/HeaderActions.astro';

import { getHomePermalink, trimSlash } from '~/utils/permalinks';
import type { HeaderProps } from './header/types';

export type Props = HeaderProps;

const {
  id = 'header',
  links = [],
  actions = [],
  isSticky = false,
  isFullWidth = false,
  showRssFeed = false,
  position = 'center',
} = Astro.props;

const currentPath = `/${trimSlash(new URL(Astro.url).pathname)}`;
---

<header
  class:list={[
    { sticky: isSticky, relative: !isSticky },
    'bg-white dark:bg-page',
    'top-0 z-40 flex-none mx-auto w-full transition-all duration-300 ease-in-out',
  ]}
  {...isSticky ? { 'data-aw-sticky-header': true } : {}}
  {...id ? { id } : {}}
  transition:name="main-header"
  transition:animate="none"
>
  <div class="absolute inset-0"></div>
  <div
    class:list={[
      'relative text-default py-0.5 px-3 md:px-6 mx-auto w-full',
      {
        'md:flex md:justify-between': position !== 'right',
      },
      {
        'md:flex md:justify-between': position === 'right',
      },
      {
        'max-w-global': !isFullWidth,
      },
    ]}
  >
    <div class:list={[{ 'mr-auto rtl:mr-0 rtl:ml-auto': position === 'right' }, 'flex items-center justify-between w-full md:w-auto']}>
      <a class="flex items-center" href={getHomePermalink()}>
        <Logo />
      </a>
      
      <div class="flex items-center md:hidden z-50 relative">
        <SearchTrigger class="-ml-2" transition:name="search-trigger" />
        <ToggleMenu />
      </div>
    </div>
    
    <!-- Search Actions - DOM order first for View Transitions stability -->
    <div class="header-search-actions flex items-center md:gap-2">
      <div class="hidden md:block">
        <SearchTrigger transition:name="search-trigger" />
      </div>
      <HeaderActions
        actions={actions}
        showRssFeed={showRssFeed}
        position={position}
      />
    </div>
    
    <HeaderNavigation links={links} currentPath={currentPath} position={position} />




  </div>
</header>

<!-- Search Overlay -->
<SearchOverlay />


<script>
  // Import dropdown functionality using Astro's default processing
  import { initializeDropdown } from './header/headerDropdown.js';
  
  // Initialize the dropdown when DOM is ready - with View Transitions support
  function initHeader() {
    if (typeof initializeDropdown === 'function') {
      initializeDropdown();
    }
  }

  // Multiple initialization points for reliability
  document.addEventListener('astro:page-load', initHeader);
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHeader);
  } else {
    initHeader();
  }
</script>

<style>
  /* CSS Order fix for View Transitions - separate DOM order from visual order */
  @media (min-width: 768px) {
    .header-container {
      display: flex !important;
      justify-content: space-between !important;
    }
    
    /* Logo section - order 1 (first visually) */
    .header-container > div:first-child {
      order: 1;
    }
    
    /* Search Actions - order 3 (last visually) */
    .header-search-actions {
      order: 3;
    }
    
    /* Navigation - order 2 (middle visually) */
    .header-container > nav {
      order: 2;
    }
  }
</style>
