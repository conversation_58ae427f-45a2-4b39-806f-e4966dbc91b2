---
import type { MenuLink } from './types';
import { getTotalProviderCount, formatTotalProviderCount } from '~/utils/providerStats';

export interface Props {
  links: Array<MenuLink>;
}

const { links } = Astro.props;

// Get actual provider count
const totalProviderCount = await getTotalProviderCount();
const providerCountText = formatTotalProviderCount(totalProviderCount);
---

<!-- Mega Menu for Direktori Hosting -->
<div class="dropdown-menu md:backdrop-blur-md dark:md:bg-dark rounded-xl md:absolute pl-4 md:pl-0 md:hidden md:bg-white/95 md:min-w-[720px] md:w-[720px] drop-shadow-2xl md:left-1/2 md:transform md:-translate-x-1/2 border border-gray-100 dark:border-gray-700">
  <div class="p-6">
    <div class="mb-6">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Direktori Hosting Indonesia</h3>
      <p class="text-sm text-gray-600 dark:text-gray-300">Temukan provider hosting terbaik dari {providerCountText}</p>
    </div>
    
    <div class="grid grid-cols-3 gap-4">
      {links.map((section) => (
        section.isSection && section.links && (
          <div>
            <h4 class="text-sm font-bold text-neutral-700 dark:text-white mb-3 pb-2 border-b border-neutral-200 dark:border-neutral-600">
              {section.text}
            </h4>
            <div class="space-y-1">
              {section.links.map((link) => (
                <a 
                  href={link.href}
                  class="group p-2 rounded-lg hover:bg-bg-muted hover:no-underline dark:hover:bg-bg-muted transition-all duration-200 block"
                >
                  <div class="space-y-1">
                    <h5 class="text-xs font-semibold text-neutral-700 dark:text-white group-hover:text-primary transition-colors">
                      {link.text}
                    </h5>
                    <p class="text-xs text-neutral-500 dark:text-neutral-400">
                      {link.description}
                    </p>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )
      ))}
    </div>
  </div>
</div>
