---
import { Icon } from 'astro-icon/components';
import type { MenuLink } from './types';

export interface Props {
  links: Array<MenuLink>;
}

const { links } = Astro.props;
---

<!-- Mega Menu for Layanan -->
<div class="dropdown-menu md:backdrop-blur-md dark:md:bg-dark rounded-xl md:absolute pl-4 md:pl-0 md:hidden md:bg-white/95 md:min-w-[680px] md:w-[680px] drop-shadow-2xl md:left-1/2 md:transform md:-translate-x-1/2 border border-gray-100 dark:border-gray-700">
  <div class="p-6">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Layanan Profesional Kami</h3>
      <p class="text-sm text-gray-600 dark:text-gray-300">Sol<PERSON>i lengkap untuk kebutuhan website dan hosting Anda</p>
    </div>
    
    <div class="grid grid-cols-2 gap-2">
      <!-- WordPress Hosting Premium - Special Featured Item -->
      <div class="col-span-2 mb-4">
        <a 
          href="https://harunstudio.com/jasa/wordpress-hosting/"
          target="_blank"
          rel="noopener"
          class="group relative p-3 rounded-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-slate-800 dark:to-slate-700 border-2 border-transparent hover:border-red-200 hover:no-underline transition-all duration-300 block overflow-hidden"
          data-umami-event="Click Layanan - WordPress Hosting Premium"
        >
          <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 via-red-600/10 to-red-700/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center shadow-lg">
                <Icon name="tabler:server" class="w-5 h-5 text-white" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-bold text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                WordPress Hosting Premium
              </h4>
              <p class="text-xs text-gray-600 dark:text-gray-300 mt-1">
                Hosting cepat dan aman untuk business owner yang sibuk. Personal support via WhatsApp, tanpa control panel ribet.
              </p>
              <div class="flex items-center mt-1.5 text-xs text-red-600 dark:text-red-400 font-medium">
                <Icon name="tabler:arrow-right" class="w-4 h-4 mr-1 group-hover:translate-x-1 transition-transform" />
                Mulai dari 200k/bulan
              </div>
            </div>
          </div>
        </a>
      </div>
      
      <!-- Other Services -->
      {links.slice(1).map(({ text: text2, href: href2, 'data-umami-event': umamiEvent }) => {
        const getServiceIcon = (serviceName) => {
          if (serviceName.includes('Pembuatan Website')) return 'tabler:code';
          if (serviceName.includes('Maintenance')) return 'tabler:tools';
          if (serviceName.includes('Optimasi') || serviceName.includes('Konversi')) return 'tabler:bolt';
          if (serviceName.includes('Migrasi')) return 'tabler:transfer';
          if (serviceName.includes('Malware')) return 'tabler:shield-check';
          if (serviceName.includes('Perbaikan')) return 'tabler:settings';
          return 'tabler:world';
        };
        
        const getServiceDescription = (serviceName) => {
          if (serviceName.includes('Pembuatan Website')) return 'Website profesional untuk bisnis Anda';
          if (serviceName.includes('Maintenance')) return 'Perawatan rutin untuk performa optimal';
          if (serviceName.includes('Optimasi') || serviceName.includes('Konversi')) return 'Perbaikan dan peningkatan performa';
          if (serviceName.includes('Migrasi WordPress')) return 'Layanan perpindahan dan konversi';
          if (serviceName.includes('Migrasi Website ke Astro')) return 'Konversi ke framework modern';
          if (serviceName.includes('Malware')) return 'Pembersihan malware dan keamanan';
          if (serviceName.includes('Perbaikan')) return 'Solusi untuk website bermasalah';
          return 'Layanan website profesional';
        };
        
        return (
          <a 
            href={href2}
            target="_blank"
            rel="noopener"
            class="group p-2.5 rounded-lg hover:bg-bg-muted hover:no-underline dark:hover:bg-bg-muted transition-all duration-200 block"
            data-umami-event={umamiEvent}
          >
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:bg-red-100 dark:group-hover:bg-red-900 transition-colors">
                  <Icon name={getServiceIcon(text2)} class="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h4 class="text-xs font-semibold text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                  {text2}
                </h4>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                  {getServiceDescription(text2)}
                </p>
              </div>
              <Icon name="tabler:external-link" class="w-3 h-3 text-gray-400 group-hover:text-red-500 transition-colors flex-shrink-0" />
            </div>
          </a>
        );
      })}
    </div>
  </div>
</div>
