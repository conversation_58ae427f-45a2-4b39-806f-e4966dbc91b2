// Header dropdown functionality
export function initializeDropdown() {
  // Make toggleDropdown available globally for onclick handlers
  window.toggleDropdown = function(button) {
    const dropdown = button.closest('.dropdown');
    const isExpanded = dropdown.classList.toggle('expanded');
    const icon = button.querySelector('svg');
    
    // Toggle arrow rotation
    if (icon) {
      icon.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
    }
    
    // Close other dropdowns when opening a new one
    if (isExpanded) {
      document.querySelectorAll('.dropdown').forEach(dd => {
        if (dd !== dropdown) {
          dd.classList.remove('expanded');
          const otherIcon = dd.querySelector('svg');
          if (otherIcon) otherIcon.style.transform = 'rotate(0deg)';
        }
      });
    }
  };
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.dropdown')) {
      document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.classList.remove('expanded');
        const icon = dropdown.querySelector('svg');
        if (icon) icon.style.transform = 'rotate(0deg)';
      });
    }
  });
  
  // Close dropdowns when pressing Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.classList.remove('expanded');
        const icon = dropdown.querySelector('svg');
        if (icon) icon.style.transform = 'rotate(0deg)';
      });
    }
  });
}
