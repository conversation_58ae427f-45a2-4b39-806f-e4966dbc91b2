# Header Component Refactoring

## Overview
File `Header.astro` telah di-refactor menjadi beberapa komponen yang lebih kecil dan mudah di-maintain untuk meningkatkan organisasi kode dan maintainability.

## Structure

```
src/components/widgets/header/
├── types.ts                      # Interface dan type definitions
├── HeaderMegaMenuLayanan.astro   # Mega menu untuk layanan
├── HeaderMegaMenuDirektori.astro # Mega menu untuk direktori hosting
├── HeaderDropdown.astro          # Dropdown menu biasa
├── HeaderNavigation.astro        # Navigation menu utama
├── HeaderActions.astro           # Actions section (search, RSS, buttons)
├── headerDropdown.js             # JavaScript functionality
└── README.md                     # Dokumentasi ini
```

## Components

### 1. `types.ts`
- **Purpose**: Mendefisikan interface dan types yang digunakan di seluruh header components
- **Exports**: `Link`, `MenuLink`, `HeaderProps`

### 2. `HeaderMegaMenuLayanan.astro`
- **Purpose**: Mega menu khusus untuk layanan
- **Features**: Featured WordPress hosting item, service icons, descriptions
- **Props**: `links: Array<MenuLink>`

### 3. `HeaderMegaMenuDirektori.astro`
- **Purpose**: Mega menu khusus untuk direktori hosting
- **Features**: Grid layout, kategorisasi, bottom CTA
- **Props**: `links: Array<MenuLink>`

### 4. `HeaderDropdown.astro`
- **Purpose**: Dropdown menu biasa untuk menu lainnya
- **Props**: `links: Array<MenuLink>`, `currentPath: string`

### 5. `HeaderNavigation.astro`
- **Purpose**: Navigation menu utama yang mengatur semua dropdown dan menu items
- **Props**: `links: Array<MenuLink>`, `currentPath: string`, `position?: string`

### 6. `HeaderActions.astro`
- **Purpose**: Section untuk search, RSS feed, dan action buttons
- **Props**: `actions?: Array<CallToAction>`, `showRssFeed?: boolean`, `position?: string`

### 7. `headerDropdown.js`
- **Purpose**: JavaScript functionality untuk dropdown behavior
- **Features**: Toggle dropdowns, close on outside click, keyboard navigation

## Usage

```astro
---
import Header from '~/components/widgets/Header.astro';
---

<Header
  links={navigationData}
  actions={headerActions}
  isSticky={true}
  showRssFeed={true}
  position="center"
/>
```

## Benefits

### 1. **Maintainability**
- Setiap komponen memiliki tanggung jawab yang jelas
- Mudah untuk mengubah atau memperbaiki bagian tertentu
- Kode lebih mudah dibaca dan dipahami

### 2. **Reusability**
- Komponen dapat digunakan kembali di tempat lain
- Mega menu dapat disesuaikan untuk kebutuhan berbeda

### 3. **Testing**
- Setiap komponen dapat ditest secara independen
- Debugging lebih mudah dengan komponen yang terisolasi

### 4. **Performance**
- JavaScript di-load secara modular
- Lebih efisien dalam bundling

## Migration Notes

- Semua functionality dari Header.astro asli tetap dipertahankan
- Interface dan props tetap sama untuk backward compatibility
- JavaScript functionality dipindahkan ke file terpisah dengan auto-initialization

## Development

Saat mengembangkan atau mengubah header:

1. **Untuk perubahan layout**: Edit komponen yang sesuai (HeaderNavigation, HeaderActions, dll.)
2. **Untuk menambah menu type**: Buat komponen baru dan tambahkan logic di HeaderNavigation
3. **Untuk perubahan behavior**: Edit headerDropdown.js
4. **Untuk menambah props**: Update types.ts dan props interface yang relevan
