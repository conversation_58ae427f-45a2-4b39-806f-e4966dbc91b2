---
import { Icon } from 'astro-icon/components';
import Button from '~/components/ui/Button.astro';
import { getAsset } from '~/utils/permalinks';
import type { CallToAction } from '~/types';

export interface Props {
  actions?: Array<CallToAction>;
  showRssFeed?: boolean;
  position?: string;
}

const { 
  actions = [], 
  showRssFeed = false, 
  position = 'center' 
} = Astro.props;
---

<div
  class:list={[
    { 'ml-auto rtl:ml-0 rtl:mr-auto': position === 'left' },
    'hidden md:self-center md:flex items-center md:mb-0 fixed w-full md:w-auto md:static justify-end left-0 rtl:left-auto rtl:right-0 bottom-0 p-3 md:p-0 md:justify-self-end',
  ]}
>
  <div class="items-center flex justify-between w-full md:w-auto">
    <div class="flex items-center">
      {
        showRssFeed && (
          <a
            class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
            aria-label="RSS Feed"
            href={getAsset('/rss.xml')}
          >
            <Icon name="tabler:rss" class="w-5 h-5" />
          </a>
        )
      }
    </div>
    {
      actions?.length ? (
        <span class="ml-4 rtl:ml-0 rtl:mr-4">
          {actions.map((btnProps) => (
            <Button {...btnProps} class="ml-2 py-2.5 px-5.5 md:px-6 font-semibold shadow-none text-sm w-auto" />
          ))}
        </span>
      ) : (
        ''
      )
    }
  </div>
</div>
