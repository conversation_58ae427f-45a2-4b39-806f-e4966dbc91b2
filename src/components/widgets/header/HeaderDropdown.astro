---
import type { MenuLink } from './types';

export interface Props {
  links: Array<MenuLink>;
  currentPath: string;
}

const { links, currentPath } = Astro.props;
---

<!-- Regular Dropdown for other menus -->
<ul class="dropdown-menu md:backdrop-blur-md dark:md:bg-dark rounded md:absolute pl-4 md:pl-0 md:hidden font-medium md:bg-white/90 md:min-w-[200px] drop-shadow-xl">
  {links.map(({ text: text2, href: href2 }) => (
    <li>
      <a
        class:list={[
          'first:rounded-t last:rounded-b hover:bg-bg-muted hover:no-underline dark:hover:bg-bg-muted py-2 px-5 block whitespace-no-wrap',
          { 'aw-link-active': href2 === currentPath },
        ]}
        href={href2}
      >
        {text2}
      </a>
    </li>
  ))}
</ul>
