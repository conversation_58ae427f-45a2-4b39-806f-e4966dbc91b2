import type { CallToAction } from '~/types';

export interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
  'data-umami-event'?: string;
  description?: string;
}

export interface MenuLink extends Link {
  links?: Array<MenuLink>;
  isSection?: boolean;
}

export interface HeaderProps {
  id?: string;
  links?: Array<MenuLink>;
  actions?: Array<CallToAction>;
  isSticky?: boolean;
  isFullWidth?: boolean;
  showToggleTheme?: boolean;
  showRssFeed?: boolean;
  position?: string;
}
