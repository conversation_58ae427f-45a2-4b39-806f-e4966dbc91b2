---
import { Icon } from 'astro-icon/components';
import type { MenuLink } from './types';
import HeaderMegaMenuLayanan from './HeaderMegaMenuLayanan.astro';
import HeaderMegaMenuDirektori from './HeaderMegaMenuDirektori.astro';
import HeaderDropdown from './HeaderDropdown.astro';
import { getProviderCountByCategory, getProviderCountByLocation, formatProviderCount } from '~/utils/providerStats';

export interface Props {
  links: Array<MenuLink>;
  currentPath: string;
  position?: string;
}

const { links, currentPath, position = 'center' } = Astro.props;

// Get dynamic stats for navigation
const categoryStats = await getProviderCountByCategory();
const locationStats = await getProviderCountByLocation();

// Update links with dynamic counts
const updatedLinks = links.map(link => {
  if (link.text === 'Direktori Hosting' && link.links) {
    return {
      ...link,
      links: link.links.map(section => {
        if (section.isSection && section.links) {
          return {
            ...section,
            links: section.links.map(item => {
              // Update category counts
              if (section.text === 'Kategori Populer') {
                if (item.text === 'Shared Hosting') {
                  return { ...item, description: formatProviderCount(categoryStats['shared-hosting'] || 0) };
                }
                if (item.text === 'VPS Hosting') {
                  return { ...item, description: formatProviderCount(categoryStats['unmanaged-vps'] || 0) };
                }
                if (item.text === 'Cloud Hosting') {
                  return { ...item, description: formatProviderCount(categoryStats['cloud-hosting'] || 0) };
                }
                if (item.text === 'WordPress Hosting') {
                  return { ...item, description: formatProviderCount(categoryStats['wordpress-hosting'] || 0) };
                }
                if (item.text === 'Dedicated Server') {
                  return { ...item, description: formatProviderCount(categoryStats['dedicated-server'] || 0) };
                }
                if (item.text === 'Reseller Hosting') {
                  return { ...item, description: formatProviderCount(categoryStats['reseller-hosting'] || 0) };
                }
              }
              // Update location counts
              if (section.text === 'Lokasi Populer') {
                if (item.text === 'Indonesia') {
                  return { ...item, description: formatProviderCount(locationStats['indonesia'] || 0) };
                }
                if (item.text === 'Singapore') {
                  return { ...item, description: formatProviderCount(locationStats['singapore'] || 0) };
                }
                if (item.text === 'United States') {
                  return { ...item, description: formatProviderCount(locationStats['united-states'] || 0) };
                }
              }
              return item;
            })
          };
        }
        return section;
      })
    };
  }
  return link;
});
---

<nav
  class:list={[
    'items-center w-full md:w-auto hidden md:flex text-default overflow-y-auto overflow-x-hidden md:overflow-y-visible md:overflow-x-auto',
    {
      'md:mx-5 md:justify-self-center': position !== 'right',
      'ml-auto': position === 'right',
    }
  ]}
  aria-label="Main navigation"
>
  <ul
    class="flex flex-col md:flex-row md:self-center w-full md:w-auto text-base md:text-[0.9rem] font-medium md:justify-center"
  >
    {
      updatedLinks.map(({ text, href, links, icon }) => (
        <li class={links?.length ? 'dropdown' : ''}>
          {links?.length ? (
            <>
              <button
                type="button"
                class="group flex items-center justify-between w-full md:w-auto px-4 py-3 text-left hover:text-link hover:no-underline dark:hover:text-white transition-colors"
                data-dropdown={text?.toLowerCase()?.replace(/\s+/g, '-') || 'menu'}
                onclick="toggleDropdown(this); event.stopPropagation();"
              >
                <span class="flex-1 font-semibold">{text}</span>
                <Icon 
                  name="tabler:chevron-down" 
                  class="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2 transform transition-transform duration-200"
                  id={`dropdown-icon-${text?.toLowerCase()?.replace(/\s+/g, '-') || 'menu'}`}
                />
              </button>
              
              {text === 'Layanan' ? (
                <HeaderMegaMenuLayanan links={links} />
              ) : text === 'Direktori Hosting' ? (
                <HeaderMegaMenuDirektori links={links} />
              ) : (
                <HeaderDropdown links={links} currentPath={currentPath} />
              )}
            </>
          ) : (
            <a
              class:list={[
                '!text-default hover:text-link hover:no-underline dark:hover:text-white px-4 py-3 flex items-center whitespace-nowrap',
                { 'aw-link-active': href === currentPath },
              ]}
              href={href}
              {...(href?.startsWith('http') ? { target: '_blank', rel: 'noopener' } : {})}
            >
              {icon && <Icon name={icon} class="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1 text-muted" />}
              {text}
              {href?.startsWith('http') && (
                 <Icon name="tabler:external-link" class="w-4 h-4 ml-1 text-muted" />
              )}
            </a>
          )}
        </li>
      ))
    }
  </ul>
</nav>
