---
// Sponsor Banner Component - Samudra Host
// Menampilkan banner sponsor di bawah header pada semua halaman
---

<div class="sticky z-30  dark:hover:border-white shadow-sm" style="top: var(--header-height, 60px);">
  <div class="max-w-7xl mx-auto px-4 py-2">
    <a 
      href="https://penasihathosting.com/go/samudrahost"
      target="_blank"
      rel="nofollow noopener"
      data-sponsor-banner="samudra-host"
      class="bg-gray-50 dark:bg-gray-800 border border-transparent hover:border-purple-700 flex items-center justify-center gap-4 text-center hover:no-underline transition-colors duration-200 rounded-lg px-4 py-2 -mx-4 -my-2 cursor-pointer"
    >
      <!-- Sponsor Label -->
      <span class="hidden md:block text-xs text-gray-500">
        Sponsor
      </span>
      
      <!-- Sponsor Message -->
      <div class="flex items-center gap-2">
        <!-- Samudra Host Logo -->
        <img 
          src="https://cdn.penasihathosting.com/image/Samudrahost%20Logo.webp" 
          alt="Samudra Host" 
          class="w-20 h-6 object-contain"
          loading="eager"
        />
        <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
          — Diskon 80% untuk 6 orang pertama
        </span>
      </div>

      
      <!-- CTA Button -->
      <span 
        class="hidden md:block bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-semibold transition-all duration-200 shadow-sm hover:shadow-md pointer-events-none"
      >
        Ambil Promo
      </span>
    </a>
  </div>
</div>

<script>
  // Calculate and set header height for sponsor banner positioning
  function setSponsorBannerPosition() {
    const header = document.querySelector('header');
    const sponsorBanner = document.querySelector('[style*="--header-height"]');
    
    if (header && sponsorBanner) {
      const headerHeight = header.offsetHeight;
      document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
    }
  }

  // Set position on load and resize
  document.addEventListener('DOMContentLoaded', setSponsorBannerPosition);
  window.addEventListener('resize', setSponsorBannerPosition);
  
  // Re-calculate after Astro navigation
  document.addEventListener('astro:after-swap', setSponsorBannerPosition);
</script>
