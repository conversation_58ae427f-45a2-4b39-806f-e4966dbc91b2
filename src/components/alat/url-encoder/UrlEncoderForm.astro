---
import { Icon } from 'astro-icon/components';
---

<!-- URL Encoder/Decoder Form -->
<div id="urlEncoderContent">
  <form id="urlEncoderForm" class="space-y-6">
    <!-- Mode Selection -->
    <div>
      <label class="block text-sm font-medium text-default mb-2">
        Mode:
      </label>
      <div class="flex space-x-1 bg-bg-muted p-1 rounded-lg mb-4">
        <button 
          type="button" 
          id="encodeModeBtn" 
          class="tab-btn flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
        >
          <Icon name="tabler:lock" class="w-4 h-4 mr-2" />
          Encode
        </button>
        <button 
          type="button" 
          id="decodeModeBtn" 
          class="tab-btn flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
        >
          <Icon name="tabler:lock-open" class="w-4 h-4 mr-2" />
          Decode
        </button>
      </div>
    </div>

    <!-- Text Input -->
    <div>
      <label for="inputText" class="block text-sm font-medium text-default mb-2">
        Masukkan Teks:
      </label>
      <textarea
        id="inputText"
        rows="6"
        class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors font-mono"
        placeholder="Masukkan teks yang ingin di-encode/decode..."
      ></textarea>
      <div id="errorMessage" class="mt-2 text-sm text-danger hidden"></div>
    </div>

    <!-- Output Text -->
    <div>
      <label for="outputText" class="block text-sm font-medium text-default mb-2">
        Hasil:
      </label>
      <div class="relative">
        <textarea
          id="outputText"
          rows="6"
          readonly
          class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-muted text-default font-mono cursor-not-allowed overflow-x-auto"
          placeholder="Hasil akan muncul di sini."
        ></textarea>
        <button 
          id="copyBtn" 
          class="absolute top-3 right-3 p-2 rounded-md hover:bg-bg-muted transition-colors" 
          title="Copy Output"
        >
          <Icon name="tabler:copy" class="w-5 h-5 text-muted" />
        </button>
      </div>
      <p id="copyFeedback" class="mt-2 text-sm text-success hidden">Hasil berhasil disalin!</p>
    </div>

    <!-- Buttons -->
    <div class="grid grid-cols-2 gap-4">
      <button 
        type="button" 
        id="clearBtn"
        class="btn-secondary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
      >
        <Icon name="tabler:x" class="w-5 h-5 mr-2" />
        Clear
      </button>
      <button 
        type="button" 
        id="convertBtn"
        class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
      >
        <Icon name="tabler:code" class="w-5 h-5 mr-2" />
        Encode URL
      </button>
    </div>
  </form>
</div>

<style>
  /* Tab styling following design patterns */
  .tab-btn {
    @apply text-muted bg-transparent hover:text-default;
  }

  .tab-btn:hover {
    background-color: var(--aw-color-bg-input);
  }

  .tab-btn.active {
    background-color: var(--aw-color-bg-section);
    color: var(--aw-color-text-default);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }

  /* Responsive tab adjustments */
  @media (max-width: 640px) {
    .tab-btn {
      @apply text-xs px-2 py-2;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const encodeModeBtn = document.getElementById('encodeModeBtn');
    const decodeModeBtn = document.getElementById('decodeModeBtn');
    
    if (encodeModeBtn && decodeModeBtn) {
      // Add active class to encode button by default
      encodeModeBtn.classList.add('active');
      
      encodeModeBtn.addEventListener('click', function() {
        encodeModeBtn.classList.add('active');
        decodeModeBtn.classList.remove('active');
      });
      
      decodeModeBtn.addEventListener('click', function() {
        decodeModeBtn.classList.add('active');
        encodeModeBtn.classList.remove('active');
      });
    }
  });
</script> 