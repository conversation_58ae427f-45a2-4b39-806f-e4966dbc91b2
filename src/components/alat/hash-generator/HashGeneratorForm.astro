---
import { Icon } from 'astro-icon/components';
---

<div id="hashGeneratorContent">
  <form id="hashGeneratorForm" class="space-y-6">
    <!-- Text Input -->
    <div>
      <label for="textInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Masukkan Teks di sini:
      </label>
      <textarea
        id="textInput"
        rows="6"
        class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-bg-input dark:bg-bg-input text-[var(--aw-color-text-default)] focus:ring-2 focus:ring-primary focus:border-primary transition-colors font-mono"
        placeholder="Masukkan teks yang ingin di-hash..."
      ></textarea>
       <div id="textInputErrorMessage" class="mt-2 text-sm text-red-600 dark:text-red-400 hidden"></div>
    </div>

    <!-- Algorithm Selection -->
    <div>
      <label for="algorithmSelect" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Pilih Algoritma Hash:
      </label>
      <select id="algorithmSelect" class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-bg-input dark:bg-bg-input text-[var(--aw-color-text-default)] focus:ring-2 focus:ring-primary focus:border-primary transition-colors">
        <option value="SHA-256">SHA-256</option>
        <option value="SHA-1">SHA-1</option>
        <option value="SHA-512">SHA-512</option>
        <!-- MD5 option can be added here later if Crypto-JS is included -->
        <!-- <option value="MD5">MD5</option> -->
      </select>
    </div>

     <!-- Output Hash -->
    <div>
      <label for="hashOutput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Hasil Hash:
      </label>
      <div class="relative">
         <textarea
            id="hashOutput"
            rows="3"
            readonly
            class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-bg-muted dark:bg-gray-700 text-[var(--aw-color-text-default)] font-mono cursor-not-allowed overflow-x-auto"
            placeholder="Klik 'Generate Hash' untuk menghasilkan hash dari teks di atas."
          ></textarea>
           <button id="copyHashBtn" class="absolute top-3 right-3 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" title="Copy Hash">
              <Icon name="tabler:copy" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
      </div>
       <p id="copyFeedbackMessage" class="mt-2 text-sm text-green-600 dark:text-green-400 hidden">Hash berhasil disalin!</p>
    </div>

    <!-- Buttons -->
    <div class="grid grid-cols-2 gap-4">
        <button type="button" id="clearButton"
          class="btn-secondary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
        >
         <Icon name="tabler:x" class="w-5 h-5 mr-2" />
          Clear
        </button>
        <button type="submit" id="generateButton"
          class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
        >
           <Icon name="tabler:lock-access" class="w-5 h-5 mr-2" />
          Generate Hash
        </button>
    </div>

  </form>
</div>

<style>
  /* Specific styles for this form if needed */
</style> 