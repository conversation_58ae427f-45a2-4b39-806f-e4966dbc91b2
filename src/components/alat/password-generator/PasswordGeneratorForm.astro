---
import { Icon } from 'astro-icon/components';

---

<div id="passwordGeneratorContent" class="tab-content">
  <form id="passwordGeneratorForm" class="space-y-6">
    <!-- Password Length -->
    <div>
      <label for="passwordLength" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Panjang Password
      </label>
      <input
        type="number"
        id="passwordLength"
        name="length"
        min="4"
        max="128"
        value="12"
        class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
      />
    </div>

    <!-- Character Options -->
    <div class="space-y-3">
      <p class="text-sm font-medium text-muted">Sertakan:</p>
      <div class="grid grid-cols-2 gap-3">
        <label class="flex items-center text-[var(--aw-color-text-default)]">
          <input type="checkbox" id="includeUppercase" name="uppercase" checked class="form-checkbox h-5 w-5 text-primary rounded focus:ring-primary">
          <span class="ml-2 text-sm">Huruf Besar (A-Z)</span>
        </label>
        <label class="flex items-center text-[var(--aw-color-text-default)]">
          <input type="checkbox" id="includeLowercase" name="lowercase" checked class="form-checkbox h-5 w-5 text-primary rounded focus:ring-primary">
          <span class="ml-2 text-sm">Huruf Kecil (a-z)</span>
        </label>
        <label class="flex items-center text-[var(--aw-color-text-default)]">
          <input type="checkbox" id="includeNumbers" name="numbers" checked class="form-checkbox h-5 w-5 text-primary rounded focus:ring-primary">
          <span class="ml-2 text-sm">Angka (0-9)</span>
        </label>
        <label class="flex items-center text-[var(--aw-color-text-default)]">
          <input type="checkbox" id="includeSymbols" name="symbols" class="form-checkbox h-5 w-5 text-primary rounded focus:ring-primary">
          <span class="ml-2 text-sm">Simbol (!@#$...)</span>
        </label>
      </div>
    </div>

     <!-- Exclude Similar Characters (Optional) -->
    <div>
      <label class="flex items-center text-[var(--aw-color-text-default)]">
        <input type="checkbox" id="excludeSimilar" name="excludeSimilar" class="form-checkbox h-5 w-5 text-primary rounded focus:ring-primary">
        <span class="ml-2 text-sm">Kecualikan karakter yang mirip (i, l, 1, L, o, 0, O)</span>
      </label>
    </div>

    <button
      type="submit"
      class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
    >
      <Icon name="tabler:lock-access" class="w-5 h-5 mr-2" />
      Generate Password
    </button>
  </form>
</div>

<style>
  /* Add specific styles for this form if needed */
</style> 