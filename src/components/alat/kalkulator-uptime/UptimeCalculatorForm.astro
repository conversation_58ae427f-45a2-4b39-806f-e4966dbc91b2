---
import { Icon } from 'astro-icon/components';
---

<div id="uptimeContent" class="tab-content">
  <h2 class="text-xl font-bold text-heading mb-6">Kalkulator Uptime</h2>

  <form id="uptimeForm" class="space-y-6">
    <div>
      <label for="uptimeInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Persentase Uptime (%)
      </label>
      <div class="relative">
        <input
          type="number"
          id="uptimeInput"
          name="uptime"
          min="0"
          max="100"
          step="0.001"
          placeholder="99.9"
          class="w-full px-4 py-3 text-lg border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
        />
        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
          <span class="text-[var(--aw-color-text-muted)] text-lg">%</span>
        </div>
      </div>
      <div id="uptimeErrorMessage" class="mt-2 text-sm text-red-600 hidden"></div>
    </div>

    <!-- Quick Preset Buttons -->
    <div class="space-y-3">
      <p class="text-sm font-medium text-muted">Atau pilih preset umum:</p>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
        <button type="button" class="uptime-preset-btn" data-value="99">99%</button>
        <button type="button" class="uptime-preset-btn" data-value="99.9">99.9%</button>
        <button type="button" class="uptime-preset-btn" data-value="99.99">99.99%</button>
        <button type="button" class="uptime-preset-btn" data-value="99.999">99.999%</button>
      </div>
    </div>

    <button
      type="submit"
      class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
    >
      <Icon name="tabler:calculator" class="w-5 h-5 mr-2" />
      Hitung Downtime
    </button>
  </form>
</div>

<style>
  /* Matching existing site design patterns */
  .uptime-preset-btn {
    @apply px-3 py-2 text-sm font-medium border text-default bg-bg-input rounded-lg hover:bg-primary hover:text-white hover:border-primary transition-colors duration-200;
  }

  .uptime-preset-btn.active {
    @apply bg-primary text-white border-primary;
  }

  /* Focus states matching site patterns */
  input:focus {
    outline: none;
  }
</style> 