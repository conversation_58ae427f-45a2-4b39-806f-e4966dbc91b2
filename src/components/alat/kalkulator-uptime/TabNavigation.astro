---
import { Icon } from 'astro-icon/components';
---

<div class="flex space-x-1 bg-bg-muted p-1 rounded-lg mb-6" role="tablist">
  <button
    id="uptimeTab"
    class="tab-btn flex-1 py-2.5 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
    role="tab"
    aria-selected="true"
    aria-controls="uptimeContent"
    tabindex="0"
  >
    <Icon name="tabler:trending-up" class="w-4 h-4 mr-2" />
    <span>Kalkulator Uptime</span>
  </button>
  <button
    id="downtimeTab"
    class="tab-btn flex-1 py-2.5 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center"
    role="tab"
    aria-selected="false"
    aria-controls="downtimeContent"
    tabindex="-1"
  >
    <Icon name="tabler:trending-down" class="w-4 h-4 mr-2" />
    <span>Kalkulator Downtime</span>
  </button>
</div>

<style>
  .tab-btn {
    @apply text-muted bg-transparent hover:bg-bg-input hover:text-heading;
  }

  .tab-btn[aria-selected="true"] {
    @apply bg-bg-input text-heading shadow-sm;
    font-weight: 500;
  }

  /* Focus styles for better accessibility */
  .tab-btn:focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .tab-btn {
      @apply text-xs px-3 py-2;
    }
    
    .tab-btn span {
      @apply truncate;
    }
  }
</style>