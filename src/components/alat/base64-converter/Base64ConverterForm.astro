---
import { Icon } from 'astro-icon/components';
---

<div id="base64ConverterContent">
  <div class="flex items-center space-x-4 mb-6">
     <span class="text-sm font-medium text-default">Mode:</span>
     <div class="flex space-x-1 bg-bg-muted p-1 rounded-lg">
        <button type="button" id="encodeModeBtn" class="tab-btn flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center active">
            <Icon name="tabler:arrow-narrow-right" class="w-4 h-4 mr-2" />
            Encode
        </button>
        <button type="button" id="decodeModeBtn" class="tab-btn flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200 flex items-center justify-center">
            <Icon name="tabler:arrow-narrow-left" class="w-4 h-4 mr-2" />
            Decode
        </button>
     </div>
  </div>

  <div class="space-y-6">
    <!-- Input Area -->
    <div>
      <label for="inputText" class="block text-sm font-medium text-default mb-2" id="inputLabel">
        Masukkan Teks untuk Encode:
      </label>
      <textarea
        id="inputText"
        rows="8"
        class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-section text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors font-mono"
        placeholder="Masukkan teks di sini..."
      ></textarea>
       <div id="inputErrorMessage" class="mt-2 text-sm text-danger hidden"></div>
    </div>

    <!-- Output Area -->
    <div>
      <label for="outputText" class="block text-sm font-medium text-default mb-2" id="outputLabel">
        Hasil Base64:
      </label>
      <div class="relative">
         <textarea
            id="outputText"
            rows="8"
            readonly
            class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-muted text-default font-mono cursor-not-allowed overflow-x-auto"
            placeholder="Hasil konversi akan muncul di sini."
          ></textarea>
           <button id="copyOutputBtn" class="absolute top-3 right-3 p-2 rounded-md hover:bg-bg-input transition-colors" title="Copy Output">
              <Icon name="tabler:copy" class="w-5 h-5 text-muted" />
            </button>
      </div>
       <p id="copyFeedbackMessage" class="mt-2 text-sm text-success hidden">Hasil berhasil disalin!</p>
    </div>

    <!-- Buttons -->
    <div class="flex justify-end">
        <button type="button" id="clearAllButton"
          class="btn-secondary py-2 px-4 rounded-lg font-semibold flex items-center justify-center"
        >
         <Icon name="tabler:x" class="w-5 h-5 mr-2" />
          Clear All
        </button>
    </div>

  </div>
</div>

<style>
  /* Add specific styles for this form if needed */
   .tab-btn {
    @apply text-muted bg-transparent hover:text-default hover:bg-bg-input;
  }

  .tab-btn.active {
    @apply bg-bg-section text-default shadow-sm;
  }
</style> 