---
export interface Props {
  currentPage: string;
  currentPageUrl?: string;
}

const { currentPage, currentPageUrl } = Astro.props;
---

<section class="bg-bg-muted py-2 border-b border-gray-200">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <a href="/" class="text-muted hover:text-primary transition-colors">
            Home
          </a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          <a href="/alat/" class="text-muted hover:text-primary transition-colors">
            Tools & Utilities
          </a>
        </li>
        <li class="text-gray-400">/</li>
        <li>
          {currentPageUrl ? (
            <a href={currentPageUrl} class="text-heading font-medium">
              {currentPage}
            </a>
          ) : (
            <span class="text-heading font-medium">{currentPage}</span>
          )}
        </li>
      </ol>
    </nav>
  </div>
</section> 