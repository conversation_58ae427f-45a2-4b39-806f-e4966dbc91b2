---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import BackToTop from '~/components/common/BackToTop.astro';
import ToolBreadcrumbs from './ToolBreadcrumbs.astro';

export interface Props {
  metadata: {
    title: string;
    description: string;
    openGraph?: {
      images: Array<{ url: string }>;
    };
  };
  currentPage: string;
  currentPageUrl?: string;
}

const { metadata, currentPage, currentPageUrl } = Astro.props;
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Breadcrumbs -->
  <ToolBreadcrumbs currentPage={currentPage} currentPageUrl={currentPageUrl} />

  <!-- Main Content -->
  <slot />

  <BackToTop />
</Layout> 