# Shared Components for Tools & Utilities

This directory contains reusable components for all tool pages in the `/alat/` section. These components eliminate code duplication and provide consistent UI across all tools.

## ✅ Migration Status: COMPLETED

All tools have been successfully migrated to use the shared components:
- ✅ Base64 Converter
- ✅ Hash Generator  
- ✅ JSON Formatter
- ✅ Kalkulator Uptime
- ✅ Password Generator
- ✅ URL Encoder

Old duplicate components have been removed from individual tool folders.

## Components Overview

### Core Layout Components

#### `ToolLayout.astro`
Main layout wrapper for all tool pages.
- **Props**: `metadata`, `currentPage`, `currentPageUrl`
- **Features**: Includes CustomStyles, ToolBreadcrumbs, and BackToTop

#### `ToolBreadcrumbs.astro`
Consistent breadcrumb navigation.
- **Props**: `currentPage`, `currentPageUrl`
- **Path**: Home > Tools & Utilities > [Current Tool]

#### `ToolHeroSection.astro`
Hero section with title, description, and optional icon.
- **Props**: `title`, `description`, `subtitle`, `icon`
- **Features**: Consistent styling with border accent

### Content Components

#### `ToolContainer.astro`
Wrapper for main content with consistent max-width and padding.
- **Props**: `className` (optional)
- **Features**: Responsive container with proper spacing

#### `ToolInterface.astro`
Consistent wrapper for tool forms with background, border, and optional title.
- **Props**: `title`, `className` (optional)
- **Features**: Provides consistent visual styling for all tool interfaces

#### `ToolPlaceholder.astro`
Placeholder content with icon, title, and description.
- **Props**: `icon`, `title`, `description`, `id`
- **Features**: Centered layout with icon and text

#### `ToolInfoSection.astro`
Information sections with title and content.
- **Props**: `title`, `content` (string or array), `className`
- **Features**: Supports both single paragraph and multiple paragraphs

## Usage Example

```astro
---
import ToolLayout from '~/components/alat/shared/ToolLayout.astro';
import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';
import ToolContainer from '~/components/alat/shared/ToolContainer.astro';
import ToolInterface from '~/components/alat/shared/ToolInterface.astro';
import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';

const metadata = {
  title: "Tool Name",
  description: "Tool description"
};

const infoSections = [
  {
    title: "What is this tool?",
    content: "Description of the tool..."
  },
  {
    title: "How to use",
    content: ["Step 1...", "Step 2..."]
  }
];
---

<ToolLayout 
  metadata={metadata} 
  currentPage="Tool Name"
>
  <ToolHeroSection
    title="Tool Name"
    description="Tool description"
    icon="tabler:tool"
  />
  
  <ToolContainer>
    <ToolInterface title="Tool Interface">
      <!-- Your tool form here -->
    </ToolInterface>
    
    {infoSections.map((section) => (
      <ToolInfoSection
        title={section.title}
        content={section.content}
      />
    ))}
  </ToolContainer>
</ToolLayout>
```

## Benefits

1. **Consistency**: All tools now have identical layout and styling
2. **Maintainability**: Changes to common elements only need to be made once
3. **Efficiency**: New tools can be created quickly using the shared components
4. **DRY Principle**: Eliminated code duplication across all tool pages
5. **Type Safety**: All components use TypeScript interfaces for props

## File Structure After Migration

```
src/components/alat/
├── shared/                    # ✅ Shared components
│   ├── ToolLayout.astro
│   ├── ToolHeroSection.astro
│   ├── ToolBreadcrumbs.astro
│   ├── ToolContainer.astro
│   ├── ToolInterface.astro
│   ├── ToolPlaceholder.astro
│   ├── ToolInfoSection.astro
│   ├── README.md
│   ├── COMPARISON.md
│   └── migration-helper.ts
├── base64-converter/          # ✅ Migrated
│   ├── Base64ConverterForm.astro
│   └── MainContent.astro
├── hash-generator/            # ✅ Migrated
│   ├── HashGeneratorForm.astro
│   └── MainContent.astro
├── json-formatter/            # ✅ Migrated
│   ├── JsonFormatterForm.astro
│   └── MainContent.astro
├── kalkulator-uptime/         # ✅ Migrated
│   ├── DowntimeCalculatorForm.astro
│   ├── ResultsSection.astro
│   ├── TabNavigation.astro
│   ├── UptimeCalculatorForm.astro
│   └── MainContent.astro
├── password-generator/        # ✅ Migrated
│   ├── PasswordGeneratorForm.astro
│   ├── ResultsSection.astro
│   └── MainContent.astro
└── url-encoder/               # ✅ Migrated
    ├── UrlEncoderForm.astro
    └── MainContent.astro
```

## Removed Components

The following duplicate components have been removed:
- ❌ `HeroSection.astro` (from all tool folders)
- ❌ `InfoSections.astro` (from all tool folders)  
- ❌ `PlaceholderContent.astro` (from kalkulator-uptime and password-generator)
- ❌ `MainContent.astro` (from url-encoder, replaced with UrlEncoderForm.astro)

## Next Steps

1. ✅ **Completed**: Migrate all existing tools
2. ✅ **Completed**: Remove duplicate components
3. ✅ **Completed**: Replace old page files with refactored versions
4. **Future**: Use these shared components for any new tools

## Migration Helper

The `migration-helper.ts` file contains utilities to help with future tool migrations:
- Template generation for new tools
- Migration checklist
- Component usage examples 