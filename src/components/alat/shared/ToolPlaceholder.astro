---
import { Icon } from 'astro-icon/components';

export interface Props {
  icon: string;
  title: string;
  description: string;
  id?: string;
}

const { icon, title, description, id = "placeholderContent" } = Astro.props;
---

<div id={id}>
  <div class="py-4 sm:py-6 bg-bg-muted px-4 sm:px-6 rounded-lg">
    <div class="text-center py-12">
      <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-primary-100 dark:bg-primary-900/30 mb-6">
        <Icon name={icon} class="h-12 w-12 text-primary" />
      </div>
      <h3 class="text-lg font-medium text-default mb-2">{title}</h3>
      <p class="text-muted max-w-md mx-auto">
        {description}
      </p>
    </div>
  </div>
</div> 