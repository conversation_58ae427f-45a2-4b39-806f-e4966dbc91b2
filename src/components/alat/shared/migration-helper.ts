/**
 * Migration Helper untuk Alat
 * 
 * Utility ini membantu migrasi alat yang sudah ada ke komponen shared
 */

export interface ToolMetadata {
  title: string;
  description: string;
  openGraph?: {
    images: Array<{ url: string }>;
  };
}

export interface ToolConfig {
  name: string;
  metadata: ToolMetadata;
  hero: {
    title: string;
    description: string;
    subtitle?: string;
  };
  placeholder?: {
    icon: string;
    title: string;
    description: string;
  };
  infoSections?: Array<{
    title: string;
    content: string | string[];
  }>;
}

/**
 * Template untuk halaman alat yang sudah direfactor
 */
export function generateToolPageTemplate(config: ToolConfig): string {
  const { name, metadata, hero, placeholder, infoSections } = config;
  
  const imports = [
    "import ToolLayout from '~/components/alat/shared/ToolLayout.astro';",
    "import ToolHeroSection from '~/components/alat/shared/ToolHeroSection.astro';",
    "import ToolContainer from '~/components/alat/shared/ToolContainer.astro';"
  ];

  if (placeholder) {
    imports.push("import ToolPlaceholder from '~/components/alat/shared/ToolPlaceholder.astro';");
  }

  if (infoSections && infoSections.length > 0) {
    imports.push("import ToolInfoSection from '~/components/alat/shared/ToolInfoSection.astro';");
  }

  imports.push(`import MainContent from '~/components/alat/${name}/MainContent.astro';`);

  const metadataString = JSON.stringify(metadata, null, 2);
  
  const infoSectionsString = infoSections && infoSections.length > 0 
    ? `const infoSections = ${JSON.stringify(infoSections, null, 2)};`
    : '';

  const placeholderComponent = placeholder 
    ? `<ToolPlaceholder 
        icon="${placeholder.icon}"
        title="${placeholder.title}"
        description="${placeholder.description}"
      />`
    : '';

  const infoSectionsComponent = infoSections && infoSections.length > 0
    ? `<div class="mb-12">
        {infoSections.map((section) => (
          <ToolInfoSection 
            title={section.title}
            content={section.content}
            className="py-6 space-y-8 max-w-full lg:max-w-[65ch] mt-20"
          />
        ))}
      </div>`
    : '';

  return `---
${imports.join('\n')}

const metadata = ${metadataString};

${infoSectionsString}
---

<ToolLayout metadata={metadata} currentPage="${hero.title}">
  <!-- Hero Section -->
  <ToolHeroSection 
    title="${hero.title}"
    description="${hero.description}"
    ${hero.subtitle ? `subtitle="${hero.subtitle}"` : ''}
  />

  <!-- Main Content Section -->
  <ToolContainer>
    <!-- Tool Interface -->
    <div class="mb-12">
      <MainContent />
      ${placeholderComponent}
    </div>
    
    ${infoSectionsComponent}
  </ToolContainer>
</ToolLayout>

<style>
  /* Add tool-specific styles here */
</style>

<!-- Tool-specific JavaScript -->
<script is:inline>
  // Add your tool logic here
</script>`;
}

/**
 * Checklist untuk migrasi alat
 */
export const migrationChecklist = [
  "✅ Ganti import Layout dengan ToolLayout",
  "✅ Ganti HeroSection dengan ToolHeroSection", 
  "✅ Ganti container sections dengan ToolContainer",
  "✅ Ganti PlaceholderContent dengan ToolPlaceholder (jika ada)",
  "✅ Ganti InfoSections dengan ToolInfoSection (jika ada)",
  "✅ Hapus breadcrumbs manual (sudah ada di ToolLayout)",
  "✅ Hapus BackToTop manual (sudah ada di ToolLayout)",
  "✅ Hapus CustomStyles manual (sudah ada di ToolLayout)",
  "✅ Pindahkan metadata ke props ToolLayout",
  "✅ Pindahkan info sections ke data array",
  "✅ Test halaman setelah migrasi",
  "✅ Hapus file komponen yang tidak digunakan lagi"
];

/**
 * Contoh konfigurasi untuk kalkulator-uptime
 */
export const uptimeCalculatorConfig: ToolConfig = {
  name: "kalkulator-uptime",
  metadata: {
    title: "Kalkulator Uptime & Downtime - Hitung Waktu Online/Offline Website | Penasihat Hosting",
    description: "Kalkulator uptime dan downtime untuk menghitung waktu online/offline website berdasarkan persentase. Ketahui berapa lama website akan offline atau online dalam setahun, sebulan, seminggu, atau sehari.",
    openGraph: {
      images: [
        {
          url: "https://img.penasihathosting.com/2025/May/kalkulator-uptime.webp",
        }
      ]
    }
  },
  hero: {
    title: "Kalkulator Uptime & Downtime",
    description: "Hitung waktu uptime dan downtime berdasarkan persentase hosting Anda. Ketahui berapa lama website akan online atau offline dalam periode waktu tertentu dengan dua kalkulator yang mudah digunakan.",
    subtitle: "Tool gratis untuk menghitung estimasi uptime dan downtime berdasarkan persentase hosting Anda."
  },
  placeholder: {
    icon: "tabler:calculator",
    title: "Kalkulator Siap Digunakan",
    description: "Masukkan nilai uptime atau downtime untuk melihat perhitungan waktu online/offline website Anda."
  },
  infoSections: [
    {
      title: "Apa Itu Uptime dan Mengapa Penting?",
      content: [
        "Uptime adalah metrik yang mengukur total waktu layanan (seperti website atau server hosting) tersedia dan beroperasi dalam periode waktu tertentu. Biasanya dinyatakan dalam persentase. Angka uptime yang tinggi menunjukkan bahwa layanan tersebut jarang mengalami gangguan atau downtime.",
        "Mengapa uptime penting? Bagi sebagian besar website, terutama yang digunakan untuk bisnis, e-commerce, atau layanan online, ketersediaan yang tinggi adalah krusial. Setiap menit downtime dapat berarti kerugian pendapatan, pengalaman pengguna yang buruk, rusaknya reputasi merek, dan bahkan dampak negatif pada peringkat mesin pencari karena Google dan mesin pencari lainnya mempertimbangkan ketersediaan situs.",
        "Kalkulator ini membantu Anda memvisualisasikan dampak dari persentase uptime tertentu menjadi total waktu downtime yang bisa terjadi dalam setahun, sebulan, seminggu, atau sehari."
      ]
    }
  ]
};

/**
 * Contoh konfigurasi untuk base64-converter
 */
export const base64ConverterConfig: ToolConfig = {
  name: "base64-converter",
  metadata: {
    title: "Base64 Encoder Decoder - Konversi Teks ke Base64 Online | Penasihat Hosting",
    description: "Encoder dan Decoder Base64 online gratis. Konversi teks ke Base64 dan sebaliknya dengan mudah.",
    openGraph: {
      images: [
        {
          url: "https://img.penasihathosting.com/placeholder/base64-converter.webp",
        }
      ]
    }
  },
  hero: {
    title: "Base64 Encoder Decoder",
    description: "Konversi teks biasa menjadi string Base64 atau sebaliknya dengan cepat dan mudah.",
    subtitle: "Tool gratis untuk meng-encode dan men-decode teks ke/dari format Base64."
  },
  infoSections: [
    {
      title: "Apa Itu Base64?",
      content: [
        "Base64 adalah metode encoding biner-ke-teks yang mengubah data biner menjadi format string ASCII. Format ini dirancang untuk membawa data yang, secara tradisional, mungkin tidak stabil di seluruh sistem jaringan, seperti byte non-ASCII dalam email atau XML. Base64 menggunakan set 64 karakter aman untuk web (A-Z, a-z, 0-9, +, /) dan karakter padding (=) untuk memastikan panjang output adalah kelipatan 4.",
        "Penting untuk dicatat bahwa Base64 bukanlah metode enkripsi. Ini tidak memberikan keamanan atau kerahasiaan pada data; ini hanyalah cara untuk merepresentasikan data biner dalam format teks. Siapa pun dapat dengan mudah men-decode string Base64 kembali ke data aslinya."
      ]
    }
  ]
}; 