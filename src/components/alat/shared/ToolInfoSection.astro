---
export interface Props {
  title: string;
  content: string | string[];
  className?: string;
}

const { title, content, className = "" } = Astro.props;

// Handle both string and array content
const contentArray = Array.isArray(content) ? content : [content];
---

<div class={`${className}`}>
  <h3 class="border-t-4 border-neutral-900 pt-4 text-xl font-bold text-heading mb-4">{title}</h3>
  <div class="prose prose-base dark:prose-invert max-w-none">
    {contentArray.map((paragraph) => (
      <p set:html={paragraph}></p>
    ))}
  </div>
</div> 