---
import { Icon } from 'astro-icon/components';
---

<div id="jsonFormatterContent">
  <form id="jsonFormatterForm" class="space-y-6">
    <!-- JSON Input -->
    <div>
      <label for="jsonInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Masukkan JSON di sini:
      </label>
      <textarea
        id="jsonInput"
        rows="10"
        class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-input text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors font-mono"
        placeholder='{ "nama": "contoh", "umur": 30 }'
      ></textarea>
       <div id="jsonErrorMessage" class="mt-2 text-sm text-red-600 dark:text-red-400 hidden"></div>
    </div>

    <button
      type="submit"
      class="btn-primary w-full py-3 px-6 rounded-lg font-semibold flex items-center justify-center"
    >
      <Icon name="tabler:code" class="w-5 h-5 mr-2" />
      Format JSON
    </button>
  </form>

   <!-- JSON Output -->
    <div>
      <label for="jsonOutput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
        Hasil Format:
      </label>
      <textarea
        id="jsonOutput"
        rows="10"
        readonly
        class="w-full px-4 py-3 text-lg border border-border rounded-lg bg-bg-muted text-default focus:ring-2 focus:ring-primary focus:border-primary transition-colors font-mono cursor-not-allowed"
        placeholder="Hasil JSON yang terformat akan muncul di sini."
      ></textarea>
    </div>
</div>

<style>
  /* Add specific styles for textareas if needed */
</style> 