# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

Repository: Penasihat Hosting (Astro 5 + Tailwind CSS)

- Runtime: Node.js >=18.20.8 or ^20.3.0 or >=22.0.0
- Package manager: npm (package-lock.json present)
- Framework: Astro 5 with Tailwind CSS and Preact (signals)
- Code style: ESLint 9 + Prettier 3 (+ prettier-plugin-astro)
- Type checking: astro check + TypeScript config (strictNullChecks enabled)

Common commands

- Install dependencies:
  - npm install

- Develop locally (do not run automatically; only when requested):
  - npm run dev
  - Local URL: http://localhost:4321

- Build (do not run automatically; only when requested):
  - npm run build
  - Output: dist/

- Preview production build locally:
  - npm run preview

- Linting and formatting:
  - Run full checks (Astro type checks + ESLint + Prettier):
    - npm run check
  - Astro type/syntax checks only:
    - npm run check:astro
  - ESLint check:
    - npm run check:eslint
  - Prettier check:
    - npm run check:prettier
  - Auto-fix lint issues:
    - npm run fix or npm run fix:eslint
  - Auto-format with Prettier:
    - npm run fix:prettier

- Tests:
  - No test runner is configured in this repository. There are no npm scripts for tests and no jest/vitest/playwright configs.

Astro/Tailwind specifics

- Tailwind integration via @astrojs/tailwind and @tailwindcss/typography.
- Preact is available for interactive islands via @astrojs/preact and preact/signals.
- SEO/Site features available: @astrolib/seo, @astrojs/rss, @astrojs/sitemap, astro-embed, unpic for images, sharp for image processing.
- Path alias: ~/* resolves to src/* (see tsconfig.json).

High-level architecture and structure

- Content-driven site
  - Markdown/MDX lives under src/content/ and data/post/; pages are routed via Astro in src/pages.
  - Key content domains:
    - Blog: src/pages/[...blog]/ with category and tag routes, posts stored under data/post/ as MDX/MD. Components under src/components/blog/ implement grids, TOC, single post, and layouts.
    - Direktori hosting: src/pages/direktori/ and src/pages/direktori-hosting/ power directory listings, categories, locations, submit flow, and provider details. Source data comes from:
      - Categories: src/content/hosting-categories/*.md
      - Providers: src/content/hosting-providers/*.json
      - Recommendations: src/content/recommendations/** (grouped by scenario)
      - Reusable directory UI in src/components/direktori/** (cards, search, filters, promos)
    - Wiki/Kamus: src/pages/wikihosting/[slug].astro and src/content/wikihosting/*.mdx plus src/content/kamus/*.mdx for glossary-like content and guides.
    - Static/regular pages: e.g., src/pages/compare/index.astro and other index pages.

- Component system (Astro components with composition)
  - Widgets (site-wide UI): src/components/widgets/
    - Header is decomposed into focused subcomponents for maintainability and performance (see src/components/widgets/header/README.md):
      - HeaderNavigation.astro orchestrates dropdowns/mega menus
      - HeaderMegaMenuLayanan.astro and HeaderMegaMenuDirektori.astro implement domain-specific mega menus
      - HeaderDropdown.astro for standard dropdowns
      - HeaderActions.astro for search/RSS/CTAs
      - types.ts centralizes shared interfaces
      - headerDropdown.js encapsulates JS behavior (toggle, outside click, keyboard nav)
  - Tools & utilities (alat): src/components/alat/
    - Shared UI primitives under src/components/alat/shared/ provide a standardized, DRY layout for all tool pages:
      - ToolLayout.astro: page wrapper; wires CustomStyles, ToolBreadcrumbs, BackToTop; takes metadata/currentPage props
      - ToolHeroSection.astro: hero with title/description/icon
      - ToolContainer.astro: responsive container for main content
      - ToolInterface.astro: consistent bordered background wrapper for forms/interactive UIs (optional title)
      - ToolInfoSection.astro: structured informational sections (single or multiple paragraphs)
      - ToolBreadcrumbs.astro: page breadcrumbs (Home > Tools & Utilities > Current Tool)
      - See shared/README.md and shared/COMPARISON.md for rationale and before/after examples
    - Individual tools (e.g., base64-converter, hash-generator, json-formatter, kalkulator-uptime, password-generator, url-encoder) compose these shared primitives for consistent UX

- Theming and styles
  - Tailwind utilities and merge helpers; consistent section backgrounds and spacing are enforced via ToolInterface and related shared components

- Project template lineage
  - Based on AstroWind template (see vendor/README.md); vendor/ is reserved for future template integration/upgrades

- TypeScript and config
  - tsconfig extends astro/tsconfigs/base; strictNullChecks enabled; JS allowed alongside TS; baseUrl is project root; ~ alias used broadly in imports

Operational notes for future agents

- When working on tools under src/components/alat/, prefer composing from shared primitives in src/components/alat/shared/ to maintain visual and behavioral consistency across tools.
- Header-related feature work should preserve the separation of concerns introduced by the refactor (navigation vs. actions vs. dropdowns/mega menus) and update types.ts for shared prop contracts.
- For content work:
  - Blog posts live in data/post/; categories/tags routing handled by src/pages/[...blog]/.
  - Direktori uses content from src/content/hosting-categories and src/content/hosting-providers; keep slugs consistent between content and routing params.
  - Recommendations live in src/content/recommendations/** and are rendered in contextual pages.
- Use npm run check before or after significant changes to catch Astro, ESLint, and Prettier issues in one pass.

Content authoring conventions

- Blog (src/data/post/*.mdx|*.md) — required title; optional publishDate/updateDate/draft/excerpt/image/category/tags/author; optional metadata for SEO (canonical, robots, openGraph, twitter, noticeType/Date, featured).
- Kamus (src/content/kamus/*.mdx|*.md) — title, description, keywords, publishedAt; optional updateDate/tags/author/metadata.
- WikiHosting (src/content/wikihosting/*.mdx|*.md) — title, description, targetKeywords; optional publish/update dates, image, tags, author, difficulty, readingTime, category/phase/priority, schemaType, FAQ, metadata.
- Hosting categories (src/content/hosting-categories/*.md|*.mdx) — title, description, icon; optional providerCount (defaults 0), featured, listTitle, seoTitle, seoDescription, metadata.
- Hosting providers (src/content/hosting-providers/*.json) — required: name, slug, logo, website, description, categories, company details, features, datacenters, pricing, gallery, tier; optional: displayName, longDescription, featuredImage, affiliateLink, isActive/isFeatured flags, created/updated/modified timestamps, badges, controlPanels.

Slug mapping and routing

- Blog
  - Posts: file name under src/data/post becomes the post slug used by blog listing pages in src/pages/[...blog]/.
  - Categories and tags: dynamic routes at src/pages/[...blog]/[category]/[...] and src/pages/[...blog]/[tag]/[...] resolve by matching the category/tag fields in post frontmatter.

- Direktori hosting
  - Categories: the file name in src/content/hosting-categories (e.g., shared-hosting.md → "shared-hosting") maps to /direktori/[category] and its pagination /direktori/[category]/page/[page]. Keep the file name slug in sync with the route param.
  - Providers: the "slug" field in each JSON under src/content/hosting-providers maps to /direktori/hosting/[slug]. Ensure uniqueness and kebab-case.
  - Locations: /direktori/lokasi/[location] derives from provider datacenters (location/country fields). Use consistent kebab-case slugs that match whichever normalization is used in the listing logic.

- Wiki/Kamus
  - WikiHosting: filename in src/content/wikihosting becomes the /wikihosting/[slug] route.
  - Kamus: entries are consumed by components/layouts under src/components/blog and rendered in their respective pages; keep filenames in kebab-case for predictable linking.

